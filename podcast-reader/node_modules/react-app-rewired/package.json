{"name": "react-app-rewired", "version": "2.2.1", "description": "Tweak the create-react-app webpack config(s) without using 'eject' and without creating a fork of the react-scripts", "private": false, "bin": {"react-app-rewired": "./bin/index.js"}, "scripts": {"build": "yarn --cwd 'test/react-app' build", "clean": "rm -rf node_modules", "setup": "yarn deps && yarn link && yarn --cwd 'test/react-app' link 'react-app-rewired'", "start": "yarn --cwd 'test/react-app' start", "teardown": "yarn unlink && yarn clean && yarn --cwd 'test/react-app' clean", "test": "yarn --cwd 'test/react-app' test", "deps": "yarn && yarn --cwd 'test/react-app'"}, "peerDependencies": {"react-scripts": ">=2.1.3"}, "dependencies": {"semver": "^5.6.0"}, "devDependencies": {"react-scripts": "^2.1.3"}, "repository": {"type": "git", "url": "git+https://github.com/timarney/react-app-rewired"}, "license": "MIT", "files": ["/index.js", "/config-overrides.js", "/assets", "/bin", "/config", "/overrides", "/scripts"]}
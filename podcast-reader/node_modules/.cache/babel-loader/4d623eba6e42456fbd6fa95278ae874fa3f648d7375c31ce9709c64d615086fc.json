{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeDetail.js\";\nimport React from 'react';\nimport { formatDate, formatDuration } from '../utils/rssParser';\nimport './EpisodeDetail.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EpisodeDetail = ({\n  episode,\n  onClose,\n  onPlay\n}) => {\n  if (!episode) return null;\n  const handlePlayClick = () => {\n    onPlay(episode);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"episode-detail-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"episode-detail\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"episode-detail-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"episode-detail-title\",\n          children: episode.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-detail-button\",\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"episode-detail-meta\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"episode-detail-date\",\n          children: [\"\\u53D1\\u5E03\\u65F6\\u95F4: \", formatDate(episode.pubDate)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), episode.duration && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"episode-detail-duration\",\n          children: [\"\\u65F6\\u957F: \", formatDuration(episode.duration)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"episode-detail-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"episode-detail-description\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u8282\\u76EE\\u7B80\\u4ECB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: episode.description || '暂无简介'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"episode-detail-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"detail-play-button\",\n            onClick: handlePlayClick,\n            disabled: !episode.audioUrl,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M8 5v14l11-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), \"\\u64AD\\u653E\\u8282\\u76EE\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), episode.link && /*#__PURE__*/_jsxDEV(\"a\", {\n            href: episode.link,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"detail-link-button\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), \"\\u67E5\\u770B\\u539F\\u6587\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = EpisodeDetail;\nexport default EpisodeDetail;\nvar _c;\n$RefreshReg$(_c, \"EpisodeDetail\");", "map": {"version": 3, "names": ["React", "formatDate", "formatDuration", "jsxDEV", "_jsxDEV", "EpisodeDetail", "episode", "onClose", "onPlay", "handlePlayClick", "className", "onClick", "children", "e", "stopPropagation", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "d", "pubDate", "duration", "description", "disabled", "audioUrl", "link", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeDetail.js"], "sourcesContent": ["import React from 'react';\nimport { formatDate, formatDuration } from '../utils/rssParser';\nimport './EpisodeDetail.css';\n\nconst EpisodeDetail = ({ episode, onClose, onPlay }) => {\n  if (!episode) return null;\n\n  const handlePlayClick = () => {\n    onPlay(episode);\n  };\n\n  return (\n    <div className=\"episode-detail-overlay\" onClick={onClose}>\n      <div className=\"episode-detail\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"episode-detail-header\">\n          <h2 className=\"episode-detail-title\">{episode.title}</h2>\n          <button className=\"close-detail-button\" onClick={onClose}>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n            </svg>\n          </button>\n        </div>\n\n        <div className=\"episode-detail-meta\">\n          <span className=\"episode-detail-date\">\n            发布时间: {formatDate(episode.pubDate)}\n          </span>\n          {episode.duration && (\n            <span className=\"episode-detail-duration\">\n              时长: {formatDuration(episode.duration)}\n            </span>\n          )}\n        </div>\n\n        <div className=\"episode-detail-content\">\n          <div className=\"episode-detail-description\">\n            <h3>节目简介</h3>\n            <p>{episode.description || '暂无简介'}</p>\n          </div>\n\n          <div className=\"episode-detail-actions\">\n            <button \n              className=\"detail-play-button\"\n              onClick={handlePlayClick}\n              disabled={!episode.audioUrl}\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n              播放节目\n            </button>\n            \n            {episode.link && (\n              <a \n                href={episode.link} \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"detail-link-button\"\n              >\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z\"/>\n                </svg>\n                查看原文\n              </a>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EpisodeDetail;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,cAAc,QAAQ,oBAAoB;AAC/D,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EACtD,IAAI,CAACF,OAAO,EAAE,OAAO,IAAI;EAEzB,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5BD,MAAM,CAACF,OAAO,CAAC;EACjB,CAAC;EAED,oBACEF,OAAA;IAAKM,SAAS,EAAC,wBAAwB;IAACC,OAAO,EAAEJ,OAAQ;IAAAK,QAAA,eACvDR,OAAA;MAAKM,SAAS,EAAC,gBAAgB;MAACC,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAF,QAAA,gBAClER,OAAA;QAAKM,SAAS,EAAC,uBAAuB;QAAAE,QAAA,gBACpCR,OAAA;UAAIM,SAAS,EAAC,sBAAsB;UAAAE,QAAA,EAAEN,OAAO,CAACS;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzDf,OAAA;UAAQM,SAAS,EAAC,qBAAqB;UAACC,OAAO,EAAEJ,OAAQ;UAAAK,QAAA,eACvDR,OAAA;YAAKgB,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAX,QAAA,eACjER,OAAA;cAAMoB,CAAC,EAAC;YAAuG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENf,OAAA;QAAKM,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAClCR,OAAA;UAAMM,SAAS,EAAC,qBAAqB;UAAAE,QAAA,GAAC,4BAC9B,EAACX,UAAU,CAACK,OAAO,CAACmB,OAAO,CAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACNb,OAAO,CAACoB,QAAQ,iBACftB,OAAA;UAAMM,SAAS,EAAC,yBAAyB;UAAAE,QAAA,GAAC,gBACpC,EAACV,cAAc,CAACI,OAAO,CAACoB,QAAQ,CAAC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENf,OAAA;QAAKM,SAAS,EAAC,wBAAwB;QAAAE,QAAA,gBACrCR,OAAA;UAAKM,SAAS,EAAC,4BAA4B;UAAAE,QAAA,gBACzCR,OAAA;YAAAQ,QAAA,EAAI;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbf,OAAA;YAAAQ,QAAA,EAAIN,OAAO,CAACqB,WAAW,IAAI;UAAM;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAENf,OAAA;UAAKM,SAAS,EAAC,wBAAwB;UAAAE,QAAA,gBACrCR,OAAA;YACEM,SAAS,EAAC,oBAAoB;YAC9BC,OAAO,EAAEF,eAAgB;YACzBmB,QAAQ,EAAE,CAACtB,OAAO,CAACuB,QAAS;YAAAjB,QAAA,gBAE5BR,OAAA;cAAKgB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAX,QAAA,eACjER,OAAA;gBAAMoB,CAAC,EAAC;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,4BAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERb,OAAO,CAACwB,IAAI,iBACX1B,OAAA;YACE2B,IAAI,EAAEzB,OAAO,CAACwB,IAAK;YACnBE,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBvB,SAAS,EAAC,oBAAoB;YAAAE,QAAA,gBAE9BR,OAAA;cAAKgB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAX,QAAA,eACjER,OAAA;gBAAMoB,CAAC,EAAC;cAAoI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3I,CAAC,4BAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACe,EAAA,GAlEI7B,aAAa;AAoEnB,eAAeA,aAAa;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
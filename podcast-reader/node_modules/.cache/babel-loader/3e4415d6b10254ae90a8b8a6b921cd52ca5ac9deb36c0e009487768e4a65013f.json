{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType, WriterState, XMLDOMImplementation, XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction, ref;\n  ref = require('./Utility'), assign = ref.assign, isFunction = ref.isFunction;\n  XMLDOMImplementation = require('./XMLDOMImplementation');\n  XMLDocument = require('./XMLDocument');\n  XMLDocumentCB = require('./XMLDocumentCB');\n  XMLStringWriter = require('./XMLStringWriter');\n  XMLStreamWriter = require('./XMLStreamWriter');\n  NodeType = require('./NodeType');\n  WriterState = require('./WriterState');\n  module.exports.create = function (name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    doc = new XMLDocument(options);\n    root = doc.element(name);\n    if (!options.headless) {\n      doc.declaration(options);\n      if (options.pubID != null || options.sysID != null) {\n        doc.dtd(options);\n      }\n    }\n    return root;\n  };\n  module.exports.begin = function (options, onData, onEnd) {\n    var ref1;\n    if (isFunction(options)) {\n      ref1 = [options, onData], onData = ref1[0], onEnd = ref1[1];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n  module.exports.stringWriter = function (options) {\n    return new XMLStringWriter(options);\n  };\n  module.exports.streamWriter = function (stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n  module.exports.implementation = new XMLDOMImplementation();\n  module.exports.nodeType = NodeType;\n  module.exports.writerState = WriterState;\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "WriterState", "XMLDOMImplementation", "XMLDocument", "XMLDocumentCB", "XMLStreamWriter", "XMLStringWriter", "assign", "isFunction", "ref", "require", "module", "exports", "create", "name", "xmldec", "doctype", "options", "doc", "root", "Error", "element", "headless", "declaration", "pubID", "sysID", "dtd", "begin", "onData", "onEnd", "ref1", "stringWriter", "streamWriter", "stream", "implementation", "nodeType", "writer<PERSON><PERSON>", "call"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xmlbuilder/lib/index.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLDOMImplementation, XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction, ref;\n\n  ref = require('./Utility'), assign = ref.assign, isFunction = ref.isFunction;\n\n  XMLDOMImplementation = require('./XMLDOMImplementation');\n\n  XMLDocument = require('./XMLDocument');\n\n  XMLDocumentCB = require('./XMLDocumentCB');\n\n  XMLStringWriter = require('./XMLStringWriter');\n\n  XMLStreamWriter = require('./XMLStreamWriter');\n\n  NodeType = require('./NodeType');\n\n  WriterState = require('./WriterState');\n\n  module.exports.create = function(name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    doc = new XMLDocument(options);\n    root = doc.element(name);\n    if (!options.headless) {\n      doc.declaration(options);\n      if ((options.pubID != null) || (options.sysID != null)) {\n        doc.dtd(options);\n      }\n    }\n    return root;\n  };\n\n  module.exports.begin = function(options, onData, onEnd) {\n    var ref1;\n    if (isFunction(options)) {\n      ref1 = [options, onData], onData = ref1[0], onEnd = ref1[1];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n\n  module.exports.stringWriter = function(options) {\n    return new XMLStringWriter(options);\n  };\n\n  module.exports.streamWriter = function(stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n\n  module.exports.implementation = new XMLDOMImplementation();\n\n  module.exports.nodeType = NodeType;\n\n  module.exports.writerState = WriterState;\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,EAAEC,MAAM,EAAEC,UAAU,EAAEC,GAAG;EAEtIA,GAAG,GAAGC,OAAO,CAAC,WAAW,CAAC,EAAEH,MAAM,GAAGE,GAAG,CAACF,MAAM,EAAEC,UAAU,GAAGC,GAAG,CAACD,UAAU;EAE5EN,oBAAoB,GAAGQ,OAAO,CAAC,wBAAwB,CAAC;EAExDP,WAAW,GAAGO,OAAO,CAAC,eAAe,CAAC;EAEtCN,aAAa,GAAGM,OAAO,CAAC,iBAAiB,CAAC;EAE1CJ,eAAe,GAAGI,OAAO,CAAC,mBAAmB,CAAC;EAE9CL,eAAe,GAAGK,OAAO,CAAC,mBAAmB,CAAC;EAE9CV,QAAQ,GAAGU,OAAO,CAAC,YAAY,CAAC;EAEhCT,WAAW,GAAGS,OAAO,CAAC,eAAe,CAAC;EAEtCC,MAAM,CAACC,OAAO,CAACC,MAAM,GAAG,UAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAC/D,IAAIC,GAAG,EAAEC,IAAI;IACb,IAAIL,IAAI,IAAI,IAAI,EAAE;MAChB,MAAM,IAAIM,KAAK,CAAC,4BAA4B,CAAC;IAC/C;IACAH,OAAO,GAAGV,MAAM,CAAC,CAAC,CAAC,EAAEQ,MAAM,EAAEC,OAAO,EAAEC,OAAO,CAAC;IAC9CC,GAAG,GAAG,IAAIf,WAAW,CAACc,OAAO,CAAC;IAC9BE,IAAI,GAAGD,GAAG,CAACG,OAAO,CAACP,IAAI,CAAC;IACxB,IAAI,CAACG,OAAO,CAACK,QAAQ,EAAE;MACrBJ,GAAG,CAACK,WAAW,CAACN,OAAO,CAAC;MACxB,IAAKA,OAAO,CAACO,KAAK,IAAI,IAAI,IAAMP,OAAO,CAACQ,KAAK,IAAI,IAAK,EAAE;QACtDP,GAAG,CAACQ,GAAG,CAACT,OAAO,CAAC;MAClB;IACF;IACA,OAAOE,IAAI;EACb,CAAC;EAEDR,MAAM,CAACC,OAAO,CAACe,KAAK,GAAG,UAASV,OAAO,EAAEW,MAAM,EAAEC,KAAK,EAAE;IACtD,IAAIC,IAAI;IACR,IAAItB,UAAU,CAACS,OAAO,CAAC,EAAE;MACvBa,IAAI,GAAG,CAACb,OAAO,EAAEW,MAAM,CAAC,EAAEA,MAAM,GAAGE,IAAI,CAAC,CAAC,CAAC,EAAED,KAAK,GAAGC,IAAI,CAAC,CAAC,CAAC;MAC3Db,OAAO,GAAG,CAAC,CAAC;IACd;IACA,IAAIW,MAAM,EAAE;MACV,OAAO,IAAIxB,aAAa,CAACa,OAAO,EAAEW,MAAM,EAAEC,KAAK,CAAC;IAClD,CAAC,MAAM;MACL,OAAO,IAAI1B,WAAW,CAACc,OAAO,CAAC;IACjC;EACF,CAAC;EAEDN,MAAM,CAACC,OAAO,CAACmB,YAAY,GAAG,UAASd,OAAO,EAAE;IAC9C,OAAO,IAAIX,eAAe,CAACW,OAAO,CAAC;EACrC,CAAC;EAEDN,MAAM,CAACC,OAAO,CAACoB,YAAY,GAAG,UAASC,MAAM,EAAEhB,OAAO,EAAE;IACtD,OAAO,IAAIZ,eAAe,CAAC4B,MAAM,EAAEhB,OAAO,CAAC;EAC7C,CAAC;EAEDN,MAAM,CAACC,OAAO,CAACsB,cAAc,GAAG,IAAIhC,oBAAoB,CAAC,CAAC;EAE1DS,MAAM,CAACC,OAAO,CAACuB,QAAQ,GAAGnC,QAAQ;EAElCW,MAAM,CAACC,OAAO,CAACwB,WAAW,GAAGnC,WAAW;AAE1C,CAAC,EAAEoC,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
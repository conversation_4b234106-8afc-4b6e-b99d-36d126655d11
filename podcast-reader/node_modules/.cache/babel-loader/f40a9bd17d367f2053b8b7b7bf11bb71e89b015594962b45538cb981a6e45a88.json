{"ast": null, "code": "module.exports = require('events').EventEmitter;", "map": {"version": 3, "names": ["module", "exports", "require", "EventEmitter"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/readable-stream/lib/internal/streams/stream-browser.js"], "sourcesContent": ["module.exports = require('events').EventEmitter;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
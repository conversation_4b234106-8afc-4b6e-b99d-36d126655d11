{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport PodcastHeader from './components/PodcastHeader';\nimport SearchBar from './components/SearchBar';\nimport EpisodeList from './components/EpisodeList';\nimport EpisodeDetail from './components/EpisodeDetail';\nimport AudioPlayer from './components/AudioPlayer';\nimport { parseRSSFeed } from './utils/rssParser';\nimport { mockPodcastData } from './data/mockData';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [podcastData, setPodcastData] = useState(mockPodcastData);\n  const [filteredEpisodes, setFilteredEpisodes] = useState(mockPodcastData.episodes);\n  const [currentEpisode, setCurrentEpisode] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // 搜索功能\n  const handleSearch = searchTerm => {\n    if (!searchTerm.trim()) {\n      setFilteredEpisodes(podcastData.episodes);\n      return;\n    }\n    const filtered = podcastData.episodes.filter(episode => episode.title.toLowerCase().includes(searchTerm.toLowerCase()) || episode.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    setFilteredEpisodes(filtered);\n  };\n\n  // 加载RSS源\n  const handleRSSUrlSubmit = async rssUrl => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const data = await parseRSSFeed(rssUrl);\n      setPodcastData(data);\n      setFilteredEpisodes(data.episodes);\n      setCurrentEpisode(null);\n    } catch (err) {\n      console.error('Failed to load RSS feed:', err);\n      setError('无法加载RSS源，请检查URL是否正确或网络连接。正在显示示例数据。');\n      // 如果加载失败，保持使用模拟数据\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 选择节目\n  const handleEpisodeSelect = episode => {\n    setCurrentEpisode(episode);\n  };\n\n  // 关闭播放器\n  const handleClosePlayer = () => {\n    setCurrentEpisode(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(PodcastHeader, {\n      podcastInfo: podcastData.podcastInfo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SearchBar, {\n      onSearch: handleSearch,\n      onRSSUrlSubmit: handleRSSUrlSubmit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7DRSS\\u6E90...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(EpisodeList, {\n      episodes: filteredEpisodes,\n      onEpisodeSelect: handleEpisodeSelect,\n      currentEpisode: currentEpisode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 9\n    }, this), currentEpisode && /*#__PURE__*/_jsxDEV(AudioPlayer, {\n      episode: currentEpisode,\n      onClose: handleClosePlayer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"lKZ9xbfHBSLruWEZA6adGlV/t6M=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "PodcastHeader", "SearchBar", "EpisodeList", "EpisodeDetail", "AudioPlayer", "parseRSSFeed", "mockPodcastData", "jsxDEV", "_jsxDEV", "App", "_s", "podcastData", "setPodcastData", "filteredEpisodes", "setFilteredEpisodes", "episodes", "currentEpisode", "setCurrentEpisode", "isLoading", "setIsLoading", "error", "setError", "handleSearch", "searchTerm", "trim", "filtered", "filter", "episode", "title", "toLowerCase", "includes", "description", "handleRSSUrlSubmit", "rssUrl", "data", "err", "console", "handleEpisodeSelect", "handleClosePlayer", "className", "children", "podcastInfo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSearch", "onRSSUrlSubmit", "onEpisodeSelect", "onClose", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport PodcastHeader from './components/PodcastHeader';\nimport SearchBar from './components/SearchBar';\nimport EpisodeList from './components/EpisodeList';\nimport EpisodeDetail from './components/EpisodeDetail';\nimport AudioPlayer from './components/AudioPlayer';\nimport { parseRSSFeed } from './utils/rssParser';\nimport { mockPodcastData } from './data/mockData';\nimport './App.css';\n\nfunction App() {\n  const [podcastData, setPodcastData] = useState(mockPodcastData);\n  const [filteredEpisodes, setFilteredEpisodes] = useState(mockPodcastData.episodes);\n  const [currentEpisode, setCurrentEpisode] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // 搜索功能\n  const handleSearch = (searchTerm) => {\n    if (!searchTerm.trim()) {\n      setFilteredEpisodes(podcastData.episodes);\n      return;\n    }\n\n    const filtered = podcastData.episodes.filter(episode =>\n      episode.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      episode.description.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n    setFilteredEpisodes(filtered);\n  };\n\n  // 加载RSS源\n  const handleRSSUrlSubmit = async (rssUrl) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const data = await parseRSSFeed(rssUrl);\n      setPodcastData(data);\n      setFilteredEpisodes(data.episodes);\n      setCurrentEpisode(null);\n    } catch (err) {\n      console.error('Failed to load RSS feed:', err);\n      setError('无法加载RSS源，请检查URL是否正确或网络连接。正在显示示例数据。');\n      // 如果加载失败，保持使用模拟数据\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 选择节目\n  const handleEpisodeSelect = (episode) => {\n    setCurrentEpisode(episode);\n  };\n\n  // 关闭播放器\n  const handleClosePlayer = () => {\n    setCurrentEpisode(null);\n  };\n\n  return (\n    <div className=\"App\">\n      <PodcastHeader podcastInfo={podcastData.podcastInfo} />\n\n      <SearchBar\n        onSearch={handleSearch}\n        onRSSUrlSubmit={handleRSSUrlSubmit}\n      />\n\n      {error && (\n        <div className=\"error-message\">\n          <p>{error}</p>\n        </div>\n      )}\n\n      {isLoading ? (\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>正在加载RSS源...</p>\n        </div>\n      ) : (\n        <EpisodeList\n          episodes={filteredEpisodes}\n          onEpisodeSelect={handleEpisodeSelect}\n          currentEpisode={currentEpisode}\n        />\n      )}\n\n      {currentEpisode && (\n        <AudioPlayer\n          episode={currentEpisode}\n          onClose={handleClosePlayer}\n        />\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,eAAe,QAAQ,iBAAiB;AACjD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAACO,eAAe,CAAC;EAC/D,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAACO,eAAe,CAACS,QAAQ,CAAC;EAClF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMuB,YAAY,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE;MACtBV,mBAAmB,CAACH,WAAW,CAACI,QAAQ,CAAC;MACzC;IACF;IAEA,MAAMU,QAAQ,GAAGd,WAAW,CAACI,QAAQ,CAACW,MAAM,CAACC,OAAO,IAClDA,OAAO,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,UAAU,CAACM,WAAW,CAAC,CAAC,CAAC,IAC9DF,OAAO,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,UAAU,CAACM,WAAW,CAAC,CAAC,CACrE,CAAC;IACDf,mBAAmB,CAACW,QAAQ,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3Cd,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMa,IAAI,GAAG,MAAM7B,YAAY,CAAC4B,MAAM,CAAC;MACvCrB,cAAc,CAACsB,IAAI,CAAC;MACpBpB,mBAAmB,CAACoB,IAAI,CAACnB,QAAQ,CAAC;MAClCE,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAAC,0BAA0B,EAAEe,GAAG,CAAC;MAC9Cd,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMkB,mBAAmB,GAAIV,OAAO,IAAK;IACvCV,iBAAiB,CAACU,OAAO,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,oBACET,OAAA;IAAK+B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBhC,OAAA,CAACR,aAAa;MAACyC,WAAW,EAAE9B,WAAW,CAAC8B;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEvDrC,OAAA,CAACP,SAAS;MACR6C,QAAQ,EAAExB,YAAa;MACvByB,cAAc,EAAEf;IAAmB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,EAEDzB,KAAK,iBACJZ,OAAA;MAAK+B,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BhC,OAAA;QAAAgC,QAAA,EAAIpB;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACN,EAEA3B,SAAS,gBACRV,OAAA;MAAK+B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChC,OAAA;QAAK+B,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCrC,OAAA;QAAAgC,QAAA,EAAG;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,gBAENrC,OAAA,CAACN,WAAW;MACVa,QAAQ,EAAEF,gBAAiB;MAC3BmC,eAAe,EAAEX,mBAAoB;MACrCrB,cAAc,EAAEA;IAAe;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF,EAEA7B,cAAc,iBACbR,OAAA,CAACJ,WAAW;MACVuB,OAAO,EAAEX,cAAe;MACxBiC,OAAO,EAAEX;IAAkB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACnC,EAAA,CAtFQD,GAAG;AAAAyC,EAAA,GAAHzC,GAAG;AAwFZ,eAAeA,GAAG;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
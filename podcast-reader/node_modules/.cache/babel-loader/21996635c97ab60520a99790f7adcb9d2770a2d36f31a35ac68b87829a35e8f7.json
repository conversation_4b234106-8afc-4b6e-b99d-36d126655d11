{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  module.exports = {\n    None: 0,\n    OpenTag: 1,\n    InsideTag: 2,\n    CloseTag: 3\n  };\n}).call(this);", "map": {"version": 3, "names": ["module", "exports", "None", "OpenTag", "InsideTag", "CloseTag", "call"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xmlbuilder/lib/WriterState.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    None: 0,\n    OpenTag: 1,\n    InsideTag: 2,\n    CloseTag: 3\n  };\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACVA,MAAM,CAACC,OAAO,GAAG;IACfC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE;EACZ,CAAC;AAEH,CAAC,EAAEC,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
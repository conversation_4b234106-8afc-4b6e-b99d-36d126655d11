{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  \"use strict\";\n\n  var bom,\n    defaults,\n    defineProperty,\n    events,\n    isEmpty,\n    processItem,\n    processors,\n    sax,\n    setImmediate,\n    bind = function (fn, me) {\n      return function () {\n        return fn.apply(me, arguments);\n      };\n    },\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  sax = require('sax');\n  events = require('events');\n  bom = require('./bom');\n  processors = require('./processors');\n  setImmediate = require('timers').setImmediate;\n  defaults = require('./defaults').defaults;\n  isEmpty = function (thing) {\n    return typeof thing === \"object\" && thing != null && Object.keys(thing).length === 0;\n  };\n  processItem = function (processors, item, key) {\n    var i, len, process;\n    for (i = 0, len = processors.length; i < len; i++) {\n      process = processors[i];\n      item = process(item, key);\n    }\n    return item;\n  };\n  defineProperty = function (obj, key, value) {\n    var descriptor;\n    descriptor = Object.create(null);\n    descriptor.value = value;\n    descriptor.writable = true;\n    descriptor.enumerable = true;\n    descriptor.configurable = true;\n    return Object.defineProperty(obj, key, descriptor);\n  };\n  exports.Parser = function (superClass) {\n    extend(Parser, superClass);\n    function Parser(opts) {\n      this.parseStringPromise = bind(this.parseStringPromise, this);\n      this.parseString = bind(this.parseString, this);\n      this.reset = bind(this.reset, this);\n      this.assignOrPush = bind(this.assignOrPush, this);\n      this.processAsync = bind(this.processAsync, this);\n      var key, ref, value;\n      if (!(this instanceof exports.Parser)) {\n        return new exports.Parser(opts);\n      }\n      this.options = {};\n      ref = defaults[\"0.2\"];\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this.options[key] = value;\n      }\n      for (key in opts) {\n        if (!hasProp.call(opts, key)) continue;\n        value = opts[key];\n        this.options[key] = value;\n      }\n      if (this.options.xmlns) {\n        this.options.xmlnskey = this.options.attrkey + \"ns\";\n      }\n      if (this.options.normalizeTags) {\n        if (!this.options.tagNameProcessors) {\n          this.options.tagNameProcessors = [];\n        }\n        this.options.tagNameProcessors.unshift(processors.normalize);\n      }\n      this.reset();\n    }\n    Parser.prototype.processAsync = function () {\n      var chunk, err;\n      try {\n        if (this.remaining.length <= this.options.chunkSize) {\n          chunk = this.remaining;\n          this.remaining = '';\n          this.saxParser = this.saxParser.write(chunk);\n          return this.saxParser.close();\n        } else {\n          chunk = this.remaining.substr(0, this.options.chunkSize);\n          this.remaining = this.remaining.substr(this.options.chunkSize, this.remaining.length);\n          this.saxParser = this.saxParser.write(chunk);\n          return setImmediate(this.processAsync);\n        }\n      } catch (error1) {\n        err = error1;\n        if (!this.saxParser.errThrown) {\n          this.saxParser.errThrown = true;\n          return this.emit(err);\n        }\n      }\n    };\n    Parser.prototype.assignOrPush = function (obj, key, newValue) {\n      if (!(key in obj)) {\n        if (!this.options.explicitArray) {\n          return defineProperty(obj, key, newValue);\n        } else {\n          return defineProperty(obj, key, [newValue]);\n        }\n      } else {\n        if (!(obj[key] instanceof Array)) {\n          defineProperty(obj, key, [obj[key]]);\n        }\n        return obj[key].push(newValue);\n      }\n    };\n    Parser.prototype.reset = function () {\n      var attrkey, charkey, ontext, stack;\n      this.removeAllListeners();\n      this.saxParser = sax.parser(this.options.strict, {\n        trim: false,\n        normalize: false,\n        xmlns: this.options.xmlns\n      });\n      this.saxParser.errThrown = false;\n      this.saxParser.onerror = function (_this) {\n        return function (error) {\n          _this.saxParser.resume();\n          if (!_this.saxParser.errThrown) {\n            _this.saxParser.errThrown = true;\n            return _this.emit(\"error\", error);\n          }\n        };\n      }(this);\n      this.saxParser.onend = function (_this) {\n        return function () {\n          if (!_this.saxParser.ended) {\n            _this.saxParser.ended = true;\n            return _this.emit(\"end\", _this.resultObject);\n          }\n        };\n      }(this);\n      this.saxParser.ended = false;\n      this.EXPLICIT_CHARKEY = this.options.explicitCharkey;\n      this.resultObject = null;\n      stack = [];\n      attrkey = this.options.attrkey;\n      charkey = this.options.charkey;\n      this.saxParser.onopentag = function (_this) {\n        return function (node) {\n          var key, newValue, obj, processedKey, ref;\n          obj = {};\n          obj[charkey] = \"\";\n          if (!_this.options.ignoreAttrs) {\n            ref = node.attributes;\n            for (key in ref) {\n              if (!hasProp.call(ref, key)) continue;\n              if (!(attrkey in obj) && !_this.options.mergeAttrs) {\n                obj[attrkey] = {};\n              }\n              newValue = _this.options.attrValueProcessors ? processItem(_this.options.attrValueProcessors, node.attributes[key], key) : node.attributes[key];\n              processedKey = _this.options.attrNameProcessors ? processItem(_this.options.attrNameProcessors, key) : key;\n              if (_this.options.mergeAttrs) {\n                _this.assignOrPush(obj, processedKey, newValue);\n              } else {\n                defineProperty(obj[attrkey], processedKey, newValue);\n              }\n            }\n          }\n          obj[\"#name\"] = _this.options.tagNameProcessors ? processItem(_this.options.tagNameProcessors, node.name) : node.name;\n          if (_this.options.xmlns) {\n            obj[_this.options.xmlnskey] = {\n              uri: node.uri,\n              local: node.local\n            };\n          }\n          return stack.push(obj);\n        };\n      }(this);\n      this.saxParser.onclosetag = function (_this) {\n        return function () {\n          var cdata, emptyStr, key, node, nodeName, obj, objClone, old, s, xpath;\n          obj = stack.pop();\n          nodeName = obj[\"#name\"];\n          if (!_this.options.explicitChildren || !_this.options.preserveChildrenOrder) {\n            delete obj[\"#name\"];\n          }\n          if (obj.cdata === true) {\n            cdata = obj.cdata;\n            delete obj.cdata;\n          }\n          s = stack[stack.length - 1];\n          if (obj[charkey].match(/^\\s*$/) && !cdata) {\n            emptyStr = obj[charkey];\n            delete obj[charkey];\n          } else {\n            if (_this.options.trim) {\n              obj[charkey] = obj[charkey].trim();\n            }\n            if (_this.options.normalize) {\n              obj[charkey] = obj[charkey].replace(/\\s{2,}/g, \" \").trim();\n            }\n            obj[charkey] = _this.options.valueProcessors ? processItem(_this.options.valueProcessors, obj[charkey], nodeName) : obj[charkey];\n            if (Object.keys(obj).length === 1 && charkey in obj && !_this.EXPLICIT_CHARKEY) {\n              obj = obj[charkey];\n            }\n          }\n          if (isEmpty(obj)) {\n            if (typeof _this.options.emptyTag === 'function') {\n              obj = _this.options.emptyTag();\n            } else {\n              obj = _this.options.emptyTag !== '' ? _this.options.emptyTag : emptyStr;\n            }\n          }\n          if (_this.options.validator != null) {\n            xpath = \"/\" + function () {\n              var i, len, results;\n              results = [];\n              for (i = 0, len = stack.length; i < len; i++) {\n                node = stack[i];\n                results.push(node[\"#name\"]);\n              }\n              return results;\n            }().concat(nodeName).join(\"/\");\n            (function () {\n              var err;\n              try {\n                return obj = _this.options.validator(xpath, s && s[nodeName], obj);\n              } catch (error1) {\n                err = error1;\n                return _this.emit(\"error\", err);\n              }\n            })();\n          }\n          if (_this.options.explicitChildren && !_this.options.mergeAttrs && typeof obj === 'object') {\n            if (!_this.options.preserveChildrenOrder) {\n              node = {};\n              if (_this.options.attrkey in obj) {\n                node[_this.options.attrkey] = obj[_this.options.attrkey];\n                delete obj[_this.options.attrkey];\n              }\n              if (!_this.options.charsAsChildren && _this.options.charkey in obj) {\n                node[_this.options.charkey] = obj[_this.options.charkey];\n                delete obj[_this.options.charkey];\n              }\n              if (Object.getOwnPropertyNames(obj).length > 0) {\n                node[_this.options.childkey] = obj;\n              }\n              obj = node;\n            } else if (s) {\n              s[_this.options.childkey] = s[_this.options.childkey] || [];\n              objClone = {};\n              for (key in obj) {\n                if (!hasProp.call(obj, key)) continue;\n                defineProperty(objClone, key, obj[key]);\n              }\n              s[_this.options.childkey].push(objClone);\n              delete obj[\"#name\"];\n              if (Object.keys(obj).length === 1 && charkey in obj && !_this.EXPLICIT_CHARKEY) {\n                obj = obj[charkey];\n              }\n            }\n          }\n          if (stack.length > 0) {\n            return _this.assignOrPush(s, nodeName, obj);\n          } else {\n            if (_this.options.explicitRoot) {\n              old = obj;\n              obj = {};\n              defineProperty(obj, nodeName, old);\n            }\n            _this.resultObject = obj;\n            _this.saxParser.ended = true;\n            return _this.emit(\"end\", _this.resultObject);\n          }\n        };\n      }(this);\n      ontext = function (_this) {\n        return function (text) {\n          var charChild, s;\n          s = stack[stack.length - 1];\n          if (s) {\n            s[charkey] += text;\n            if (_this.options.explicitChildren && _this.options.preserveChildrenOrder && _this.options.charsAsChildren && (_this.options.includeWhiteChars || text.replace(/\\\\n/g, '').trim() !== '')) {\n              s[_this.options.childkey] = s[_this.options.childkey] || [];\n              charChild = {\n                '#name': '__text__'\n              };\n              charChild[charkey] = text;\n              if (_this.options.normalize) {\n                charChild[charkey] = charChild[charkey].replace(/\\s{2,}/g, \" \").trim();\n              }\n              s[_this.options.childkey].push(charChild);\n            }\n            return s;\n          }\n        };\n      }(this);\n      this.saxParser.ontext = ontext;\n      return this.saxParser.oncdata = function (_this) {\n        return function (text) {\n          var s;\n          s = ontext(text);\n          if (s) {\n            return s.cdata = true;\n          }\n        };\n      }(this);\n    };\n    Parser.prototype.parseString = function (str, cb) {\n      var err;\n      if (cb != null && typeof cb === \"function\") {\n        this.on(\"end\", function (result) {\n          this.reset();\n          return cb(null, result);\n        });\n        this.on(\"error\", function (err) {\n          this.reset();\n          return cb(err);\n        });\n      }\n      try {\n        str = str.toString();\n        if (str.trim() === '') {\n          this.emit(\"end\", null);\n          return true;\n        }\n        str = bom.stripBOM(str);\n        if (this.options.async) {\n          this.remaining = str;\n          setImmediate(this.processAsync);\n          return this.saxParser;\n        }\n        return this.saxParser.write(str).close();\n      } catch (error1) {\n        err = error1;\n        if (!(this.saxParser.errThrown || this.saxParser.ended)) {\n          this.emit('error', err);\n          return this.saxParser.errThrown = true;\n        } else if (this.saxParser.ended) {\n          throw err;\n        }\n      }\n    };\n    Parser.prototype.parseStringPromise = function (str) {\n      return new Promise(function (_this) {\n        return function (resolve, reject) {\n          return _this.parseString(str, function (err, value) {\n            if (err) {\n              return reject(err);\n            } else {\n              return resolve(value);\n            }\n          });\n        };\n      }(this));\n    };\n    return Parser;\n  }(events);\n  exports.parseString = function (str, a, b) {\n    var cb, options, parser;\n    if (b != null) {\n      if (typeof b === 'function') {\n        cb = b;\n      }\n      if (typeof a === 'object') {\n        options = a;\n      }\n    } else {\n      if (typeof a === 'function') {\n        cb = a;\n      }\n      options = {};\n    }\n    parser = new exports.Parser(options);\n    return parser.parseString(str, cb);\n  };\n  exports.parseStringPromise = function (str, a) {\n    var options, parser;\n    if (typeof a === 'object') {\n      options = a;\n    }\n    parser = new exports.Parser(options);\n    return parser.parseStringPromise(str);\n  };\n}).call(this);", "map": {"version": 3, "names": ["bom", "defaults", "defineProperty", "events", "isEmpty", "processItem", "processors", "sax", "setImmediate", "bind", "fn", "me", "apply", "arguments", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "thing", "Object", "keys", "length", "item", "i", "len", "process", "obj", "value", "descriptor", "create", "writable", "enumerable", "configurable", "exports", "<PERSON><PERSON><PERSON>", "superClass", "opts", "parseStringPromise", "parseString", "reset", "assignOrPush", "processAsync", "ref", "options", "xmlns", "xmlnskey", "attrkey", "normalizeTags", "tagNameProcessors", "unshift", "normalize", "chunk", "err", "remaining", "chunkSize", "sax<PERSON><PERSON><PERSON>", "write", "close", "substr", "error1", "errThrown", "emit", "newValue", "explicitArray", "Array", "push", "charkey", "ontext", "stack", "removeAllListeners", "parser", "strict", "trim", "onerror", "_this", "error", "resume", "onend", "ended", "resultObject", "EXPLICIT_CHARKEY", "<PERSON><PERSON><PERSON><PERSON>", "onopentag", "node", "<PERSON><PERSON>ey", "ignoreAttrs", "attributes", "mergeAttrs", "attrValueProcessors", "attrNameProcessors", "name", "uri", "local", "onclosetag", "cdata", "emptyStr", "nodeName", "obj<PERSON><PERSON>", "old", "s", "xpath", "pop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preserveChildrenOrder", "match", "replace", "valueProcessors", "emptyTag", "validator", "results", "concat", "join", "chars<PERSON><PERSON><PERSON><PERSON><PERSON>", "getOwnPropertyNames", "childkey", "explicitRoot", "text", "<PERSON>ar<PERSON><PERSON><PERSON>", "includeWhiteChars", "oncdata", "str", "cb", "on", "result", "toString", "stripBOM", "async", "Promise", "resolve", "reject", "a", "b"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xml2js/lib/parser.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  \"use strict\";\n  var bom, defaults, defineProperty, events, isEmpty, processItem, processors, sax, setImmediate,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; },\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  sax = require('sax');\n\n  events = require('events');\n\n  bom = require('./bom');\n\n  processors = require('./processors');\n\n  setImmediate = require('timers').setImmediate;\n\n  defaults = require('./defaults').defaults;\n\n  isEmpty = function(thing) {\n    return typeof thing === \"object\" && (thing != null) && Object.keys(thing).length === 0;\n  };\n\n  processItem = function(processors, item, key) {\n    var i, len, process;\n    for (i = 0, len = processors.length; i < len; i++) {\n      process = processors[i];\n      item = process(item, key);\n    }\n    return item;\n  };\n\n  defineProperty = function(obj, key, value) {\n    var descriptor;\n    descriptor = Object.create(null);\n    descriptor.value = value;\n    descriptor.writable = true;\n    descriptor.enumerable = true;\n    descriptor.configurable = true;\n    return Object.defineProperty(obj, key, descriptor);\n  };\n\n  exports.Parser = (function(superClass) {\n    extend(Parser, superClass);\n\n    function Parser(opts) {\n      this.parseStringPromise = bind(this.parseStringPromise, this);\n      this.parseString = bind(this.parseString, this);\n      this.reset = bind(this.reset, this);\n      this.assignOrPush = bind(this.assignOrPush, this);\n      this.processAsync = bind(this.processAsync, this);\n      var key, ref, value;\n      if (!(this instanceof exports.Parser)) {\n        return new exports.Parser(opts);\n      }\n      this.options = {};\n      ref = defaults[\"0.2\"];\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this.options[key] = value;\n      }\n      for (key in opts) {\n        if (!hasProp.call(opts, key)) continue;\n        value = opts[key];\n        this.options[key] = value;\n      }\n      if (this.options.xmlns) {\n        this.options.xmlnskey = this.options.attrkey + \"ns\";\n      }\n      if (this.options.normalizeTags) {\n        if (!this.options.tagNameProcessors) {\n          this.options.tagNameProcessors = [];\n        }\n        this.options.tagNameProcessors.unshift(processors.normalize);\n      }\n      this.reset();\n    }\n\n    Parser.prototype.processAsync = function() {\n      var chunk, err;\n      try {\n        if (this.remaining.length <= this.options.chunkSize) {\n          chunk = this.remaining;\n          this.remaining = '';\n          this.saxParser = this.saxParser.write(chunk);\n          return this.saxParser.close();\n        } else {\n          chunk = this.remaining.substr(0, this.options.chunkSize);\n          this.remaining = this.remaining.substr(this.options.chunkSize, this.remaining.length);\n          this.saxParser = this.saxParser.write(chunk);\n          return setImmediate(this.processAsync);\n        }\n      } catch (error1) {\n        err = error1;\n        if (!this.saxParser.errThrown) {\n          this.saxParser.errThrown = true;\n          return this.emit(err);\n        }\n      }\n    };\n\n    Parser.prototype.assignOrPush = function(obj, key, newValue) {\n      if (!(key in obj)) {\n        if (!this.options.explicitArray) {\n          return defineProperty(obj, key, newValue);\n        } else {\n          return defineProperty(obj, key, [newValue]);\n        }\n      } else {\n        if (!(obj[key] instanceof Array)) {\n          defineProperty(obj, key, [obj[key]]);\n        }\n        return obj[key].push(newValue);\n      }\n    };\n\n    Parser.prototype.reset = function() {\n      var attrkey, charkey, ontext, stack;\n      this.removeAllListeners();\n      this.saxParser = sax.parser(this.options.strict, {\n        trim: false,\n        normalize: false,\n        xmlns: this.options.xmlns\n      });\n      this.saxParser.errThrown = false;\n      this.saxParser.onerror = (function(_this) {\n        return function(error) {\n          _this.saxParser.resume();\n          if (!_this.saxParser.errThrown) {\n            _this.saxParser.errThrown = true;\n            return _this.emit(\"error\", error);\n          }\n        };\n      })(this);\n      this.saxParser.onend = (function(_this) {\n        return function() {\n          if (!_this.saxParser.ended) {\n            _this.saxParser.ended = true;\n            return _this.emit(\"end\", _this.resultObject);\n          }\n        };\n      })(this);\n      this.saxParser.ended = false;\n      this.EXPLICIT_CHARKEY = this.options.explicitCharkey;\n      this.resultObject = null;\n      stack = [];\n      attrkey = this.options.attrkey;\n      charkey = this.options.charkey;\n      this.saxParser.onopentag = (function(_this) {\n        return function(node) {\n          var key, newValue, obj, processedKey, ref;\n          obj = {};\n          obj[charkey] = \"\";\n          if (!_this.options.ignoreAttrs) {\n            ref = node.attributes;\n            for (key in ref) {\n              if (!hasProp.call(ref, key)) continue;\n              if (!(attrkey in obj) && !_this.options.mergeAttrs) {\n                obj[attrkey] = {};\n              }\n              newValue = _this.options.attrValueProcessors ? processItem(_this.options.attrValueProcessors, node.attributes[key], key) : node.attributes[key];\n              processedKey = _this.options.attrNameProcessors ? processItem(_this.options.attrNameProcessors, key) : key;\n              if (_this.options.mergeAttrs) {\n                _this.assignOrPush(obj, processedKey, newValue);\n              } else {\n                defineProperty(obj[attrkey], processedKey, newValue);\n              }\n            }\n          }\n          obj[\"#name\"] = _this.options.tagNameProcessors ? processItem(_this.options.tagNameProcessors, node.name) : node.name;\n          if (_this.options.xmlns) {\n            obj[_this.options.xmlnskey] = {\n              uri: node.uri,\n              local: node.local\n            };\n          }\n          return stack.push(obj);\n        };\n      })(this);\n      this.saxParser.onclosetag = (function(_this) {\n        return function() {\n          var cdata, emptyStr, key, node, nodeName, obj, objClone, old, s, xpath;\n          obj = stack.pop();\n          nodeName = obj[\"#name\"];\n          if (!_this.options.explicitChildren || !_this.options.preserveChildrenOrder) {\n            delete obj[\"#name\"];\n          }\n          if (obj.cdata === true) {\n            cdata = obj.cdata;\n            delete obj.cdata;\n          }\n          s = stack[stack.length - 1];\n          if (obj[charkey].match(/^\\s*$/) && !cdata) {\n            emptyStr = obj[charkey];\n            delete obj[charkey];\n          } else {\n            if (_this.options.trim) {\n              obj[charkey] = obj[charkey].trim();\n            }\n            if (_this.options.normalize) {\n              obj[charkey] = obj[charkey].replace(/\\s{2,}/g, \" \").trim();\n            }\n            obj[charkey] = _this.options.valueProcessors ? processItem(_this.options.valueProcessors, obj[charkey], nodeName) : obj[charkey];\n            if (Object.keys(obj).length === 1 && charkey in obj && !_this.EXPLICIT_CHARKEY) {\n              obj = obj[charkey];\n            }\n          }\n          if (isEmpty(obj)) {\n            if (typeof _this.options.emptyTag === 'function') {\n              obj = _this.options.emptyTag();\n            } else {\n              obj = _this.options.emptyTag !== '' ? _this.options.emptyTag : emptyStr;\n            }\n          }\n          if (_this.options.validator != null) {\n            xpath = \"/\" + ((function() {\n              var i, len, results;\n              results = [];\n              for (i = 0, len = stack.length; i < len; i++) {\n                node = stack[i];\n                results.push(node[\"#name\"]);\n              }\n              return results;\n            })()).concat(nodeName).join(\"/\");\n            (function() {\n              var err;\n              try {\n                return obj = _this.options.validator(xpath, s && s[nodeName], obj);\n              } catch (error1) {\n                err = error1;\n                return _this.emit(\"error\", err);\n              }\n            })();\n          }\n          if (_this.options.explicitChildren && !_this.options.mergeAttrs && typeof obj === 'object') {\n            if (!_this.options.preserveChildrenOrder) {\n              node = {};\n              if (_this.options.attrkey in obj) {\n                node[_this.options.attrkey] = obj[_this.options.attrkey];\n                delete obj[_this.options.attrkey];\n              }\n              if (!_this.options.charsAsChildren && _this.options.charkey in obj) {\n                node[_this.options.charkey] = obj[_this.options.charkey];\n                delete obj[_this.options.charkey];\n              }\n              if (Object.getOwnPropertyNames(obj).length > 0) {\n                node[_this.options.childkey] = obj;\n              }\n              obj = node;\n            } else if (s) {\n              s[_this.options.childkey] = s[_this.options.childkey] || [];\n              objClone = {};\n              for (key in obj) {\n                if (!hasProp.call(obj, key)) continue;\n                defineProperty(objClone, key, obj[key]);\n              }\n              s[_this.options.childkey].push(objClone);\n              delete obj[\"#name\"];\n              if (Object.keys(obj).length === 1 && charkey in obj && !_this.EXPLICIT_CHARKEY) {\n                obj = obj[charkey];\n              }\n            }\n          }\n          if (stack.length > 0) {\n            return _this.assignOrPush(s, nodeName, obj);\n          } else {\n            if (_this.options.explicitRoot) {\n              old = obj;\n              obj = {};\n              defineProperty(obj, nodeName, old);\n            }\n            _this.resultObject = obj;\n            _this.saxParser.ended = true;\n            return _this.emit(\"end\", _this.resultObject);\n          }\n        };\n      })(this);\n      ontext = (function(_this) {\n        return function(text) {\n          var charChild, s;\n          s = stack[stack.length - 1];\n          if (s) {\n            s[charkey] += text;\n            if (_this.options.explicitChildren && _this.options.preserveChildrenOrder && _this.options.charsAsChildren && (_this.options.includeWhiteChars || text.replace(/\\\\n/g, '').trim() !== '')) {\n              s[_this.options.childkey] = s[_this.options.childkey] || [];\n              charChild = {\n                '#name': '__text__'\n              };\n              charChild[charkey] = text;\n              if (_this.options.normalize) {\n                charChild[charkey] = charChild[charkey].replace(/\\s{2,}/g, \" \").trim();\n              }\n              s[_this.options.childkey].push(charChild);\n            }\n            return s;\n          }\n        };\n      })(this);\n      this.saxParser.ontext = ontext;\n      return this.saxParser.oncdata = (function(_this) {\n        return function(text) {\n          var s;\n          s = ontext(text);\n          if (s) {\n            return s.cdata = true;\n          }\n        };\n      })(this);\n    };\n\n    Parser.prototype.parseString = function(str, cb) {\n      var err;\n      if ((cb != null) && typeof cb === \"function\") {\n        this.on(\"end\", function(result) {\n          this.reset();\n          return cb(null, result);\n        });\n        this.on(\"error\", function(err) {\n          this.reset();\n          return cb(err);\n        });\n      }\n      try {\n        str = str.toString();\n        if (str.trim() === '') {\n          this.emit(\"end\", null);\n          return true;\n        }\n        str = bom.stripBOM(str);\n        if (this.options.async) {\n          this.remaining = str;\n          setImmediate(this.processAsync);\n          return this.saxParser;\n        }\n        return this.saxParser.write(str).close();\n      } catch (error1) {\n        err = error1;\n        if (!(this.saxParser.errThrown || this.saxParser.ended)) {\n          this.emit('error', err);\n          return this.saxParser.errThrown = true;\n        } else if (this.saxParser.ended) {\n          throw err;\n        }\n      }\n    };\n\n    Parser.prototype.parseStringPromise = function(str) {\n      return new Promise((function(_this) {\n        return function(resolve, reject) {\n          return _this.parseString(str, function(err, value) {\n            if (err) {\n              return reject(err);\n            } else {\n              return resolve(value);\n            }\n          });\n        };\n      })(this));\n    };\n\n    return Parser;\n\n  })(events);\n\n  exports.parseString = function(str, a, b) {\n    var cb, options, parser;\n    if (b != null) {\n      if (typeof b === 'function') {\n        cb = b;\n      }\n      if (typeof a === 'object') {\n        options = a;\n      }\n    } else {\n      if (typeof a === 'function') {\n        cb = a;\n      }\n      options = {};\n    }\n    parser = new exports.Parser(options);\n    return parser.parseString(str, cb);\n  };\n\n  exports.parseStringPromise = function(str, a) {\n    var options, parser;\n    if (typeof a === 'object') {\n      options = a;\n    }\n    parser = new exports.Parser(options);\n    return parser.parseStringPromise(str);\n  };\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,YAAY;;EACZ,IAAIA,GAAG;IAAEC,QAAQ;IAAEC,cAAc;IAAEC,MAAM;IAAEC,OAAO;IAAEC,WAAW;IAAEC,UAAU;IAAEC,GAAG;IAAEC,YAAY;IAC5FC,IAAI,GAAG,SAAAA,CAASC,EAAE,EAAEC,EAAE,EAAC;MAAE,OAAO,YAAU;QAAE,OAAOD,EAAE,CAACE,KAAK,CAACD,EAAE,EAAEE,SAAS,CAAC;MAAE,CAAC;IAAE,CAAC;IAChFC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BjB,GAAG,GAAGkB,OAAO,CAAC,KAAK,CAAC;EAEpBtB,MAAM,GAAGsB,OAAO,CAAC,QAAQ,CAAC;EAE1BzB,GAAG,GAAGyB,OAAO,CAAC,OAAO,CAAC;EAEtBnB,UAAU,GAAGmB,OAAO,CAAC,cAAc,CAAC;EAEpCjB,YAAY,GAAGiB,OAAO,CAAC,QAAQ,CAAC,CAACjB,YAAY;EAE7CP,QAAQ,GAAGwB,OAAO,CAAC,YAAY,CAAC,CAACxB,QAAQ;EAEzCG,OAAO,GAAG,SAAAA,CAASsB,KAAK,EAAE;IACxB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAKA,KAAK,IAAI,IAAK,IAAIC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,MAAM,KAAK,CAAC;EACxF,CAAC;EAEDxB,WAAW,GAAG,SAAAA,CAASC,UAAU,EAAEwB,IAAI,EAAEb,GAAG,EAAE;IAC5C,IAAIc,CAAC,EAAEC,GAAG,EAAEC,OAAO;IACnB,KAAKF,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG1B,UAAU,CAACuB,MAAM,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACjDE,OAAO,GAAG3B,UAAU,CAACyB,CAAC,CAAC;MACvBD,IAAI,GAAGG,OAAO,CAACH,IAAI,EAAEb,GAAG,CAAC;IAC3B;IACA,OAAOa,IAAI;EACb,CAAC;EAED5B,cAAc,GAAG,SAAAA,CAASgC,GAAG,EAAEjB,GAAG,EAAEkB,KAAK,EAAE;IACzC,IAAIC,UAAU;IACdA,UAAU,GAAGT,MAAM,CAACU,MAAM,CAAC,IAAI,CAAC;IAChCD,UAAU,CAACD,KAAK,GAAGA,KAAK;IACxBC,UAAU,CAACE,QAAQ,GAAG,IAAI;IAC1BF,UAAU,CAACG,UAAU,GAAG,IAAI;IAC5BH,UAAU,CAACI,YAAY,GAAG,IAAI;IAC9B,OAAOb,MAAM,CAACzB,cAAc,CAACgC,GAAG,EAAEjB,GAAG,EAAEmB,UAAU,CAAC;EACpD,CAAC;EAEDK,OAAO,CAACC,MAAM,GAAI,UAASC,UAAU,EAAE;IACrC7B,MAAM,CAAC4B,MAAM,EAAEC,UAAU,CAAC;IAE1B,SAASD,MAAMA,CAACE,IAAI,EAAE;MACpB,IAAI,CAACC,kBAAkB,GAAGpC,IAAI,CAAC,IAAI,CAACoC,kBAAkB,EAAE,IAAI,CAAC;MAC7D,IAAI,CAACC,WAAW,GAAGrC,IAAI,CAAC,IAAI,CAACqC,WAAW,EAAE,IAAI,CAAC;MAC/C,IAAI,CAACC,KAAK,GAAGtC,IAAI,CAAC,IAAI,CAACsC,KAAK,EAAE,IAAI,CAAC;MACnC,IAAI,CAACC,YAAY,GAAGvC,IAAI,CAAC,IAAI,CAACuC,YAAY,EAAE,IAAI,CAAC;MACjD,IAAI,CAACC,YAAY,GAAGxC,IAAI,CAAC,IAAI,CAACwC,YAAY,EAAE,IAAI,CAAC;MACjD,IAAIhC,GAAG,EAAEiC,GAAG,EAAEf,KAAK;MACnB,IAAI,EAAE,IAAI,YAAYM,OAAO,CAACC,MAAM,CAAC,EAAE;QACrC,OAAO,IAAID,OAAO,CAACC,MAAM,CAACE,IAAI,CAAC;MACjC;MACA,IAAI,CAACO,OAAO,GAAG,CAAC,CAAC;MACjBD,GAAG,GAAGjD,QAAQ,CAAC,KAAK,CAAC;MACrB,KAAKgB,GAAG,IAAIiC,GAAG,EAAE;QACf,IAAI,CAAChC,OAAO,CAACC,IAAI,CAAC+B,GAAG,EAAEjC,GAAG,CAAC,EAAE;QAC7BkB,KAAK,GAAGe,GAAG,CAACjC,GAAG,CAAC;QAChB,IAAI,CAACkC,OAAO,CAAClC,GAAG,CAAC,GAAGkB,KAAK;MAC3B;MACA,KAAKlB,GAAG,IAAI2B,IAAI,EAAE;QAChB,IAAI,CAAC1B,OAAO,CAACC,IAAI,CAACyB,IAAI,EAAE3B,GAAG,CAAC,EAAE;QAC9BkB,KAAK,GAAGS,IAAI,CAAC3B,GAAG,CAAC;QACjB,IAAI,CAACkC,OAAO,CAAClC,GAAG,CAAC,GAAGkB,KAAK;MAC3B;MACA,IAAI,IAAI,CAACgB,OAAO,CAACC,KAAK,EAAE;QACtB,IAAI,CAACD,OAAO,CAACE,QAAQ,GAAG,IAAI,CAACF,OAAO,CAACG,OAAO,GAAG,IAAI;MACrD;MACA,IAAI,IAAI,CAACH,OAAO,CAACI,aAAa,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACJ,OAAO,CAACK,iBAAiB,EAAE;UACnC,IAAI,CAACL,OAAO,CAACK,iBAAiB,GAAG,EAAE;QACrC;QACA,IAAI,CAACL,OAAO,CAACK,iBAAiB,CAACC,OAAO,CAACnD,UAAU,CAACoD,SAAS,CAAC;MAC9D;MACA,IAAI,CAACX,KAAK,CAAC,CAAC;IACd;IAEAL,MAAM,CAACpB,SAAS,CAAC2B,YAAY,GAAG,YAAW;MACzC,IAAIU,KAAK,EAAEC,GAAG;MACd,IAAI;QACF,IAAI,IAAI,CAACC,SAAS,CAAChC,MAAM,IAAI,IAAI,CAACsB,OAAO,CAACW,SAAS,EAAE;UACnDH,KAAK,GAAG,IAAI,CAACE,SAAS;UACtB,IAAI,CAACA,SAAS,GAAG,EAAE;UACnB,IAAI,CAACE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,KAAK,CAACL,KAAK,CAAC;UAC5C,OAAO,IAAI,CAACI,SAAS,CAACE,KAAK,CAAC,CAAC;QAC/B,CAAC,MAAM;UACLN,KAAK,GAAG,IAAI,CAACE,SAAS,CAACK,MAAM,CAAC,CAAC,EAAE,IAAI,CAACf,OAAO,CAACW,SAAS,CAAC;UACxD,IAAI,CAACD,SAAS,GAAG,IAAI,CAACA,SAAS,CAACK,MAAM,CAAC,IAAI,CAACf,OAAO,CAACW,SAAS,EAAE,IAAI,CAACD,SAAS,CAAChC,MAAM,CAAC;UACrF,IAAI,CAACkC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACC,KAAK,CAACL,KAAK,CAAC;UAC5C,OAAOnD,YAAY,CAAC,IAAI,CAACyC,YAAY,CAAC;QACxC;MACF,CAAC,CAAC,OAAOkB,MAAM,EAAE;QACfP,GAAG,GAAGO,MAAM;QACZ,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACK,SAAS,EAAE;UAC7B,IAAI,CAACL,SAAS,CAACK,SAAS,GAAG,IAAI;UAC/B,OAAO,IAAI,CAACC,IAAI,CAACT,GAAG,CAAC;QACvB;MACF;IACF,CAAC;IAEDlB,MAAM,CAACpB,SAAS,CAAC0B,YAAY,GAAG,UAASd,GAAG,EAAEjB,GAAG,EAAEqD,QAAQ,EAAE;MAC3D,IAAI,EAAErD,GAAG,IAAIiB,GAAG,CAAC,EAAE;QACjB,IAAI,CAAC,IAAI,CAACiB,OAAO,CAACoB,aAAa,EAAE;UAC/B,OAAOrE,cAAc,CAACgC,GAAG,EAAEjB,GAAG,EAAEqD,QAAQ,CAAC;QAC3C,CAAC,MAAM;UACL,OAAOpE,cAAc,CAACgC,GAAG,EAAEjB,GAAG,EAAE,CAACqD,QAAQ,CAAC,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,EAAEpC,GAAG,CAACjB,GAAG,CAAC,YAAYuD,KAAK,CAAC,EAAE;UAChCtE,cAAc,CAACgC,GAAG,EAAEjB,GAAG,EAAE,CAACiB,GAAG,CAACjB,GAAG,CAAC,CAAC,CAAC;QACtC;QACA,OAAOiB,GAAG,CAACjB,GAAG,CAAC,CAACwD,IAAI,CAACH,QAAQ,CAAC;MAChC;IACF,CAAC;IAED5B,MAAM,CAACpB,SAAS,CAACyB,KAAK,GAAG,YAAW;MAClC,IAAIO,OAAO,EAAEoB,OAAO,EAAEC,MAAM,EAAEC,KAAK;MACnC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACd,SAAS,GAAGxD,GAAG,CAACuE,MAAM,CAAC,IAAI,CAAC3B,OAAO,CAAC4B,MAAM,EAAE;QAC/CC,IAAI,EAAE,KAAK;QACXtB,SAAS,EAAE,KAAK;QAChBN,KAAK,EAAE,IAAI,CAACD,OAAO,CAACC;MACtB,CAAC,CAAC;MACF,IAAI,CAACW,SAAS,CAACK,SAAS,GAAG,KAAK;MAChC,IAAI,CAACL,SAAS,CAACkB,OAAO,GAAI,UAASC,KAAK,EAAE;QACxC,OAAO,UAASC,KAAK,EAAE;UACrBD,KAAK,CAACnB,SAAS,CAACqB,MAAM,CAAC,CAAC;UACxB,IAAI,CAACF,KAAK,CAACnB,SAAS,CAACK,SAAS,EAAE;YAC9Bc,KAAK,CAACnB,SAAS,CAACK,SAAS,GAAG,IAAI;YAChC,OAAOc,KAAK,CAACb,IAAI,CAAC,OAAO,EAAEc,KAAK,CAAC;UACnC;QACF,CAAC;MACH,CAAC,CAAE,IAAI,CAAC;MACR,IAAI,CAACpB,SAAS,CAACsB,KAAK,GAAI,UAASH,KAAK,EAAE;QACtC,OAAO,YAAW;UAChB,IAAI,CAACA,KAAK,CAACnB,SAAS,CAACuB,KAAK,EAAE;YAC1BJ,KAAK,CAACnB,SAAS,CAACuB,KAAK,GAAG,IAAI;YAC5B,OAAOJ,KAAK,CAACb,IAAI,CAAC,KAAK,EAAEa,KAAK,CAACK,YAAY,CAAC;UAC9C;QACF,CAAC;MACH,CAAC,CAAE,IAAI,CAAC;MACR,IAAI,CAACxB,SAAS,CAACuB,KAAK,GAAG,KAAK;MAC5B,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACrC,OAAO,CAACsC,eAAe;MACpD,IAAI,CAACF,YAAY,GAAG,IAAI;MACxBX,KAAK,GAAG,EAAE;MACVtB,OAAO,GAAG,IAAI,CAACH,OAAO,CAACG,OAAO;MAC9BoB,OAAO,GAAG,IAAI,CAACvB,OAAO,CAACuB,OAAO;MAC9B,IAAI,CAACX,SAAS,CAAC2B,SAAS,GAAI,UAASR,KAAK,EAAE;QAC1C,OAAO,UAASS,IAAI,EAAE;UACpB,IAAI1E,GAAG,EAAEqD,QAAQ,EAAEpC,GAAG,EAAE0D,YAAY,EAAE1C,GAAG;UACzChB,GAAG,GAAG,CAAC,CAAC;UACRA,GAAG,CAACwC,OAAO,CAAC,GAAG,EAAE;UACjB,IAAI,CAACQ,KAAK,CAAC/B,OAAO,CAAC0C,WAAW,EAAE;YAC9B3C,GAAG,GAAGyC,IAAI,CAACG,UAAU;YACrB,KAAK7E,GAAG,IAAIiC,GAAG,EAAE;cACf,IAAI,CAAChC,OAAO,CAACC,IAAI,CAAC+B,GAAG,EAAEjC,GAAG,CAAC,EAAE;cAC7B,IAAI,EAAEqC,OAAO,IAAIpB,GAAG,CAAC,IAAI,CAACgD,KAAK,CAAC/B,OAAO,CAAC4C,UAAU,EAAE;gBAClD7D,GAAG,CAACoB,OAAO,CAAC,GAAG,CAAC,CAAC;cACnB;cACAgB,QAAQ,GAAGY,KAAK,CAAC/B,OAAO,CAAC6C,mBAAmB,GAAG3F,WAAW,CAAC6E,KAAK,CAAC/B,OAAO,CAAC6C,mBAAmB,EAAEL,IAAI,CAACG,UAAU,CAAC7E,GAAG,CAAC,EAAEA,GAAG,CAAC,GAAG0E,IAAI,CAACG,UAAU,CAAC7E,GAAG,CAAC;cAC/I2E,YAAY,GAAGV,KAAK,CAAC/B,OAAO,CAAC8C,kBAAkB,GAAG5F,WAAW,CAAC6E,KAAK,CAAC/B,OAAO,CAAC8C,kBAAkB,EAAEhF,GAAG,CAAC,GAAGA,GAAG;cAC1G,IAAIiE,KAAK,CAAC/B,OAAO,CAAC4C,UAAU,EAAE;gBAC5Bb,KAAK,CAAClC,YAAY,CAACd,GAAG,EAAE0D,YAAY,EAAEtB,QAAQ,CAAC;cACjD,CAAC,MAAM;gBACLpE,cAAc,CAACgC,GAAG,CAACoB,OAAO,CAAC,EAAEsC,YAAY,EAAEtB,QAAQ,CAAC;cACtD;YACF;UACF;UACApC,GAAG,CAAC,OAAO,CAAC,GAAGgD,KAAK,CAAC/B,OAAO,CAACK,iBAAiB,GAAGnD,WAAW,CAAC6E,KAAK,CAAC/B,OAAO,CAACK,iBAAiB,EAAEmC,IAAI,CAACO,IAAI,CAAC,GAAGP,IAAI,CAACO,IAAI;UACpH,IAAIhB,KAAK,CAAC/B,OAAO,CAACC,KAAK,EAAE;YACvBlB,GAAG,CAACgD,KAAK,CAAC/B,OAAO,CAACE,QAAQ,CAAC,GAAG;cAC5B8C,GAAG,EAAER,IAAI,CAACQ,GAAG;cACbC,KAAK,EAAET,IAAI,CAACS;YACd,CAAC;UACH;UACA,OAAOxB,KAAK,CAACH,IAAI,CAACvC,GAAG,CAAC;QACxB,CAAC;MACH,CAAC,CAAE,IAAI,CAAC;MACR,IAAI,CAAC6B,SAAS,CAACsC,UAAU,GAAI,UAASnB,KAAK,EAAE;QAC3C,OAAO,YAAW;UAChB,IAAIoB,KAAK,EAAEC,QAAQ,EAAEtF,GAAG,EAAE0E,IAAI,EAAEa,QAAQ,EAAEtE,GAAG,EAAEuE,QAAQ,EAAEC,GAAG,EAAEC,CAAC,EAAEC,KAAK;UACtE1E,GAAG,GAAG0C,KAAK,CAACiC,GAAG,CAAC,CAAC;UACjBL,QAAQ,GAAGtE,GAAG,CAAC,OAAO,CAAC;UACvB,IAAI,CAACgD,KAAK,CAAC/B,OAAO,CAAC2D,gBAAgB,IAAI,CAAC5B,KAAK,CAAC/B,OAAO,CAAC4D,qBAAqB,EAAE;YAC3E,OAAO7E,GAAG,CAAC,OAAO,CAAC;UACrB;UACA,IAAIA,GAAG,CAACoE,KAAK,KAAK,IAAI,EAAE;YACtBA,KAAK,GAAGpE,GAAG,CAACoE,KAAK;YACjB,OAAOpE,GAAG,CAACoE,KAAK;UAClB;UACAK,CAAC,GAAG/B,KAAK,CAACA,KAAK,CAAC/C,MAAM,GAAG,CAAC,CAAC;UAC3B,IAAIK,GAAG,CAACwC,OAAO,CAAC,CAACsC,KAAK,CAAC,OAAO,CAAC,IAAI,CAACV,KAAK,EAAE;YACzCC,QAAQ,GAAGrE,GAAG,CAACwC,OAAO,CAAC;YACvB,OAAOxC,GAAG,CAACwC,OAAO,CAAC;UACrB,CAAC,MAAM;YACL,IAAIQ,KAAK,CAAC/B,OAAO,CAAC6B,IAAI,EAAE;cACtB9C,GAAG,CAACwC,OAAO,CAAC,GAAGxC,GAAG,CAACwC,OAAO,CAAC,CAACM,IAAI,CAAC,CAAC;YACpC;YACA,IAAIE,KAAK,CAAC/B,OAAO,CAACO,SAAS,EAAE;cAC3BxB,GAAG,CAACwC,OAAO,CAAC,GAAGxC,GAAG,CAACwC,OAAO,CAAC,CAACuC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACjC,IAAI,CAAC,CAAC;YAC5D;YACA9C,GAAG,CAACwC,OAAO,CAAC,GAAGQ,KAAK,CAAC/B,OAAO,CAAC+D,eAAe,GAAG7G,WAAW,CAAC6E,KAAK,CAAC/B,OAAO,CAAC+D,eAAe,EAAEhF,GAAG,CAACwC,OAAO,CAAC,EAAE8B,QAAQ,CAAC,GAAGtE,GAAG,CAACwC,OAAO,CAAC;YAChI,IAAI/C,MAAM,CAACC,IAAI,CAACM,GAAG,CAAC,CAACL,MAAM,KAAK,CAAC,IAAI6C,OAAO,IAAIxC,GAAG,IAAI,CAACgD,KAAK,CAACM,gBAAgB,EAAE;cAC9EtD,GAAG,GAAGA,GAAG,CAACwC,OAAO,CAAC;YACpB;UACF;UACA,IAAItE,OAAO,CAAC8B,GAAG,CAAC,EAAE;YAChB,IAAI,OAAOgD,KAAK,CAAC/B,OAAO,CAACgE,QAAQ,KAAK,UAAU,EAAE;cAChDjF,GAAG,GAAGgD,KAAK,CAAC/B,OAAO,CAACgE,QAAQ,CAAC,CAAC;YAChC,CAAC,MAAM;cACLjF,GAAG,GAAGgD,KAAK,CAAC/B,OAAO,CAACgE,QAAQ,KAAK,EAAE,GAAGjC,KAAK,CAAC/B,OAAO,CAACgE,QAAQ,GAAGZ,QAAQ;YACzE;UACF;UACA,IAAIrB,KAAK,CAAC/B,OAAO,CAACiE,SAAS,IAAI,IAAI,EAAE;YACnCR,KAAK,GAAG,GAAG,GAAK,YAAW;cACzB,IAAI7E,CAAC,EAAEC,GAAG,EAAEqF,OAAO;cACnBA,OAAO,GAAG,EAAE;cACZ,KAAKtF,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG4C,KAAK,CAAC/C,MAAM,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;gBAC5C4D,IAAI,GAAGf,KAAK,CAAC7C,CAAC,CAAC;gBACfsF,OAAO,CAAC5C,IAAI,CAACkB,IAAI,CAAC,OAAO,CAAC,CAAC;cAC7B;cACA,OAAO0B,OAAO;YAChB,CAAC,CAAE,CAAC,CAAEC,MAAM,CAACd,QAAQ,CAAC,CAACe,IAAI,CAAC,GAAG,CAAC;YAChC,CAAC,YAAW;cACV,IAAI3D,GAAG;cACP,IAAI;gBACF,OAAO1B,GAAG,GAAGgD,KAAK,CAAC/B,OAAO,CAACiE,SAAS,CAACR,KAAK,EAAED,CAAC,IAAIA,CAAC,CAACH,QAAQ,CAAC,EAAEtE,GAAG,CAAC;cACpE,CAAC,CAAC,OAAOiC,MAAM,EAAE;gBACfP,GAAG,GAAGO,MAAM;gBACZ,OAAOe,KAAK,CAACb,IAAI,CAAC,OAAO,EAAET,GAAG,CAAC;cACjC;YACF,CAAC,EAAE,CAAC;UACN;UACA,IAAIsB,KAAK,CAAC/B,OAAO,CAAC2D,gBAAgB,IAAI,CAAC5B,KAAK,CAAC/B,OAAO,CAAC4C,UAAU,IAAI,OAAO7D,GAAG,KAAK,QAAQ,EAAE;YAC1F,IAAI,CAACgD,KAAK,CAAC/B,OAAO,CAAC4D,qBAAqB,EAAE;cACxCpB,IAAI,GAAG,CAAC,CAAC;cACT,IAAIT,KAAK,CAAC/B,OAAO,CAACG,OAAO,IAAIpB,GAAG,EAAE;gBAChCyD,IAAI,CAACT,KAAK,CAAC/B,OAAO,CAACG,OAAO,CAAC,GAAGpB,GAAG,CAACgD,KAAK,CAAC/B,OAAO,CAACG,OAAO,CAAC;gBACxD,OAAOpB,GAAG,CAACgD,KAAK,CAAC/B,OAAO,CAACG,OAAO,CAAC;cACnC;cACA,IAAI,CAAC4B,KAAK,CAAC/B,OAAO,CAACqE,eAAe,IAAItC,KAAK,CAAC/B,OAAO,CAACuB,OAAO,IAAIxC,GAAG,EAAE;gBAClEyD,IAAI,CAACT,KAAK,CAAC/B,OAAO,CAACuB,OAAO,CAAC,GAAGxC,GAAG,CAACgD,KAAK,CAAC/B,OAAO,CAACuB,OAAO,CAAC;gBACxD,OAAOxC,GAAG,CAACgD,KAAK,CAAC/B,OAAO,CAACuB,OAAO,CAAC;cACnC;cACA,IAAI/C,MAAM,CAAC8F,mBAAmB,CAACvF,GAAG,CAAC,CAACL,MAAM,GAAG,CAAC,EAAE;gBAC9C8D,IAAI,CAACT,KAAK,CAAC/B,OAAO,CAACuE,QAAQ,CAAC,GAAGxF,GAAG;cACpC;cACAA,GAAG,GAAGyD,IAAI;YACZ,CAAC,MAAM,IAAIgB,CAAC,EAAE;cACZA,CAAC,CAACzB,KAAK,CAAC/B,OAAO,CAACuE,QAAQ,CAAC,GAAGf,CAAC,CAACzB,KAAK,CAAC/B,OAAO,CAACuE,QAAQ,CAAC,IAAI,EAAE;cAC3DjB,QAAQ,GAAG,CAAC,CAAC;cACb,KAAKxF,GAAG,IAAIiB,GAAG,EAAE;gBACf,IAAI,CAAChB,OAAO,CAACC,IAAI,CAACe,GAAG,EAAEjB,GAAG,CAAC,EAAE;gBAC7Bf,cAAc,CAACuG,QAAQ,EAAExF,GAAG,EAAEiB,GAAG,CAACjB,GAAG,CAAC,CAAC;cACzC;cACA0F,CAAC,CAACzB,KAAK,CAAC/B,OAAO,CAACuE,QAAQ,CAAC,CAACjD,IAAI,CAACgC,QAAQ,CAAC;cACxC,OAAOvE,GAAG,CAAC,OAAO,CAAC;cACnB,IAAIP,MAAM,CAACC,IAAI,CAACM,GAAG,CAAC,CAACL,MAAM,KAAK,CAAC,IAAI6C,OAAO,IAAIxC,GAAG,IAAI,CAACgD,KAAK,CAACM,gBAAgB,EAAE;gBAC9EtD,GAAG,GAAGA,GAAG,CAACwC,OAAO,CAAC;cACpB;YACF;UACF;UACA,IAAIE,KAAK,CAAC/C,MAAM,GAAG,CAAC,EAAE;YACpB,OAAOqD,KAAK,CAAClC,YAAY,CAAC2D,CAAC,EAAEH,QAAQ,EAAEtE,GAAG,CAAC;UAC7C,CAAC,MAAM;YACL,IAAIgD,KAAK,CAAC/B,OAAO,CAACwE,YAAY,EAAE;cAC9BjB,GAAG,GAAGxE,GAAG;cACTA,GAAG,GAAG,CAAC,CAAC;cACRhC,cAAc,CAACgC,GAAG,EAAEsE,QAAQ,EAAEE,GAAG,CAAC;YACpC;YACAxB,KAAK,CAACK,YAAY,GAAGrD,GAAG;YACxBgD,KAAK,CAACnB,SAAS,CAACuB,KAAK,GAAG,IAAI;YAC5B,OAAOJ,KAAK,CAACb,IAAI,CAAC,KAAK,EAAEa,KAAK,CAACK,YAAY,CAAC;UAC9C;QACF,CAAC;MACH,CAAC,CAAE,IAAI,CAAC;MACRZ,MAAM,GAAI,UAASO,KAAK,EAAE;QACxB,OAAO,UAAS0C,IAAI,EAAE;UACpB,IAAIC,SAAS,EAAElB,CAAC;UAChBA,CAAC,GAAG/B,KAAK,CAACA,KAAK,CAAC/C,MAAM,GAAG,CAAC,CAAC;UAC3B,IAAI8E,CAAC,EAAE;YACLA,CAAC,CAACjC,OAAO,CAAC,IAAIkD,IAAI;YAClB,IAAI1C,KAAK,CAAC/B,OAAO,CAAC2D,gBAAgB,IAAI5B,KAAK,CAAC/B,OAAO,CAAC4D,qBAAqB,IAAI7B,KAAK,CAAC/B,OAAO,CAACqE,eAAe,KAAKtC,KAAK,CAAC/B,OAAO,CAAC2E,iBAAiB,IAAIF,IAAI,CAACX,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACjC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;cACzL2B,CAAC,CAACzB,KAAK,CAAC/B,OAAO,CAACuE,QAAQ,CAAC,GAAGf,CAAC,CAACzB,KAAK,CAAC/B,OAAO,CAACuE,QAAQ,CAAC,IAAI,EAAE;cAC3DG,SAAS,GAAG;gBACV,OAAO,EAAE;cACX,CAAC;cACDA,SAAS,CAACnD,OAAO,CAAC,GAAGkD,IAAI;cACzB,IAAI1C,KAAK,CAAC/B,OAAO,CAACO,SAAS,EAAE;gBAC3BmE,SAAS,CAACnD,OAAO,CAAC,GAAGmD,SAAS,CAACnD,OAAO,CAAC,CAACuC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACjC,IAAI,CAAC,CAAC;cACxE;cACA2B,CAAC,CAACzB,KAAK,CAAC/B,OAAO,CAACuE,QAAQ,CAAC,CAACjD,IAAI,CAACoD,SAAS,CAAC;YAC3C;YACA,OAAOlB,CAAC;UACV;QACF,CAAC;MACH,CAAC,CAAE,IAAI,CAAC;MACR,IAAI,CAAC5C,SAAS,CAACY,MAAM,GAAGA,MAAM;MAC9B,OAAO,IAAI,CAACZ,SAAS,CAACgE,OAAO,GAAI,UAAS7C,KAAK,EAAE;QAC/C,OAAO,UAAS0C,IAAI,EAAE;UACpB,IAAIjB,CAAC;UACLA,CAAC,GAAGhC,MAAM,CAACiD,IAAI,CAAC;UAChB,IAAIjB,CAAC,EAAE;YACL,OAAOA,CAAC,CAACL,KAAK,GAAG,IAAI;UACvB;QACF,CAAC;MACH,CAAC,CAAE,IAAI,CAAC;IACV,CAAC;IAED5D,MAAM,CAACpB,SAAS,CAACwB,WAAW,GAAG,UAASkF,GAAG,EAAEC,EAAE,EAAE;MAC/C,IAAIrE,GAAG;MACP,IAAKqE,EAAE,IAAI,IAAI,IAAK,OAAOA,EAAE,KAAK,UAAU,EAAE;QAC5C,IAAI,CAACC,EAAE,CAAC,KAAK,EAAE,UAASC,MAAM,EAAE;UAC9B,IAAI,CAACpF,KAAK,CAAC,CAAC;UACZ,OAAOkF,EAAE,CAAC,IAAI,EAAEE,MAAM,CAAC;QACzB,CAAC,CAAC;QACF,IAAI,CAACD,EAAE,CAAC,OAAO,EAAE,UAAStE,GAAG,EAAE;UAC7B,IAAI,CAACb,KAAK,CAAC,CAAC;UACZ,OAAOkF,EAAE,CAACrE,GAAG,CAAC;QAChB,CAAC,CAAC;MACJ;MACA,IAAI;QACFoE,GAAG,GAAGA,GAAG,CAACI,QAAQ,CAAC,CAAC;QACpB,IAAIJ,GAAG,CAAChD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACrB,IAAI,CAACX,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;UACtB,OAAO,IAAI;QACb;QACA2D,GAAG,GAAGhI,GAAG,CAACqI,QAAQ,CAACL,GAAG,CAAC;QACvB,IAAI,IAAI,CAAC7E,OAAO,CAACmF,KAAK,EAAE;UACtB,IAAI,CAACzE,SAAS,GAAGmE,GAAG;UACpBxH,YAAY,CAAC,IAAI,CAACyC,YAAY,CAAC;UAC/B,OAAO,IAAI,CAACc,SAAS;QACvB;QACA,OAAO,IAAI,CAACA,SAAS,CAACC,KAAK,CAACgE,GAAG,CAAC,CAAC/D,KAAK,CAAC,CAAC;MAC1C,CAAC,CAAC,OAAOE,MAAM,EAAE;QACfP,GAAG,GAAGO,MAAM;QACZ,IAAI,EAAE,IAAI,CAACJ,SAAS,CAACK,SAAS,IAAI,IAAI,CAACL,SAAS,CAACuB,KAAK,CAAC,EAAE;UACvD,IAAI,CAACjB,IAAI,CAAC,OAAO,EAAET,GAAG,CAAC;UACvB,OAAO,IAAI,CAACG,SAAS,CAACK,SAAS,GAAG,IAAI;QACxC,CAAC,MAAM,IAAI,IAAI,CAACL,SAAS,CAACuB,KAAK,EAAE;UAC/B,MAAM1B,GAAG;QACX;MACF;IACF,CAAC;IAEDlB,MAAM,CAACpB,SAAS,CAACuB,kBAAkB,GAAG,UAASmF,GAAG,EAAE;MAClD,OAAO,IAAIO,OAAO,CAAE,UAASrD,KAAK,EAAE;QAClC,OAAO,UAASsD,OAAO,EAAEC,MAAM,EAAE;UAC/B,OAAOvD,KAAK,CAACpC,WAAW,CAACkF,GAAG,EAAE,UAASpE,GAAG,EAAEzB,KAAK,EAAE;YACjD,IAAIyB,GAAG,EAAE;cACP,OAAO6E,MAAM,CAAC7E,GAAG,CAAC;YACpB,CAAC,MAAM;cACL,OAAO4E,OAAO,CAACrG,KAAK,CAAC;YACvB;UACF,CAAC,CAAC;QACJ,CAAC;MACH,CAAC,CAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAED,OAAOO,MAAM;EAEf,CAAC,CAAEvC,MAAM,CAAC;EAEVsC,OAAO,CAACK,WAAW,GAAG,UAASkF,GAAG,EAAEU,CAAC,EAAEC,CAAC,EAAE;IACxC,IAAIV,EAAE,EAAE9E,OAAO,EAAE2B,MAAM;IACvB,IAAI6D,CAAC,IAAI,IAAI,EAAE;MACb,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;QAC3BV,EAAE,GAAGU,CAAC;MACR;MACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;QACzBvF,OAAO,GAAGuF,CAAC;MACb;IACF,CAAC,MAAM;MACL,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;QAC3BT,EAAE,GAAGS,CAAC;MACR;MACAvF,OAAO,GAAG,CAAC,CAAC;IACd;IACA2B,MAAM,GAAG,IAAIrC,OAAO,CAACC,MAAM,CAACS,OAAO,CAAC;IACpC,OAAO2B,MAAM,CAAChC,WAAW,CAACkF,GAAG,EAAEC,EAAE,CAAC;EACpC,CAAC;EAEDxF,OAAO,CAACI,kBAAkB,GAAG,UAASmF,GAAG,EAAEU,CAAC,EAAE;IAC5C,IAAIvF,OAAO,EAAE2B,MAAM;IACnB,IAAI,OAAO4D,CAAC,KAAK,QAAQ,EAAE;MACzBvF,OAAO,GAAGuF,CAAC;IACb;IACA5D,MAAM,GAAG,IAAIrC,OAAO,CAACC,MAAM,CAACS,OAAO,CAAC;IACpC,OAAO2B,MAAM,CAACjC,kBAAkB,CAACmF,GAAG,CAAC;EACvC,CAAC;AAEH,CAAC,EAAE7G,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
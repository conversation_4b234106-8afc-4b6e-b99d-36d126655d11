{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLCharacterData,\n    XMLProcessingInstruction,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  NodeType = require('./NodeType');\n  XMLCharacterData = require('./XMLCharacterData');\n  module.exports = XMLProcessingInstruction = function (superClass) {\n    extend(XMLProcessingInstruction, superClass);\n    function XMLProcessingInstruction(parent, target, value) {\n      XMLProcessingInstruction.__super__.constructor.call(this, parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.type = NodeType.ProcessingInstruction;\n      this.target = this.stringify.insTarget(target);\n      this.name = this.target;\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n    XMLProcessingInstruction.prototype.clone = function () {\n      return Object.create(this);\n    };\n    XMLProcessingInstruction.prototype.toString = function (options) {\n      return this.options.writer.processingInstruction(this, this.options.writer.filterOptions(options));\n    };\n    XMLProcessingInstruction.prototype.isEqualNode = function (node) {\n      if (!XMLProcessingInstruction.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.target !== this.target) {\n        return false;\n      }\n      return true;\n    };\n    return XMLProcessingInstruction;\n  }(XMLCharacterData);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLCharacterData", "XMLProcessingInstruction", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "target", "value", "Error", "debugInfo", "type", "ProcessingInstruction", "stringify", "insTarget", "name", "insValue", "clone", "Object", "create", "toString", "options", "writer", "processingInstruction", "filterOptions", "isEqualNode", "node", "apply", "arguments"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLProcessingInstruction,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLCharacterData = require('./XMLCharacterData');\n\n  module.exports = XMLProcessingInstruction = (function(superClass) {\n    extend(XMLProcessingInstruction, superClass);\n\n    function XMLProcessingInstruction(parent, target, value) {\n      XMLProcessingInstruction.__super__.constructor.call(this, parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.type = NodeType.ProcessingInstruction;\n      this.target = this.stringify.insTarget(target);\n      this.name = this.target;\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n\n    XMLProcessingInstruction.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLProcessingInstruction.prototype.toString = function(options) {\n      return this.options.writer.processingInstruction(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLProcessingInstruction.prototype.isEqualNode = function(node) {\n      if (!XMLProcessingInstruction.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.target !== this.target) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLProcessingInstruction;\n\n  })(XMLCharacterData);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,gBAAgB;IAAEC,wBAAwB;IACtDC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7Bb,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCb,gBAAgB,GAAGa,OAAO,CAAC,oBAAoB,CAAC;EAEhDC,MAAM,CAACC,OAAO,GAAGd,wBAAwB,GAAI,UAASe,UAAU,EAAE;IAChEd,MAAM,CAACD,wBAAwB,EAAEe,UAAU,CAAC;IAE5C,SAASf,wBAAwBA,CAACG,MAAM,EAAEa,MAAM,EAAEC,KAAK,EAAE;MACvDjB,wBAAwB,CAACU,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACjE,IAAIa,MAAM,IAAI,IAAI,EAAE;QAClB,MAAM,IAAIE,KAAK,CAAC,8BAA8B,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MACpE;MACA,IAAI,CAACC,IAAI,GAAGtB,QAAQ,CAACuB,qBAAqB;MAC1C,IAAI,CAACL,MAAM,GAAG,IAAI,CAACM,SAAS,CAACC,SAAS,CAACP,MAAM,CAAC;MAC9C,IAAI,CAACQ,IAAI,GAAG,IAAI,CAACR,MAAM;MACvB,IAAIC,KAAK,EAAE;QACT,IAAI,CAACA,KAAK,GAAG,IAAI,CAACK,SAAS,CAACG,QAAQ,CAACR,KAAK,CAAC;MAC7C;IACF;IAEAjB,wBAAwB,CAACS,SAAS,CAACiB,KAAK,GAAG,YAAW;MACpD,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED5B,wBAAwB,CAACS,SAAS,CAACoB,QAAQ,GAAG,UAASC,OAAO,EAAE;MAC9D,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACC,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAACF,OAAO,CAACC,MAAM,CAACE,aAAa,CAACH,OAAO,CAAC,CAAC;IACpG,CAAC;IAED9B,wBAAwB,CAACS,SAAS,CAACyB,WAAW,GAAG,UAASC,IAAI,EAAE;MAC9D,IAAI,CAACnC,wBAAwB,CAACU,SAAS,CAACwB,WAAW,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACH,WAAW,CAACC,IAAI,CAAC,EAAE;QAC5F,OAAO,KAAK;MACd;MACA,IAAIA,IAAI,CAACnB,MAAM,KAAK,IAAI,CAACA,MAAM,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IAED,OAAOhB,wBAAwB;EAEjC,CAAC,CAAED,gBAAgB,CAAC;AAEtB,CAAC,EAAEO,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType, XMLAttribute, XMLNode;\n  NodeType = require('./NodeType');\n  XMLNode = require('./XMLNode');\n  module.exports = XMLAttribute = function () {\n    function XMLAttribute(parent, name, value) {\n      this.parent = parent;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.value = this.stringify.attValue(value);\n      this.type = NodeType.Attribute;\n      this.isId = false;\n      this.schemaTypeInfo = null;\n    }\n    Object.defineProperty(XMLAttribute.prototype, 'nodeType', {\n      get: function () {\n        return this.type;\n      }\n    });\n    Object.defineProperty(XMLAttribute.prototype, 'ownerElement', {\n      get: function () {\n        return this.parent;\n      }\n    });\n    Object.defineProperty(XMLAttribute.prototype, 'textContent', {\n      get: function () {\n        return this.value;\n      },\n      set: function (value) {\n        return this.value = value || '';\n      }\n    });\n    Object.defineProperty(XMLAttribute.prototype, 'namespaceURI', {\n      get: function () {\n        return '';\n      }\n    });\n    Object.defineProperty(XMLAttribute.prototype, 'prefix', {\n      get: function () {\n        return '';\n      }\n    });\n    Object.defineProperty(XMLAttribute.prototype, 'localName', {\n      get: function () {\n        return this.name;\n      }\n    });\n    Object.defineProperty(XMLAttribute.prototype, 'specified', {\n      get: function () {\n        return true;\n      }\n    });\n    XMLAttribute.prototype.clone = function () {\n      return Object.create(this);\n    };\n    XMLAttribute.prototype.toString = function (options) {\n      return this.options.writer.attribute(this, this.options.writer.filterOptions(options));\n    };\n    XMLAttribute.prototype.debugInfo = function (name) {\n      name = name || this.name;\n      if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else {\n        return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n      }\n    };\n    XMLAttribute.prototype.isEqualNode = function (node) {\n      if (node.namespaceURI !== this.namespaceURI) {\n        return false;\n      }\n      if (node.prefix !== this.prefix) {\n        return false;\n      }\n      if (node.localName !== this.localName) {\n        return false;\n      }\n      if (node.value !== this.value) {\n        return false;\n      }\n      return true;\n    };\n    return XMLAttribute;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLAttribute", "XMLNode", "require", "module", "exports", "parent", "name", "value", "options", "stringify", "Error", "debugInfo", "attValue", "type", "Attribute", "isId", "schemaTypeInfo", "Object", "defineProperty", "prototype", "get", "set", "clone", "create", "toString", "writer", "attribute", "filterOptions", "isEqualNode", "node", "namespaceURI", "prefix", "localName", "call"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xmlbuilder/lib/XMLAttribute.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLAttribute, XMLNode;\n\n  NodeType = require('./NodeType');\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLAttribute = (function() {\n    function XMLAttribute(parent, name, value) {\n      this.parent = parent;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.value = this.stringify.attValue(value);\n      this.type = NodeType.Attribute;\n      this.isId = false;\n      this.schemaTypeInfo = null;\n    }\n\n    Object.defineProperty(XMLAttribute.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'ownerElement', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'specified', {\n      get: function() {\n        return true;\n      }\n    });\n\n    XMLAttribute.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLAttribute.prototype.toString = function(options) {\n      return this.options.writer.attribute(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLAttribute.prototype.debugInfo = function(name) {\n      name = name || this.name;\n      if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else {\n        return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLAttribute.prototype.isEqualNode = function(node) {\n      if (node.namespaceURI !== this.namespaceURI) {\n        return false;\n      }\n      if (node.prefix !== this.prefix) {\n        return false;\n      }\n      if (node.localName !== this.localName) {\n        return false;\n      }\n      if (node.value !== this.value) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLAttribute;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ,EAAEC,YAAY,EAAEC,OAAO;EAEnCF,QAAQ,GAAGG,OAAO,CAAC,YAAY,CAAC;EAEhCD,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;EAE9BC,MAAM,CAACC,OAAO,GAAGJ,YAAY,GAAI,YAAW;IAC1C,SAASA,YAAYA,CAACK,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;MACzC,IAAI,CAACF,MAAM,GAAGA,MAAM;MACpB,IAAI,IAAI,CAACA,MAAM,EAAE;QACf,IAAI,CAACG,OAAO,GAAG,IAAI,CAACH,MAAM,CAACG,OAAO;QAClC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACJ,MAAM,CAACI,SAAS;MACxC;MACA,IAAIH,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAII,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC,CAAC;MACpE;MACA,IAAI,CAACA,IAAI,GAAG,IAAI,CAACG,SAAS,CAACH,IAAI,CAACA,IAAI,CAAC;MACrC,IAAI,CAACC,KAAK,GAAG,IAAI,CAACE,SAAS,CAACG,QAAQ,CAACL,KAAK,CAAC;MAC3C,IAAI,CAACM,IAAI,GAAGd,QAAQ,CAACe,SAAS;MAC9B,IAAI,CAACC,IAAI,GAAG,KAAK;MACjB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC5B;IAEAC,MAAM,CAACC,cAAc,CAAClB,YAAY,CAACmB,SAAS,EAAE,UAAU,EAAE;MACxDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACP,IAAI;MAClB;IACF,CAAC,CAAC;IAEFI,MAAM,CAACC,cAAc,CAAClB,YAAY,CAACmB,SAAS,EAAE,cAAc,EAAE;MAC5DC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACf,MAAM;MACpB;IACF,CAAC,CAAC;IAEFY,MAAM,CAACC,cAAc,CAAClB,YAAY,CAACmB,SAAS,EAAE,aAAa,EAAE;MAC3DC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACb,KAAK;MACnB,CAAC;MACDc,GAAG,EAAE,SAAAA,CAASd,KAAK,EAAE;QACnB,OAAO,IAAI,CAACA,KAAK,GAAGA,KAAK,IAAI,EAAE;MACjC;IACF,CAAC,CAAC;IAEFU,MAAM,CAACC,cAAc,CAAClB,YAAY,CAACmB,SAAS,EAAE,cAAc,EAAE;MAC5DC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IAEFH,MAAM,CAACC,cAAc,CAAClB,YAAY,CAACmB,SAAS,EAAE,QAAQ,EAAE;MACtDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IAEFH,MAAM,CAACC,cAAc,CAAClB,YAAY,CAACmB,SAAS,EAAE,WAAW,EAAE;MACzDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACd,IAAI;MAClB;IACF,CAAC,CAAC;IAEFW,MAAM,CAACC,cAAc,CAAClB,YAAY,CAACmB,SAAS,EAAE,WAAW,EAAE;MACzDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI;MACb;IACF,CAAC,CAAC;IAEFpB,YAAY,CAACmB,SAAS,CAACG,KAAK,GAAG,YAAW;MACxC,OAAOL,MAAM,CAACM,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAEDvB,YAAY,CAACmB,SAAS,CAACK,QAAQ,GAAG,UAAShB,OAAO,EAAE;MAClD,OAAO,IAAI,CAACA,OAAO,CAACiB,MAAM,CAACC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAClB,OAAO,CAACiB,MAAM,CAACE,aAAa,CAACnB,OAAO,CAAC,CAAC;IACxF,CAAC;IAEDR,YAAY,CAACmB,SAAS,CAACR,SAAS,GAAG,UAASL,IAAI,EAAE;MAChDA,IAAI,GAAGA,IAAI,IAAI,IAAI,CAACA,IAAI;MACxB,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,WAAW,GAAG,IAAI,CAACD,MAAM,CAACC,IAAI,GAAG,GAAG;MAC7C,CAAC,MAAM;QACL,OAAO,cAAc,GAAGA,IAAI,GAAG,cAAc,GAAG,IAAI,CAACD,MAAM,CAACC,IAAI,GAAG,GAAG;MACxE;IACF,CAAC;IAEDN,YAAY,CAACmB,SAAS,CAACS,WAAW,GAAG,UAASC,IAAI,EAAE;MAClD,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAACA,YAAY,EAAE;QAC3C,OAAO,KAAK;MACd;MACA,IAAID,IAAI,CAACE,MAAM,KAAK,IAAI,CAACA,MAAM,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,IAAIF,IAAI,CAACG,SAAS,KAAK,IAAI,CAACA,SAAS,EAAE;QACrC,OAAO,KAAK;MACd;MACA,IAAIH,IAAI,CAACtB,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;QAC7B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IAED,OAAOP,YAAY;EAErB,CAAC,CAAE,CAAC;AAEN,CAAC,EAAEiC,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
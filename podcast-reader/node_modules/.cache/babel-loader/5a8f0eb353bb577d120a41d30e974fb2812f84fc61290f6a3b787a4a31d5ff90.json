{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeCard.js\";\nimport React from 'react';\nimport { formatDate, formatDuration } from '../utils/rssParser';\nimport './EpisodeCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EpisodeCard = ({\n  episode,\n  onSelect,\n  onShowDetail,\n  isActive\n}) => {\n  const handleClick = () => {\n    if (onShowDetail) {\n      onShowDetail(episode);\n    }\n  };\n  const handlePlayClick = e => {\n    e.stopPropagation();\n    onSelect(episode);\n  };\n\n  // 截取描述文本\n  const truncateDescription = (text, maxLength = 200) => {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `episode-card ${isActive ? 'active' : ''}`,\n    onClick: handleClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"episode-card-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"episode-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"episode-title\",\n          children: episode.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"episode-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"episode-date\",\n            children: formatDate(episode.pubDate)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), episode.duration && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"episode-duration\",\n            children: formatDuration(episode.duration)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"episode-description\",\n        children: truncateDescription(episode.description)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"episode-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"play-button\",\n          onClick: handlePlayClick,\n          disabled: !episode.audioUrl,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8 5v14l11-7z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), \"\\u64AD\\u653E\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), episode.link && /*#__PURE__*/_jsxDEV(\"a\", {\n          href: episode.link,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"episode-link\",\n          onClick: e => e.stopPropagation(),\n          children: \"\\u67E5\\u770B\\u539F\\u6587\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_c = EpisodeCard;\nexport default EpisodeCard;\nvar _c;\n$RefreshReg$(_c, \"EpisodeCard\");", "map": {"version": 3, "names": ["React", "formatDate", "formatDuration", "jsxDEV", "_jsxDEV", "EpisodeCard", "episode", "onSelect", "onShowDetail", "isActive", "handleClick", "handlePlayClick", "e", "stopPropagation", "truncateDescription", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "className", "onClick", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pubDate", "duration", "description", "disabled", "audioUrl", "width", "height", "viewBox", "fill", "d", "link", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeCard.js"], "sourcesContent": ["import React from 'react';\nimport { formatDate, formatDuration } from '../utils/rssParser';\nimport './EpisodeCard.css';\n\nconst EpisodeCard = ({ episode, onSelect, onShowDetail, isActive }) => {\n  const handleClick = () => {\n    if (onShowDetail) {\n      onShowDetail(episode);\n    }\n  };\n\n  const handlePlayClick = (e) => {\n    e.stopPropagation();\n    onSelect(episode);\n  };\n\n  // 截取描述文本\n  const truncateDescription = (text, maxLength = 200) => {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  return (\n    <div\n      className={`episode-card ${isActive ? 'active' : ''}`}\n      onClick={handleClick}\n    >\n      <div className=\"episode-card-content\">\n        <div className=\"episode-header\">\n          <h3 className=\"episode-title\">{episode.title}</h3>\n          <div className=\"episode-meta\">\n            <span className=\"episode-date\">\n              {formatDate(episode.pubDate)}\n            </span>\n            {episode.duration && (\n              <span className=\"episode-duration\">\n                {formatDuration(episode.duration)}\n              </span>\n            )}\n          </div>\n        </div>\n\n        <p className=\"episode-description\">\n          {truncateDescription(episode.description)}\n        </p>\n\n        <div className=\"episode-actions\">\n          <button\n            className=\"play-button\"\n            onClick={handlePlayClick}\n            disabled={!episode.audioUrl}\n          >\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M8 5v14l11-7z\"/>\n            </svg>\n            播放\n          </button>\n\n          {episode.link && (\n            <a\n              href={episode.link}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"episode-link\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              查看原文\n            </a>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EpisodeCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,cAAc,QAAQ,oBAAoB;AAC/D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,YAAY;EAAEC;AAAS,CAAC,KAAK;EACrE,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACF,OAAO,CAAC;IACvB;EACF,CAAC;EAED,MAAMK,eAAe,GAAIC,CAAC,IAAK;IAC7BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBN,QAAQ,CAACD,OAAO,CAAC;EACnB,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,GAAG,KAAK;IACrD,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;IACpB,IAAIA,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EAED,oBACEZ,OAAA;IACEe,SAAS,EAAE,gBAAgBV,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;IACtDW,OAAO,EAAEV,WAAY;IAAAW,QAAA,eAErBjB,OAAA;MAAKe,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACnCjB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BjB,OAAA;UAAIe,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAEf,OAAO,CAACgB;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDtB,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAAE,QAAA,gBAC3BjB,OAAA;YAAMe,SAAS,EAAC,cAAc;YAAAE,QAAA,EAC3BpB,UAAU,CAACK,OAAO,CAACqB,OAAO;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EACNpB,OAAO,CAACsB,QAAQ,iBACfxB,OAAA;YAAMe,SAAS,EAAC,kBAAkB;YAAAE,QAAA,EAC/BnB,cAAc,CAACI,OAAO,CAACsB,QAAQ;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtB,OAAA;QAAGe,SAAS,EAAC,qBAAqB;QAAAE,QAAA,EAC/BP,mBAAmB,CAACR,OAAO,CAACuB,WAAW;MAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAEJtB,OAAA;QAAKe,SAAS,EAAC,iBAAiB;QAAAE,QAAA,gBAC9BjB,OAAA;UACEe,SAAS,EAAC,aAAa;UACvBC,OAAO,EAAET,eAAgB;UACzBmB,QAAQ,EAAE,CAACxB,OAAO,CAACyB,QAAS;UAAAV,QAAA,gBAE5BjB,OAAA;YAAK4B,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAd,QAAA,eACjEjB,OAAA;cAAMgC,CAAC,EAAC;YAAe;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,gBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERpB,OAAO,CAAC+B,IAAI,iBACXjC,OAAA;UACEkC,IAAI,EAAEhC,OAAO,CAAC+B,IAAK;UACnBE,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBrB,SAAS,EAAC,cAAc;UACxBC,OAAO,EAAGR,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;UAAAQ,QAAA,EACrC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACe,EAAA,GAtEIpC,WAAW;AAwEjB,eAAeA,WAAW;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
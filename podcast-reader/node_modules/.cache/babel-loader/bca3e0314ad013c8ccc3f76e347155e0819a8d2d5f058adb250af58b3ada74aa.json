{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var XMLNodeList;\n  module.exports = XMLNodeList = function () {\n    function XMLNodeList(nodes) {\n      this.nodes = nodes;\n    }\n    Object.defineProperty(XMLNodeList.prototype, 'length', {\n      get: function () {\n        return this.nodes.length || 0;\n      }\n    });\n    XMLNodeList.prototype.clone = function () {\n      return this.nodes = null;\n    };\n    XMLNodeList.prototype.item = function (index) {\n      return this.nodes[index] || null;\n    };\n    return XMLNodeList;\n  }();\n}).call(this);", "map": {"version": 3, "names": ["XMLNodeList", "module", "exports", "nodes", "Object", "defineProperty", "prototype", "get", "length", "clone", "item", "index", "call"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xmlbuilder/lib/XMLNodeList.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNodeList;\n\n  module.exports = XMLNodeList = (function() {\n    function XMLNodeList(nodes) {\n      this.nodes = nodes;\n    }\n\n    Object.defineProperty(XMLNodeList.prototype, 'length', {\n      get: function() {\n        return this.nodes.length || 0;\n      }\n    });\n\n    XMLNodeList.prototype.clone = function() {\n      return this.nodes = null;\n    };\n\n    XMLNodeList.prototype.item = function(index) {\n      return this.nodes[index] || null;\n    };\n\n    return XMLNodeList;\n\n  })();\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,WAAW;EAEfC,MAAM,CAACC,OAAO,GAAGF,WAAW,GAAI,YAAW;IACzC,SAASA,WAAWA,CAACG,KAAK,EAAE;MAC1B,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB;IAEAC,MAAM,CAACC,cAAc,CAACL,WAAW,CAACM,SAAS,EAAE,QAAQ,EAAE;MACrDC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACJ,KAAK,CAACK,MAAM,IAAI,CAAC;MAC/B;IACF,CAAC,CAAC;IAEFR,WAAW,CAACM,SAAS,CAACG,KAAK,GAAG,YAAW;MACvC,OAAO,IAAI,CAACN,KAAK,GAAG,IAAI;IAC1B,CAAC;IAEDH,WAAW,CAACM,SAAS,CAACI,IAAI,GAAG,UAASC,KAAK,EAAE;MAC3C,OAAO,IAAI,CAACR,KAAK,CAACQ,KAAK,CAAC,IAAI,IAAI;IAClC,CAAC;IAED,OAAOX,WAAW;EAEpB,CAAC,CAAE,CAAC;AAEN,CAAC,EAAEY,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  \"use strict\";\n\n  exports.stripBOM = function (str) {\n    if (str[0] === '\\uFEFF') {\n      return str.substring(1);\n    } else {\n      return str;\n    }\n  };\n}).call(this);", "map": {"version": 3, "names": ["exports", "stripBOM", "str", "substring", "call"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xml2js/lib/bom.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  \"use strict\";\n  exports.stripBOM = function(str) {\n    if (str[0] === '\\uFEFF') {\n      return str.substring(1);\n    } else {\n      return str;\n    }\n  };\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,YAAY;;EACZA,OAAO,CAACC,QAAQ,GAAG,UAASC,GAAG,EAAE;IAC/B,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACvB,OAAOA,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM;MACL,OAAOD,GAAG;IACZ;EACF,CAAC;AAEH,CAAC,EAAEE,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "module.exports = function () {\n  throw new Error('Readable.from is not available in the browser');\n};", "map": {"version": 3, "names": ["module", "exports", "Error"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/readable-stream/lib/internal/streams/from-browser.js"], "sourcesContent": ["module.exports = function () {\n  throw new Error('Readable.from is not available in the browser')\n};\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;AAClE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "import axios from 'axios';\n\n// 由于CORS限制，我们需要使用代理服务或者在开发环境中处理\nconst CORS_PROXY = 'https://cors-anywhere.herokuapp.com/';\nexport const parseRSSFeed = async rssUrl => {\n  try {\n    // 在实际应用中，你可能需要设置自己的CORS代理\n    const response = await axios.get(`${CORS_PROXY}${rssUrl}`);\n\n    // 使用浏览器原生的DOMParser解析XML\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(response.data, 'text/xml');\n\n    // 检查解析错误\n    const parseError = xmlDoc.querySelector('parsererror');\n    if (parseError) {\n      throw new Error('XML parsing error');\n    }\n    const channel = xmlDoc.querySelector('channel');\n    if (!channel) {\n      throw new Error('Invalid RSS format');\n    }\n\n    // 提取播客信息\n    const podcastInfo = {\n      title: getTextContent(channel, 'title') || 'Unknown Podcast',\n      description: getTextContent(channel, 'description') || '',\n      language: getTextContent(channel, 'language') || 'zh-cn',\n      image: getImageUrl(channel),\n      link: getTextContent(channel, 'link') || '',\n      lastBuildDate: getTextContent(channel, 'lastBuildDate') || null\n    };\n\n    // 提取节目列表\n    const items = Array.from(xmlDoc.querySelectorAll('item'));\n    const episodes = items.map((item, index) => {\n      // 提取音频链接\n      const enclosure = item.querySelector('enclosure');\n      const audioUrl = enclosure ? enclosure.getAttribute('url') : null;\n\n      // 提取时长\n      let duration = getTextContent(item, 'itunes\\\\:duration') || (enclosure ? enclosure.getAttribute('itunes:duration') : null);\n      return {\n        id: `episode-${index}`,\n        title: stripHtml(getTextContent(item, 'title') || `Episode ${index + 1}`),\n        description: stripHtml(getTextContent(item, 'description') || ''),\n        pubDate: getTextContent(item, 'pubDate') || null,\n        audioUrl: audioUrl,\n        duration: duration,\n        link: getTextContent(item, 'link') || null,\n        guid: getTextContent(item, 'guid') || `episode-${index}`\n      };\n    });\n    return {\n      podcastInfo,\n      episodes\n    };\n  } catch (error) {\n    console.error('Error fetching RSS feed:', error);\n    throw error;\n  }\n};\n\n// 辅助函数：获取元素文本内容\nconst getTextContent = (parent, selector) => {\n  const element = parent.querySelector(selector);\n  return element ? element.textContent.trim() : null;\n};\n\n// 辅助函数：获取图片URL\nconst getImageUrl = channel => {\n  const image = channel.querySelector('image url');\n  if (image) return image.textContent.trim();\n  const itunesImage = channel.querySelector('itunes\\\\:image');\n  if (itunesImage) return itunesImage.getAttribute('href');\n  return null;\n};\n\n// 清理HTML标签的简单函数\nconst stripHtml = html => {\n  if (!html) return '';\n  return html.replace(/<[^>]*>/g, '').trim();\n};\n\n// 格式化时长显示\nexport const formatDuration = duration => {\n  if (!duration) return '';\n\n  // 如果是 HH:MM:SS 格式\n  if (duration.includes(':')) {\n    return duration;\n  }\n\n  // 如果是秒数\n  const seconds = parseInt(duration);\n  if (isNaN(seconds)) return duration;\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor(seconds % 3600 / 60);\n  const remainingSeconds = seconds % 60;\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  } else {\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n};\n\n// 格式化日期显示\nexport const formatDate = dateString => {\n  if (!dateString) return '';\n  try {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  } catch (error) {\n    return dateString;\n  }\n};", "map": {"version": 3, "names": ["axios", "CORS_PROXY", "parseRSSFeed", "rssUrl", "response", "get", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlDoc", "parseFromString", "data", "parseError", "querySelector", "Error", "channel", "podcastInfo", "title", "getTextContent", "description", "language", "image", "getImageUrl", "link", "lastBuildDate", "items", "Array", "from", "querySelectorAll", "episodes", "map", "item", "index", "enclosure", "audioUrl", "getAttribute", "duration", "id", "stripHtml", "pubDate", "guid", "error", "console", "parent", "selector", "element", "textContent", "trim", "itunesImage", "html", "replace", "formatDuration", "includes", "seconds", "parseInt", "isNaN", "hours", "Math", "floor", "minutes", "remainingSeconds", "toString", "padStart", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/utils/rssParser.js"], "sourcesContent": ["import axios from 'axios';\n\n// 由于CORS限制，我们需要使用代理服务或者在开发环境中处理\nconst CORS_PROXY = 'https://cors-anywhere.herokuapp.com/';\n\nexport const parseRSSFeed = async (rssUrl) => {\n  try {\n    // 在实际应用中，你可能需要设置自己的CORS代理\n    const response = await axios.get(`${CORS_PROXY}${rssUrl}`);\n\n    // 使用浏览器原生的DOMParser解析XML\n    const parser = new DOMParser();\n    const xmlDoc = parser.parseFromString(response.data, 'text/xml');\n\n    // 检查解析错误\n    const parseError = xmlDoc.querySelector('parsererror');\n    if (parseError) {\n      throw new Error('XML parsing error');\n    }\n\n    const channel = xmlDoc.querySelector('channel');\n    if (!channel) {\n      throw new Error('Invalid RSS format');\n    }\n\n    // 提取播客信息\n    const podcastInfo = {\n      title: getTextContent(channel, 'title') || 'Unknown Podcast',\n      description: getTextContent(channel, 'description') || '',\n      language: getTextContent(channel, 'language') || 'zh-cn',\n      image: getImageUrl(channel),\n      link: getTextContent(channel, 'link') || '',\n      lastBuildDate: getTextContent(channel, 'lastBuildDate') || null,\n    };\n\n    // 提取节目列表\n    const items = Array.from(xmlDoc.querySelectorAll('item'));\n    const episodes = items.map((item, index) => {\n      // 提取音频链接\n      const enclosure = item.querySelector('enclosure');\n      const audioUrl = enclosure ? enclosure.getAttribute('url') : null;\n\n      // 提取时长\n      let duration = getTextContent(item, 'itunes\\\\:duration') ||\n                    (enclosure ? enclosure.getAttribute('itunes:duration') : null);\n\n      return {\n        id: `episode-${index}`,\n        title: stripHtml(getTextContent(item, 'title') || `Episode ${index + 1}`),\n        description: stripHtml(getTextContent(item, 'description') || ''),\n        pubDate: getTextContent(item, 'pubDate') || null,\n        audioUrl: audioUrl,\n        duration: duration,\n        link: getTextContent(item, 'link') || null,\n        guid: getTextContent(item, 'guid') || `episode-${index}`,\n      };\n    });\n\n    return {\n      podcastInfo,\n      episodes\n    };\n  } catch (error) {\n    console.error('Error fetching RSS feed:', error);\n    throw error;\n  }\n};\n\n// 辅助函数：获取元素文本内容\nconst getTextContent = (parent, selector) => {\n  const element = parent.querySelector(selector);\n  return element ? element.textContent.trim() : null;\n};\n\n// 辅助函数：获取图片URL\nconst getImageUrl = (channel) => {\n  const image = channel.querySelector('image url');\n  if (image) return image.textContent.trim();\n\n  const itunesImage = channel.querySelector('itunes\\\\:image');\n  if (itunesImage) return itunesImage.getAttribute('href');\n\n  return null;\n};\n\n// 清理HTML标签的简单函数\nconst stripHtml = (html) => {\n  if (!html) return '';\n  return html.replace(/<[^>]*>/g, '').trim();\n};\n\n// 格式化时长显示\nexport const formatDuration = (duration) => {\n  if (!duration) return '';\n\n  // 如果是 HH:MM:SS 格式\n  if (duration.includes(':')) {\n    return duration;\n  }\n\n  // 如果是秒数\n  const seconds = parseInt(duration);\n  if (isNaN(seconds)) return duration;\n\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const remainingSeconds = seconds % 60;\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  } else {\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n};\n\n// 格式化日期显示\nexport const formatDate = (dateString) => {\n  if (!dateString) return '';\n\n  try {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  } catch (error) {\n    return dateString;\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,UAAU,GAAG,sCAAsC;AAEzD,OAAO,MAAMC,YAAY,GAAG,MAAOC,MAAM,IAAK;EAC5C,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,MAAMJ,KAAK,CAACK,GAAG,CAAC,GAAGJ,UAAU,GAAGE,MAAM,EAAE,CAAC;;IAE1D;IACA,MAAMG,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;IAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACL,QAAQ,CAACM,IAAI,EAAE,UAAU,CAAC;;IAEhE;IACA,MAAMC,UAAU,GAAGH,MAAM,CAACI,aAAa,CAAC,aAAa,CAAC;IACtD,IAAID,UAAU,EAAE;MACd,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IACtC;IAEA,MAAMC,OAAO,GAAGN,MAAM,CAACI,aAAa,CAAC,SAAS,CAAC;IAC/C,IAAI,CAACE,OAAO,EAAE;MACZ,MAAM,IAAID,KAAK,CAAC,oBAAoB,CAAC;IACvC;;IAEA;IACA,MAAME,WAAW,GAAG;MAClBC,KAAK,EAAEC,cAAc,CAACH,OAAO,EAAE,OAAO,CAAC,IAAI,iBAAiB;MAC5DI,WAAW,EAAED,cAAc,CAACH,OAAO,EAAE,aAAa,CAAC,IAAI,EAAE;MACzDK,QAAQ,EAAEF,cAAc,CAACH,OAAO,EAAE,UAAU,CAAC,IAAI,OAAO;MACxDM,KAAK,EAAEC,WAAW,CAACP,OAAO,CAAC;MAC3BQ,IAAI,EAAEL,cAAc,CAACH,OAAO,EAAE,MAAM,CAAC,IAAI,EAAE;MAC3CS,aAAa,EAAEN,cAAc,CAACH,OAAO,EAAE,eAAe,CAAC,IAAI;IAC7D,CAAC;;IAED;IACA,MAAMU,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAClB,MAAM,CAACmB,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACzD,MAAMC,QAAQ,GAAGJ,KAAK,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MAC1C;MACA,MAAMC,SAAS,GAAGF,IAAI,CAAClB,aAAa,CAAC,WAAW,CAAC;MACjD,MAAMqB,QAAQ,GAAGD,SAAS,GAAGA,SAAS,CAACE,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI;;MAEjE;MACA,IAAIC,QAAQ,GAAGlB,cAAc,CAACa,IAAI,EAAE,mBAAmB,CAAC,KACzCE,SAAS,GAAGA,SAAS,CAACE,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;MAE5E,OAAO;QACLE,EAAE,EAAE,WAAWL,KAAK,EAAE;QACtBf,KAAK,EAAEqB,SAAS,CAACpB,cAAc,CAACa,IAAI,EAAE,OAAO,CAAC,IAAI,WAAWC,KAAK,GAAG,CAAC,EAAE,CAAC;QACzEb,WAAW,EAAEmB,SAAS,CAACpB,cAAc,CAACa,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC;QACjEQ,OAAO,EAAErB,cAAc,CAACa,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI;QAChDG,QAAQ,EAAEA,QAAQ;QAClBE,QAAQ,EAAEA,QAAQ;QAClBb,IAAI,EAAEL,cAAc,CAACa,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI;QAC1CS,IAAI,EAAEtB,cAAc,CAACa,IAAI,EAAE,MAAM,CAAC,IAAI,WAAWC,KAAK;MACxD,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MACLhB,WAAW;MACXa;IACF,CAAC;EACH,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,MAAMvB,cAAc,GAAGA,CAACyB,MAAM,EAAEC,QAAQ,KAAK;EAC3C,MAAMC,OAAO,GAAGF,MAAM,CAAC9B,aAAa,CAAC+B,QAAQ,CAAC;EAC9C,OAAOC,OAAO,GAAGA,OAAO,CAACC,WAAW,CAACC,IAAI,CAAC,CAAC,GAAG,IAAI;AACpD,CAAC;;AAED;AACA,MAAMzB,WAAW,GAAIP,OAAO,IAAK;EAC/B,MAAMM,KAAK,GAAGN,OAAO,CAACF,aAAa,CAAC,WAAW,CAAC;EAChD,IAAIQ,KAAK,EAAE,OAAOA,KAAK,CAACyB,WAAW,CAACC,IAAI,CAAC,CAAC;EAE1C,MAAMC,WAAW,GAAGjC,OAAO,CAACF,aAAa,CAAC,gBAAgB,CAAC;EAC3D,IAAImC,WAAW,EAAE,OAAOA,WAAW,CAACb,YAAY,CAAC,MAAM,CAAC;EAExD,OAAO,IAAI;AACb,CAAC;;AAED;AACA,MAAMG,SAAS,GAAIW,IAAI,IAAK;EAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,OAAOA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACH,IAAI,CAAC,CAAC;AAC5C,CAAC;;AAED;AACA,OAAO,MAAMI,cAAc,GAAIf,QAAQ,IAAK;EAC1C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;;EAExB;EACA,IAAIA,QAAQ,CAACgB,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC1B,OAAOhB,QAAQ;EACjB;;EAEA;EACA,MAAMiB,OAAO,GAAGC,QAAQ,CAAClB,QAAQ,CAAC;EAClC,IAAImB,KAAK,CAACF,OAAO,CAAC,EAAE,OAAOjB,QAAQ;EAEnC,MAAMoB,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,GAAG,IAAI,CAAC;EACxC,MAAMM,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEL,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;EACjD,MAAMO,gBAAgB,GAAGP,OAAO,GAAG,EAAE;EAErC,IAAIG,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,GAAGA,KAAK,IAAIG,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC1G,CAAC,MAAM;IACL,OAAO,GAAGH,OAAO,IAAIC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAIC,UAAU,IAAK;EACxC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAE1B,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC,CAAC,OAAO7B,KAAK,EAAE;IACd,OAAOuB,UAAU;EACnB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
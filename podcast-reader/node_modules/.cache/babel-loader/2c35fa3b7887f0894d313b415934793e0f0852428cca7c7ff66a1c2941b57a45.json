{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nmodule.exports = Readable;\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nReadable.ReadableState = ReadableState;\n\n/*<replacement>*/\nvar EE = require('events').EventEmitter;\nvar EElistenerCount = function EElistenerCount(emitter, type) {\n  return emitter.listeners(type).length;\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\nvar Buffer = require('buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\n/*<replacement>*/\nvar debugUtil = require('util');\nvar debug;\nif (debugUtil && debugUtil.debuglog) {\n  debug = debugUtil.debuglog('stream');\n} else {\n  debug = function debug() {};\n}\n/*</replacement>*/\n\nvar BufferList = require('./internal/streams/buffer_list');\nvar destroyImpl = require('./internal/streams/destroy');\nvar _require = require('./internal/streams/state'),\n  getHighWaterMark = _require.getHighWaterMark;\nvar _require$codes = require('../errors').codes,\n  ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n  ERR_STREAM_PUSH_AFTER_EOF = _require$codes.ERR_STREAM_PUSH_AFTER_EOF,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_STREAM_UNSHIFT_AFTER_END_EVENT = _require$codes.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;\n\n// Lazy loaded to improve the startup performance.\nvar StringDecoder;\nvar createReadableStreamAsyncIterator;\nvar from;\nrequire('inherits')(Readable, Stream);\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\nvar kProxyEvents = ['error', 'close', 'destroy', 'pause', 'resume'];\nfunction prependListener(emitter, event, fn) {\n  // Sadly this is not cacheable as some libraries bundle their own\n  // event emitter implementation with them.\n  if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn);\n\n  // This is a hack to make sure that our error handler is attached before any\n  // userland ones.  NEVER DO THIS. This is here only because this code needs\n  // to continue to work with older versions of Node.js that do not include\n  // the prependListener() method. The goal is to eventually remove this hack.\n  if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);else if (Array.isArray(emitter._events[event])) emitter._events[event].unshift(fn);else emitter._events[event] = [fn, emitter._events[event]];\n}\nfunction ReadableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex;\n\n  // object stream flag. Used to make read(n) ignore n and to\n  // make all the buffer merging and length checks go away\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode;\n\n  // the point at which it stops calling _read() to fill the buffer\n  // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n  this.highWaterMark = getHighWaterMark(this, options, 'readableHighWaterMark', isDuplex);\n\n  // A linked list is used to store data chunks instead of an array because the\n  // linked list can remove elements from the beginning faster than\n  // array.shift()\n  this.buffer = new BufferList();\n  this.length = 0;\n  this.pipes = null;\n  this.pipesCount = 0;\n  this.flowing = null;\n  this.ended = false;\n  this.endEmitted = false;\n  this.reading = false;\n\n  // a flag to be able to tell if the event 'readable'/'data' is emitted\n  // immediately, or on a later tick.  We set this to true at first, because\n  // any actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first read call.\n  this.sync = true;\n\n  // whenever we return null, then we set a flag to say\n  // that we're awaiting a 'readable' event emission.\n  this.needReadable = false;\n  this.emittedReadable = false;\n  this.readableListening = false;\n  this.resumeScheduled = false;\n  this.paused = true;\n\n  // Should close be emitted on destroy. Defaults to true.\n  this.emitClose = options.emitClose !== false;\n\n  // Should .destroy() be called after 'end' (and potentially 'finish')\n  this.autoDestroy = !!options.autoDestroy;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // the number of writers that are awaiting a drain event in .pipe()s\n  this.awaitDrain = 0;\n\n  // if true, a maybeReadMore has been scheduled\n  this.readingMore = false;\n  this.decoder = null;\n  this.encoding = null;\n  if (options.encoding) {\n    if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n    this.decoder = new StringDecoder(options.encoding);\n    this.encoding = options.encoding;\n  }\n}\nfunction Readable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n  if (!(this instanceof Readable)) return new Readable(options);\n\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the ReadableState constructor, at least with V8 6.5\n  var isDuplex = this instanceof Duplex;\n  this._readableState = new ReadableState(options, this, isDuplex);\n\n  // legacy\n  this.readable = true;\n  if (options) {\n    if (typeof options.read === 'function') this._read = options.read;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n  }\n  Stream.call(this);\n}\nObject.defineProperty(Readable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._readableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n  }\n});\nReadable.prototype.destroy = destroyImpl.destroy;\nReadable.prototype._undestroy = destroyImpl.undestroy;\nReadable.prototype._destroy = function (err, cb) {\n  cb(err);\n};\n\n// Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\nReadable.prototype.push = function (chunk, encoding) {\n  var state = this._readableState;\n  var skipChunkCheck;\n  if (!state.objectMode) {\n    if (typeof chunk === 'string') {\n      encoding = encoding || state.defaultEncoding;\n      if (encoding !== state.encoding) {\n        chunk = Buffer.from(chunk, encoding);\n        encoding = '';\n      }\n      skipChunkCheck = true;\n    }\n  } else {\n    skipChunkCheck = true;\n  }\n  return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);\n};\n\n// Unshift should *always* be something directly out of read()\nReadable.prototype.unshift = function (chunk) {\n  return readableAddChunk(this, chunk, null, true, false);\n};\nfunction readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {\n  debug('readableAddChunk', chunk);\n  var state = stream._readableState;\n  if (chunk === null) {\n    state.reading = false;\n    onEofChunk(stream, state);\n  } else {\n    var er;\n    if (!skipChunkCheck) er = chunkInvalid(state, chunk);\n    if (er) {\n      errorOrDestroy(stream, er);\n    } else if (state.objectMode || chunk && chunk.length > 0) {\n      if (typeof chunk !== 'string' && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {\n        chunk = _uint8ArrayToBuffer(chunk);\n      }\n      if (addToFront) {\n        if (state.endEmitted) errorOrDestroy(stream, new ERR_STREAM_UNSHIFT_AFTER_END_EVENT());else addChunk(stream, state, chunk, true);\n      } else if (state.ended) {\n        errorOrDestroy(stream, new ERR_STREAM_PUSH_AFTER_EOF());\n      } else if (state.destroyed) {\n        return false;\n      } else {\n        state.reading = false;\n        if (state.decoder && !encoding) {\n          chunk = state.decoder.write(chunk);\n          if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);else maybeReadMore(stream, state);\n        } else {\n          addChunk(stream, state, chunk, false);\n        }\n      }\n    } else if (!addToFront) {\n      state.reading = false;\n      maybeReadMore(stream, state);\n    }\n  }\n\n  // We can push more data if we are below the highWaterMark.\n  // Also, if we have no data yet, we can stand some more bytes.\n  // This is to work around cases where hwm=0, such as the repl.\n  return !state.ended && (state.length < state.highWaterMark || state.length === 0);\n}\nfunction addChunk(stream, state, chunk, addToFront) {\n  if (state.flowing && state.length === 0 && !state.sync) {\n    state.awaitDrain = 0;\n    stream.emit('data', chunk);\n  } else {\n    // update the buffer info.\n    state.length += state.objectMode ? 1 : chunk.length;\n    if (addToFront) state.buffer.unshift(chunk);else state.buffer.push(chunk);\n    if (state.needReadable) emitReadable(stream);\n  }\n  maybeReadMore(stream, state);\n}\nfunction chunkInvalid(state, chunk) {\n  var er;\n  if (!_isUint8Array(chunk) && typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer', 'Uint8Array'], chunk);\n  }\n  return er;\n}\nReadable.prototype.isPaused = function () {\n  return this._readableState.flowing === false;\n};\n\n// backwards compatibility.\nReadable.prototype.setEncoding = function (enc) {\n  if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n  var decoder = new StringDecoder(enc);\n  this._readableState.decoder = decoder;\n  // If setEncoding(null), decoder.encoding equals utf8\n  this._readableState.encoding = this._readableState.decoder.encoding;\n\n  // Iterate over current buffer to convert already stored Buffers:\n  var p = this._readableState.buffer.head;\n  var content = '';\n  while (p !== null) {\n    content += decoder.write(p.data);\n    p = p.next;\n  }\n  this._readableState.buffer.clear();\n  if (content !== '') this._readableState.buffer.push(content);\n  this._readableState.length = content.length;\n  return this;\n};\n\n// Don't raise the hwm > 1GB\nvar MAX_HWM = 0x40000000;\nfunction computeNewHighWaterMark(n) {\n  if (n >= MAX_HWM) {\n    // TODO(ronag): Throw ERR_VALUE_OUT_OF_RANGE.\n    n = MAX_HWM;\n  } else {\n    // Get the next highest power of 2 to prevent increasing hwm excessively in\n    // tiny amounts\n    n--;\n    n |= n >>> 1;\n    n |= n >>> 2;\n    n |= n >>> 4;\n    n |= n >>> 8;\n    n |= n >>> 16;\n    n++;\n  }\n  return n;\n}\n\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction howMuchToRead(n, state) {\n  if (n <= 0 || state.length === 0 && state.ended) return 0;\n  if (state.objectMode) return 1;\n  if (n !== n) {\n    // Only flow one buffer at a time\n    if (state.flowing && state.length) return state.buffer.head.data.length;else return state.length;\n  }\n  // If we're asking for more than the current hwm, then raise the hwm.\n  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);\n  if (n <= state.length) return n;\n  // Don't have enough\n  if (!state.ended) {\n    state.needReadable = true;\n    return 0;\n  }\n  return state.length;\n}\n\n// you can override either this method, or the async _read(n) below.\nReadable.prototype.read = function (n) {\n  debug('read', n);\n  n = parseInt(n, 10);\n  var state = this._readableState;\n  var nOrig = n;\n  if (n !== 0) state.emittedReadable = false;\n\n  // if we're doing read(0) to trigger a readable event, but we\n  // already have a bunch of data in the buffer, then just trigger\n  // the 'readable' event and move on.\n  if (n === 0 && state.needReadable && ((state.highWaterMark !== 0 ? state.length >= state.highWaterMark : state.length > 0) || state.ended)) {\n    debug('read: emitReadable', state.length, state.ended);\n    if (state.length === 0 && state.ended) endReadable(this);else emitReadable(this);\n    return null;\n  }\n  n = howMuchToRead(n, state);\n\n  // if we've ended, and we're now clear, then finish it up.\n  if (n === 0 && state.ended) {\n    if (state.length === 0) endReadable(this);\n    return null;\n  }\n\n  // All the actual chunk generation logic needs to be\n  // *below* the call to _read.  The reason is that in certain\n  // synthetic stream cases, such as passthrough streams, _read\n  // may be a completely synchronous operation which may change\n  // the state of the read buffer, providing enough data when\n  // before there was *not* enough.\n  //\n  // So, the steps are:\n  // 1. Figure out what the state of things will be after we do\n  // a read from the buffer.\n  //\n  // 2. If that resulting state will trigger a _read, then call _read.\n  // Note that this may be asynchronous, or synchronous.  Yes, it is\n  // deeply ugly to write APIs this way, but that still doesn't mean\n  // that the Readable class should behave improperly, as streams are\n  // designed to be sync/async agnostic.\n  // Take note if the _read call is sync or async (ie, if the read call\n  // has returned yet), so that we know whether or not it's safe to emit\n  // 'readable' etc.\n  //\n  // 3. Actually pull the requested chunks out of the buffer and return.\n\n  // if we need a readable event, then we need to do some reading.\n  var doRead = state.needReadable;\n  debug('need readable', doRead);\n\n  // if we currently have less than the highWaterMark, then also read some\n  if (state.length === 0 || state.length - n < state.highWaterMark) {\n    doRead = true;\n    debug('length less than watermark', doRead);\n  }\n\n  // however, if we've ended, then there's no point, and if we're already\n  // reading, then it's unnecessary.\n  if (state.ended || state.reading) {\n    doRead = false;\n    debug('reading or ended', doRead);\n  } else if (doRead) {\n    debug('do read');\n    state.reading = true;\n    state.sync = true;\n    // if the length is currently zero, then we *need* a readable event.\n    if (state.length === 0) state.needReadable = true;\n    // call internal read method\n    this._read(state.highWaterMark);\n    state.sync = false;\n    // If _read pushed data synchronously, then `reading` will be false,\n    // and we need to re-evaluate how much data we can return to the user.\n    if (!state.reading) n = howMuchToRead(nOrig, state);\n  }\n  var ret;\n  if (n > 0) ret = fromList(n, state);else ret = null;\n  if (ret === null) {\n    state.needReadable = state.length <= state.highWaterMark;\n    n = 0;\n  } else {\n    state.length -= n;\n    state.awaitDrain = 0;\n  }\n  if (state.length === 0) {\n    // If we have nothing in the buffer, then we want to know\n    // as soon as we *do* get something into the buffer.\n    if (!state.ended) state.needReadable = true;\n\n    // If we tried to read() past the EOF, then emit end on the next tick.\n    if (nOrig !== n && state.ended) endReadable(this);\n  }\n  if (ret !== null) this.emit('data', ret);\n  return ret;\n};\nfunction onEofChunk(stream, state) {\n  debug('onEofChunk');\n  if (state.ended) return;\n  if (state.decoder) {\n    var chunk = state.decoder.end();\n    if (chunk && chunk.length) {\n      state.buffer.push(chunk);\n      state.length += state.objectMode ? 1 : chunk.length;\n    }\n  }\n  state.ended = true;\n  if (state.sync) {\n    // if we are sync, wait until next tick to emit the data.\n    // Otherwise we risk emitting data in the flow()\n    // the readable code triggers during a read() call\n    emitReadable(stream);\n  } else {\n    // emit 'readable' now to make sure it gets picked up.\n    state.needReadable = false;\n    if (!state.emittedReadable) {\n      state.emittedReadable = true;\n      emitReadable_(stream);\n    }\n  }\n}\n\n// Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\nfunction emitReadable(stream) {\n  var state = stream._readableState;\n  debug('emitReadable', state.needReadable, state.emittedReadable);\n  state.needReadable = false;\n  if (!state.emittedReadable) {\n    debug('emitReadable', state.flowing);\n    state.emittedReadable = true;\n    process.nextTick(emitReadable_, stream);\n  }\n}\nfunction emitReadable_(stream) {\n  var state = stream._readableState;\n  debug('emitReadable_', state.destroyed, state.length, state.ended);\n  if (!state.destroyed && (state.length || state.ended)) {\n    stream.emit('readable');\n    state.emittedReadable = false;\n  }\n\n  // The stream needs another readable event if\n  // 1. It is not flowing, as the flow mechanism will take\n  //    care of it.\n  // 2. It is not ended.\n  // 3. It is below the highWaterMark, so we can schedule\n  //    another readable later.\n  state.needReadable = !state.flowing && !state.ended && state.length <= state.highWaterMark;\n  flow(stream);\n}\n\n// at this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\nfunction maybeReadMore(stream, state) {\n  if (!state.readingMore) {\n    state.readingMore = true;\n    process.nextTick(maybeReadMore_, stream, state);\n  }\n}\nfunction maybeReadMore_(stream, state) {\n  // Attempt to read more data if we should.\n  //\n  // The conditions for reading more data are (one of):\n  // - Not enough data buffered (state.length < state.highWaterMark). The loop\n  //   is responsible for filling the buffer with enough data if such data\n  //   is available. If highWaterMark is 0 and we are not in the flowing mode\n  //   we should _not_ attempt to buffer any extra data. We'll get more data\n  //   when the stream consumer calls read() instead.\n  // - No data in the buffer, and the stream is in flowing mode. In this mode\n  //   the loop below is responsible for ensuring read() is called. Failing to\n  //   call read here would abort the flow and there's no other mechanism for\n  //   continuing the flow if the stream consumer has just subscribed to the\n  //   'data' event.\n  //\n  // In addition to the above conditions to keep reading data, the following\n  // conditions prevent the data from being read:\n  // - The stream has ended (state.ended).\n  // - There is already a pending 'read' operation (state.reading). This is a\n  //   case where the the stream has called the implementation defined _read()\n  //   method, but they are processing the call asynchronously and have _not_\n  //   called push() with new data. In this case we skip performing more\n  //   read()s. The execution ends in this method again after the _read() ends\n  //   up calling push() with more data.\n  while (!state.reading && !state.ended && (state.length < state.highWaterMark || state.flowing && state.length === 0)) {\n    var len = state.length;\n    debug('maybeReadMore read 0');\n    stream.read(0);\n    if (len === state.length)\n      // didn't get any data, stop spinning.\n      break;\n  }\n  state.readingMore = false;\n}\n\n// abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\nReadable.prototype._read = function (n) {\n  errorOrDestroy(this, new ERR_METHOD_NOT_IMPLEMENTED('_read()'));\n};\nReadable.prototype.pipe = function (dest, pipeOpts) {\n  var src = this;\n  var state = this._readableState;\n  switch (state.pipesCount) {\n    case 0:\n      state.pipes = dest;\n      break;\n    case 1:\n      state.pipes = [state.pipes, dest];\n      break;\n    default:\n      state.pipes.push(dest);\n      break;\n  }\n  state.pipesCount += 1;\n  debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);\n  var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;\n  var endFn = doEnd ? onend : unpipe;\n  if (state.endEmitted) process.nextTick(endFn);else src.once('end', endFn);\n  dest.on('unpipe', onunpipe);\n  function onunpipe(readable, unpipeInfo) {\n    debug('onunpipe');\n    if (readable === src) {\n      if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n        unpipeInfo.hasUnpiped = true;\n        cleanup();\n      }\n    }\n  }\n  function onend() {\n    debug('onend');\n    dest.end();\n  }\n\n  // when the dest drains, it reduces the awaitDrain counter\n  // on the source.  This would be more elegant with a .once()\n  // handler in flow(), but adding and removing repeatedly is\n  // too slow.\n  var ondrain = pipeOnDrain(src);\n  dest.on('drain', ondrain);\n  var cleanedUp = false;\n  function cleanup() {\n    debug('cleanup');\n    // cleanup event handlers once the pipe is broken\n    dest.removeListener('close', onclose);\n    dest.removeListener('finish', onfinish);\n    dest.removeListener('drain', ondrain);\n    dest.removeListener('error', onerror);\n    dest.removeListener('unpipe', onunpipe);\n    src.removeListener('end', onend);\n    src.removeListener('end', unpipe);\n    src.removeListener('data', ondata);\n    cleanedUp = true;\n\n    // if the reader is waiting for a drain event from this\n    // specific writer, then it would cause it to never start\n    // flowing again.\n    // So, if this is awaiting a drain, then we just call it now.\n    // If we don't know, then assume that we are waiting for one.\n    if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();\n  }\n  src.on('data', ondata);\n  function ondata(chunk) {\n    debug('ondata');\n    var ret = dest.write(chunk);\n    debug('dest.write', ret);\n    if (ret === false) {\n      // If the user unpiped during `dest.write()`, it is possible\n      // to get stuck in a permanently paused state if that write\n      // also returned false.\n      // => Check whether `dest` is still a piping destination.\n      if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {\n        debug('false write response, pause', state.awaitDrain);\n        state.awaitDrain++;\n      }\n      src.pause();\n    }\n  }\n\n  // if the dest has an error, then stop piping into it.\n  // however, don't suppress the throwing behavior for this.\n  function onerror(er) {\n    debug('onerror', er);\n    unpipe();\n    dest.removeListener('error', onerror);\n    if (EElistenerCount(dest, 'error') === 0) errorOrDestroy(dest, er);\n  }\n\n  // Make sure our error handler is attached before userland ones.\n  prependListener(dest, 'error', onerror);\n\n  // Both close and finish should trigger unpipe, but only once.\n  function onclose() {\n    dest.removeListener('finish', onfinish);\n    unpipe();\n  }\n  dest.once('close', onclose);\n  function onfinish() {\n    debug('onfinish');\n    dest.removeListener('close', onclose);\n    unpipe();\n  }\n  dest.once('finish', onfinish);\n  function unpipe() {\n    debug('unpipe');\n    src.unpipe(dest);\n  }\n\n  // tell the dest that it's being piped to\n  dest.emit('pipe', src);\n\n  // start the flow if it hasn't been started already.\n  if (!state.flowing) {\n    debug('pipe resume');\n    src.resume();\n  }\n  return dest;\n};\nfunction pipeOnDrain(src) {\n  return function pipeOnDrainFunctionResult() {\n    var state = src._readableState;\n    debug('pipeOnDrain', state.awaitDrain);\n    if (state.awaitDrain) state.awaitDrain--;\n    if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {\n      state.flowing = true;\n      flow(src);\n    }\n  };\n}\nReadable.prototype.unpipe = function (dest) {\n  var state = this._readableState;\n  var unpipeInfo = {\n    hasUnpiped: false\n  };\n\n  // if we're not piping anywhere, then do nothing.\n  if (state.pipesCount === 0) return this;\n\n  // just one destination.  most common case.\n  if (state.pipesCount === 1) {\n    // passed in one, but it's not the right one.\n    if (dest && dest !== state.pipes) return this;\n    if (!dest) dest = state.pipes;\n\n    // got a match.\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    if (dest) dest.emit('unpipe', this, unpipeInfo);\n    return this;\n  }\n\n  // slow case. multiple pipe destinations.\n\n  if (!dest) {\n    // remove all.\n    var dests = state.pipes;\n    var len = state.pipesCount;\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    for (var i = 0; i < len; i++) dests[i].emit('unpipe', this, {\n      hasUnpiped: false\n    });\n    return this;\n  }\n\n  // try to find the right one.\n  var index = indexOf(state.pipes, dest);\n  if (index === -1) return this;\n  state.pipes.splice(index, 1);\n  state.pipesCount -= 1;\n  if (state.pipesCount === 1) state.pipes = state.pipes[0];\n  dest.emit('unpipe', this, unpipeInfo);\n  return this;\n};\n\n// set up data events if they are asked for\n// Ensure readable listeners eventually get something\nReadable.prototype.on = function (ev, fn) {\n  var res = Stream.prototype.on.call(this, ev, fn);\n  var state = this._readableState;\n  if (ev === 'data') {\n    // update readableListening so that resume() may be a no-op\n    // a few lines down. This is needed to support once('readable').\n    state.readableListening = this.listenerCount('readable') > 0;\n\n    // Try start flowing on next tick if stream isn't explicitly paused\n    if (state.flowing !== false) this.resume();\n  } else if (ev === 'readable') {\n    if (!state.endEmitted && !state.readableListening) {\n      state.readableListening = state.needReadable = true;\n      state.flowing = false;\n      state.emittedReadable = false;\n      debug('on readable', state.length, state.reading);\n      if (state.length) {\n        emitReadable(this);\n      } else if (!state.reading) {\n        process.nextTick(nReadingNextTick, this);\n      }\n    }\n  }\n  return res;\n};\nReadable.prototype.addListener = Readable.prototype.on;\nReadable.prototype.removeListener = function (ev, fn) {\n  var res = Stream.prototype.removeListener.call(this, ev, fn);\n  if (ev === 'readable') {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n  return res;\n};\nReadable.prototype.removeAllListeners = function (ev) {\n  var res = Stream.prototype.removeAllListeners.apply(this, arguments);\n  if (ev === 'readable' || ev === undefined) {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n  return res;\n};\nfunction updateReadableListening(self) {\n  var state = self._readableState;\n  state.readableListening = self.listenerCount('readable') > 0;\n  if (state.resumeScheduled && !state.paused) {\n    // flowing needs to be set to true now, otherwise\n    // the upcoming resume will not flow.\n    state.flowing = true;\n\n    // crude way to check if we should resume\n  } else if (self.listenerCount('data') > 0) {\n    self.resume();\n  }\n}\nfunction nReadingNextTick(self) {\n  debug('readable nexttick read 0');\n  self.read(0);\n}\n\n// pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\nReadable.prototype.resume = function () {\n  var state = this._readableState;\n  if (!state.flowing) {\n    debug('resume');\n    // we flow only if there is no one listening\n    // for readable, but we still have to call\n    // resume()\n    state.flowing = !state.readableListening;\n    resume(this, state);\n  }\n  state.paused = false;\n  return this;\n};\nfunction resume(stream, state) {\n  if (!state.resumeScheduled) {\n    state.resumeScheduled = true;\n    process.nextTick(resume_, stream, state);\n  }\n}\nfunction resume_(stream, state) {\n  debug('resume', state.reading);\n  if (!state.reading) {\n    stream.read(0);\n  }\n  state.resumeScheduled = false;\n  stream.emit('resume');\n  flow(stream);\n  if (state.flowing && !state.reading) stream.read(0);\n}\nReadable.prototype.pause = function () {\n  debug('call pause flowing=%j', this._readableState.flowing);\n  if (this._readableState.flowing !== false) {\n    debug('pause');\n    this._readableState.flowing = false;\n    this.emit('pause');\n  }\n  this._readableState.paused = true;\n  return this;\n};\nfunction flow(stream) {\n  var state = stream._readableState;\n  debug('flow', state.flowing);\n  while (state.flowing && stream.read() !== null);\n}\n\n// wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\nReadable.prototype.wrap = function (stream) {\n  var _this = this;\n  var state = this._readableState;\n  var paused = false;\n  stream.on('end', function () {\n    debug('wrapped end');\n    if (state.decoder && !state.ended) {\n      var chunk = state.decoder.end();\n      if (chunk && chunk.length) _this.push(chunk);\n    }\n    _this.push(null);\n  });\n  stream.on('data', function (chunk) {\n    debug('wrapped data');\n    if (state.decoder) chunk = state.decoder.write(chunk);\n\n    // don't skip over falsy values in objectMode\n    if (state.objectMode && (chunk === null || chunk === undefined)) return;else if (!state.objectMode && (!chunk || !chunk.length)) return;\n    var ret = _this.push(chunk);\n    if (!ret) {\n      paused = true;\n      stream.pause();\n    }\n  });\n\n  // proxy all the other methods.\n  // important when wrapping filters and duplexes.\n  for (var i in stream) {\n    if (this[i] === undefined && typeof stream[i] === 'function') {\n      this[i] = function methodWrap(method) {\n        return function methodWrapReturnFunction() {\n          return stream[method].apply(stream, arguments);\n        };\n      }(i);\n    }\n  }\n\n  // proxy certain important events.\n  for (var n = 0; n < kProxyEvents.length; n++) {\n    stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));\n  }\n\n  // when we try to consume some more bytes, simply unpause the\n  // underlying stream.\n  this._read = function (n) {\n    debug('wrapped _read', n);\n    if (paused) {\n      paused = false;\n      stream.resume();\n    }\n  };\n  return this;\n};\nif (typeof Symbol === 'function') {\n  Readable.prototype[Symbol.asyncIterator] = function () {\n    if (createReadableStreamAsyncIterator === undefined) {\n      createReadableStreamAsyncIterator = require('./internal/streams/async_iterator');\n    }\n    return createReadableStreamAsyncIterator(this);\n  };\n}\nObject.defineProperty(Readable.prototype, 'readableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.highWaterMark;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState && this._readableState.buffer;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableFlowing', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.flowing;\n  },\n  set: function set(state) {\n    if (this._readableState) {\n      this._readableState.flowing = state;\n    }\n  }\n});\n\n// exposed for testing purposes only.\nReadable._fromList = fromList;\nObject.defineProperty(Readable.prototype, 'readableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.length;\n  }\n});\n\n// Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromList(n, state) {\n  // nothing buffered\n  if (state.length === 0) return null;\n  var ret;\n  if (state.objectMode) ret = state.buffer.shift();else if (!n || n >= state.length) {\n    // read it all, truncate the list\n    if (state.decoder) ret = state.buffer.join('');else if (state.buffer.length === 1) ret = state.buffer.first();else ret = state.buffer.concat(state.length);\n    state.buffer.clear();\n  } else {\n    // read part of list\n    ret = state.buffer.consume(n, state.decoder);\n  }\n  return ret;\n}\nfunction endReadable(stream) {\n  var state = stream._readableState;\n  debug('endReadable', state.endEmitted);\n  if (!state.endEmitted) {\n    state.ended = true;\n    process.nextTick(endReadableNT, state, stream);\n  }\n}\nfunction endReadableNT(state, stream) {\n  debug('endReadableNT', state.endEmitted, state.length);\n\n  // Check that we didn't get one last unshift.\n  if (!state.endEmitted && state.length === 0) {\n    state.endEmitted = true;\n    stream.readable = false;\n    stream.emit('end');\n    if (state.autoDestroy) {\n      // In case of duplex streams we need a way to detect\n      // if the writable side is ready for autoDestroy as well\n      var wState = stream._writableState;\n      if (!wState || wState.autoDestroy && wState.finished) {\n        stream.destroy();\n      }\n    }\n  }\n}\nif (typeof Symbol === 'function') {\n  Readable.from = function (iterable, opts) {\n    if (from === undefined) {\n      from = require('./internal/streams/from');\n    }\n    return from(Readable, iterable, opts);\n  };\n}\nfunction indexOf(xs, x) {\n  for (var i = 0, l = xs.length; i < l; i++) {\n    if (xs[i] === x) return i;\n  }\n  return -1;\n}", "map": {"version": 3, "names": ["module", "exports", "Readable", "Duplex", "ReadableState", "EE", "require", "EventEmitter", "EElistenerCount", "emitter", "type", "listeners", "length", "Stream", "<PERSON><PERSON><PERSON>", "OurUint8Array", "global", "window", "self", "Uint8Array", "_uint8ArrayToBuffer", "chunk", "from", "_isUint8Array", "obj", "<PERSON><PERSON><PERSON><PERSON>", "debugUtil", "debug", "debuglog", "BufferList", "destroyImpl", "_require", "getHighWaterMark", "_require$codes", "codes", "ERR_INVALID_ARG_TYPE", "ERR_STREAM_PUSH_AFTER_EOF", "ERR_METHOD_NOT_IMPLEMENTED", "ERR_STREAM_UNSHIFT_AFTER_END_EVENT", "StringDecoder", "createReadableStreamAsyncIterator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kProxyEvents", "prependListener", "event", "fn", "_events", "on", "Array", "isArray", "unshift", "options", "stream", "isDuplex", "objectMode", "readableObjectMode", "highWaterMark", "buffer", "pipes", "pipesCount", "flowing", "ended", "endEmitted", "reading", "sync", "needReadable", "emittedReadable", "readableListening", "resumeScheduled", "paused", "emitClose", "autoDestroy", "destroyed", "defaultEncoding", "await<PERSON><PERSON>", "readingMore", "decoder", "encoding", "_readableState", "readable", "read", "_read", "destroy", "_destroy", "call", "Object", "defineProperty", "prototype", "enumerable", "get", "undefined", "set", "value", "_undestroy", "undestroy", "err", "cb", "push", "state", "skip<PERSON><PERSON>k<PERSON><PERSON><PERSON>", "readableAddChunk", "addToFront", "onEofChunk", "er", "chunkInvalid", "getPrototypeOf", "addChunk", "write", "maybeReadMore", "emit", "emitReadable", "isPaused", "setEncoding", "enc", "p", "head", "content", "data", "next", "clear", "MAX_HWM", "computeNewHighWaterMark", "n", "howMuchToRead", "parseInt", "nOrig", "endReadable", "doRead", "ret", "fromList", "end", "emitReadable_", "process", "nextTick", "flow", "maybeReadMore_", "len", "pipe", "dest", "pipeOpts", "src", "doEnd", "stdout", "stderr", "endFn", "onend", "unpipe", "once", "onunpipe", "unpipeInfo", "hasUnpiped", "cleanup", "ondrain", "pipeOnDrain", "cleanedUp", "removeListener", "onclose", "onfinish", "onerror", "ondata", "_writableState", "needDrain", "indexOf", "pause", "resume", "pipeOnDrainFunctionResult", "dests", "i", "index", "splice", "ev", "res", "listenerCount", "nReadingNextTick", "addListener", "updateReadableListening", "removeAllListeners", "apply", "arguments", "resume_", "wrap", "_this", "methodWrap", "method", "methodWrapReturnFunction", "bind", "Symbol", "asyncIterator", "_fromList", "shift", "join", "first", "concat", "consume", "endReadableNT", "wState", "finished", "iterable", "opts", "xs", "x", "l"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/readable-stream/lib/_stream_readable.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nmodule.exports = Readable;\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nReadable.ReadableState = ReadableState;\n\n/*<replacement>*/\nvar EE = require('events').EventEmitter;\nvar EElistenerCount = function EElistenerCount(emitter, type) {\n  return emitter.listeners(type).length;\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\nvar Buffer = require('buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\n/*<replacement>*/\nvar debugUtil = require('util');\nvar debug;\nif (debugUtil && debugUtil.debuglog) {\n  debug = debugUtil.debuglog('stream');\n} else {\n  debug = function debug() {};\n}\n/*</replacement>*/\n\nvar BufferList = require('./internal/streams/buffer_list');\nvar destroyImpl = require('./internal/streams/destroy');\nvar _require = require('./internal/streams/state'),\n  getHighWaterMark = _require.getHighWaterMark;\nvar _require$codes = require('../errors').codes,\n  ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n  ERR_STREAM_PUSH_AFTER_EOF = _require$codes.ERR_STREAM_PUSH_AFTER_EOF,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_STREAM_UNSHIFT_AFTER_END_EVENT = _require$codes.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;\n\n// Lazy loaded to improve the startup performance.\nvar StringDecoder;\nvar createReadableStreamAsyncIterator;\nvar from;\nrequire('inherits')(Readable, Stream);\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\nvar kProxyEvents = ['error', 'close', 'destroy', 'pause', 'resume'];\nfunction prependListener(emitter, event, fn) {\n  // Sadly this is not cacheable as some libraries bundle their own\n  // event emitter implementation with them.\n  if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn);\n\n  // This is a hack to make sure that our error handler is attached before any\n  // userland ones.  NEVER DO THIS. This is here only because this code needs\n  // to continue to work with older versions of Node.js that do not include\n  // the prependListener() method. The goal is to eventually remove this hack.\n  if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);else if (Array.isArray(emitter._events[event])) emitter._events[event].unshift(fn);else emitter._events[event] = [fn, emitter._events[event]];\n}\nfunction ReadableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex;\n\n  // object stream flag. Used to make read(n) ignore n and to\n  // make all the buffer merging and length checks go away\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode;\n\n  // the point at which it stops calling _read() to fill the buffer\n  // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n  this.highWaterMark = getHighWaterMark(this, options, 'readableHighWaterMark', isDuplex);\n\n  // A linked list is used to store data chunks instead of an array because the\n  // linked list can remove elements from the beginning faster than\n  // array.shift()\n  this.buffer = new BufferList();\n  this.length = 0;\n  this.pipes = null;\n  this.pipesCount = 0;\n  this.flowing = null;\n  this.ended = false;\n  this.endEmitted = false;\n  this.reading = false;\n\n  // a flag to be able to tell if the event 'readable'/'data' is emitted\n  // immediately, or on a later tick.  We set this to true at first, because\n  // any actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first read call.\n  this.sync = true;\n\n  // whenever we return null, then we set a flag to say\n  // that we're awaiting a 'readable' event emission.\n  this.needReadable = false;\n  this.emittedReadable = false;\n  this.readableListening = false;\n  this.resumeScheduled = false;\n  this.paused = true;\n\n  // Should close be emitted on destroy. Defaults to true.\n  this.emitClose = options.emitClose !== false;\n\n  // Should .destroy() be called after 'end' (and potentially 'finish')\n  this.autoDestroy = !!options.autoDestroy;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // the number of writers that are awaiting a drain event in .pipe()s\n  this.awaitDrain = 0;\n\n  // if true, a maybeReadMore has been scheduled\n  this.readingMore = false;\n  this.decoder = null;\n  this.encoding = null;\n  if (options.encoding) {\n    if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n    this.decoder = new StringDecoder(options.encoding);\n    this.encoding = options.encoding;\n  }\n}\nfunction Readable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n  if (!(this instanceof Readable)) return new Readable(options);\n\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the ReadableState constructor, at least with V8 6.5\n  var isDuplex = this instanceof Duplex;\n  this._readableState = new ReadableState(options, this, isDuplex);\n\n  // legacy\n  this.readable = true;\n  if (options) {\n    if (typeof options.read === 'function') this._read = options.read;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n  }\n  Stream.call(this);\n}\nObject.defineProperty(Readable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._readableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n  }\n});\nReadable.prototype.destroy = destroyImpl.destroy;\nReadable.prototype._undestroy = destroyImpl.undestroy;\nReadable.prototype._destroy = function (err, cb) {\n  cb(err);\n};\n\n// Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\nReadable.prototype.push = function (chunk, encoding) {\n  var state = this._readableState;\n  var skipChunkCheck;\n  if (!state.objectMode) {\n    if (typeof chunk === 'string') {\n      encoding = encoding || state.defaultEncoding;\n      if (encoding !== state.encoding) {\n        chunk = Buffer.from(chunk, encoding);\n        encoding = '';\n      }\n      skipChunkCheck = true;\n    }\n  } else {\n    skipChunkCheck = true;\n  }\n  return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);\n};\n\n// Unshift should *always* be something directly out of read()\nReadable.prototype.unshift = function (chunk) {\n  return readableAddChunk(this, chunk, null, true, false);\n};\nfunction readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {\n  debug('readableAddChunk', chunk);\n  var state = stream._readableState;\n  if (chunk === null) {\n    state.reading = false;\n    onEofChunk(stream, state);\n  } else {\n    var er;\n    if (!skipChunkCheck) er = chunkInvalid(state, chunk);\n    if (er) {\n      errorOrDestroy(stream, er);\n    } else if (state.objectMode || chunk && chunk.length > 0) {\n      if (typeof chunk !== 'string' && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {\n        chunk = _uint8ArrayToBuffer(chunk);\n      }\n      if (addToFront) {\n        if (state.endEmitted) errorOrDestroy(stream, new ERR_STREAM_UNSHIFT_AFTER_END_EVENT());else addChunk(stream, state, chunk, true);\n      } else if (state.ended) {\n        errorOrDestroy(stream, new ERR_STREAM_PUSH_AFTER_EOF());\n      } else if (state.destroyed) {\n        return false;\n      } else {\n        state.reading = false;\n        if (state.decoder && !encoding) {\n          chunk = state.decoder.write(chunk);\n          if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);else maybeReadMore(stream, state);\n        } else {\n          addChunk(stream, state, chunk, false);\n        }\n      }\n    } else if (!addToFront) {\n      state.reading = false;\n      maybeReadMore(stream, state);\n    }\n  }\n\n  // We can push more data if we are below the highWaterMark.\n  // Also, if we have no data yet, we can stand some more bytes.\n  // This is to work around cases where hwm=0, such as the repl.\n  return !state.ended && (state.length < state.highWaterMark || state.length === 0);\n}\nfunction addChunk(stream, state, chunk, addToFront) {\n  if (state.flowing && state.length === 0 && !state.sync) {\n    state.awaitDrain = 0;\n    stream.emit('data', chunk);\n  } else {\n    // update the buffer info.\n    state.length += state.objectMode ? 1 : chunk.length;\n    if (addToFront) state.buffer.unshift(chunk);else state.buffer.push(chunk);\n    if (state.needReadable) emitReadable(stream);\n  }\n  maybeReadMore(stream, state);\n}\nfunction chunkInvalid(state, chunk) {\n  var er;\n  if (!_isUint8Array(chunk) && typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer', 'Uint8Array'], chunk);\n  }\n  return er;\n}\nReadable.prototype.isPaused = function () {\n  return this._readableState.flowing === false;\n};\n\n// backwards compatibility.\nReadable.prototype.setEncoding = function (enc) {\n  if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n  var decoder = new StringDecoder(enc);\n  this._readableState.decoder = decoder;\n  // If setEncoding(null), decoder.encoding equals utf8\n  this._readableState.encoding = this._readableState.decoder.encoding;\n\n  // Iterate over current buffer to convert already stored Buffers:\n  var p = this._readableState.buffer.head;\n  var content = '';\n  while (p !== null) {\n    content += decoder.write(p.data);\n    p = p.next;\n  }\n  this._readableState.buffer.clear();\n  if (content !== '') this._readableState.buffer.push(content);\n  this._readableState.length = content.length;\n  return this;\n};\n\n// Don't raise the hwm > 1GB\nvar MAX_HWM = 0x40000000;\nfunction computeNewHighWaterMark(n) {\n  if (n >= MAX_HWM) {\n    // TODO(ronag): Throw ERR_VALUE_OUT_OF_RANGE.\n    n = MAX_HWM;\n  } else {\n    // Get the next highest power of 2 to prevent increasing hwm excessively in\n    // tiny amounts\n    n--;\n    n |= n >>> 1;\n    n |= n >>> 2;\n    n |= n >>> 4;\n    n |= n >>> 8;\n    n |= n >>> 16;\n    n++;\n  }\n  return n;\n}\n\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction howMuchToRead(n, state) {\n  if (n <= 0 || state.length === 0 && state.ended) return 0;\n  if (state.objectMode) return 1;\n  if (n !== n) {\n    // Only flow one buffer at a time\n    if (state.flowing && state.length) return state.buffer.head.data.length;else return state.length;\n  }\n  // If we're asking for more than the current hwm, then raise the hwm.\n  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);\n  if (n <= state.length) return n;\n  // Don't have enough\n  if (!state.ended) {\n    state.needReadable = true;\n    return 0;\n  }\n  return state.length;\n}\n\n// you can override either this method, or the async _read(n) below.\nReadable.prototype.read = function (n) {\n  debug('read', n);\n  n = parseInt(n, 10);\n  var state = this._readableState;\n  var nOrig = n;\n  if (n !== 0) state.emittedReadable = false;\n\n  // if we're doing read(0) to trigger a readable event, but we\n  // already have a bunch of data in the buffer, then just trigger\n  // the 'readable' event and move on.\n  if (n === 0 && state.needReadable && ((state.highWaterMark !== 0 ? state.length >= state.highWaterMark : state.length > 0) || state.ended)) {\n    debug('read: emitReadable', state.length, state.ended);\n    if (state.length === 0 && state.ended) endReadable(this);else emitReadable(this);\n    return null;\n  }\n  n = howMuchToRead(n, state);\n\n  // if we've ended, and we're now clear, then finish it up.\n  if (n === 0 && state.ended) {\n    if (state.length === 0) endReadable(this);\n    return null;\n  }\n\n  // All the actual chunk generation logic needs to be\n  // *below* the call to _read.  The reason is that in certain\n  // synthetic stream cases, such as passthrough streams, _read\n  // may be a completely synchronous operation which may change\n  // the state of the read buffer, providing enough data when\n  // before there was *not* enough.\n  //\n  // So, the steps are:\n  // 1. Figure out what the state of things will be after we do\n  // a read from the buffer.\n  //\n  // 2. If that resulting state will trigger a _read, then call _read.\n  // Note that this may be asynchronous, or synchronous.  Yes, it is\n  // deeply ugly to write APIs this way, but that still doesn't mean\n  // that the Readable class should behave improperly, as streams are\n  // designed to be sync/async agnostic.\n  // Take note if the _read call is sync or async (ie, if the read call\n  // has returned yet), so that we know whether or not it's safe to emit\n  // 'readable' etc.\n  //\n  // 3. Actually pull the requested chunks out of the buffer and return.\n\n  // if we need a readable event, then we need to do some reading.\n  var doRead = state.needReadable;\n  debug('need readable', doRead);\n\n  // if we currently have less than the highWaterMark, then also read some\n  if (state.length === 0 || state.length - n < state.highWaterMark) {\n    doRead = true;\n    debug('length less than watermark', doRead);\n  }\n\n  // however, if we've ended, then there's no point, and if we're already\n  // reading, then it's unnecessary.\n  if (state.ended || state.reading) {\n    doRead = false;\n    debug('reading or ended', doRead);\n  } else if (doRead) {\n    debug('do read');\n    state.reading = true;\n    state.sync = true;\n    // if the length is currently zero, then we *need* a readable event.\n    if (state.length === 0) state.needReadable = true;\n    // call internal read method\n    this._read(state.highWaterMark);\n    state.sync = false;\n    // If _read pushed data synchronously, then `reading` will be false,\n    // and we need to re-evaluate how much data we can return to the user.\n    if (!state.reading) n = howMuchToRead(nOrig, state);\n  }\n  var ret;\n  if (n > 0) ret = fromList(n, state);else ret = null;\n  if (ret === null) {\n    state.needReadable = state.length <= state.highWaterMark;\n    n = 0;\n  } else {\n    state.length -= n;\n    state.awaitDrain = 0;\n  }\n  if (state.length === 0) {\n    // If we have nothing in the buffer, then we want to know\n    // as soon as we *do* get something into the buffer.\n    if (!state.ended) state.needReadable = true;\n\n    // If we tried to read() past the EOF, then emit end on the next tick.\n    if (nOrig !== n && state.ended) endReadable(this);\n  }\n  if (ret !== null) this.emit('data', ret);\n  return ret;\n};\nfunction onEofChunk(stream, state) {\n  debug('onEofChunk');\n  if (state.ended) return;\n  if (state.decoder) {\n    var chunk = state.decoder.end();\n    if (chunk && chunk.length) {\n      state.buffer.push(chunk);\n      state.length += state.objectMode ? 1 : chunk.length;\n    }\n  }\n  state.ended = true;\n  if (state.sync) {\n    // if we are sync, wait until next tick to emit the data.\n    // Otherwise we risk emitting data in the flow()\n    // the readable code triggers during a read() call\n    emitReadable(stream);\n  } else {\n    // emit 'readable' now to make sure it gets picked up.\n    state.needReadable = false;\n    if (!state.emittedReadable) {\n      state.emittedReadable = true;\n      emitReadable_(stream);\n    }\n  }\n}\n\n// Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\nfunction emitReadable(stream) {\n  var state = stream._readableState;\n  debug('emitReadable', state.needReadable, state.emittedReadable);\n  state.needReadable = false;\n  if (!state.emittedReadable) {\n    debug('emitReadable', state.flowing);\n    state.emittedReadable = true;\n    process.nextTick(emitReadable_, stream);\n  }\n}\nfunction emitReadable_(stream) {\n  var state = stream._readableState;\n  debug('emitReadable_', state.destroyed, state.length, state.ended);\n  if (!state.destroyed && (state.length || state.ended)) {\n    stream.emit('readable');\n    state.emittedReadable = false;\n  }\n\n  // The stream needs another readable event if\n  // 1. It is not flowing, as the flow mechanism will take\n  //    care of it.\n  // 2. It is not ended.\n  // 3. It is below the highWaterMark, so we can schedule\n  //    another readable later.\n  state.needReadable = !state.flowing && !state.ended && state.length <= state.highWaterMark;\n  flow(stream);\n}\n\n// at this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\nfunction maybeReadMore(stream, state) {\n  if (!state.readingMore) {\n    state.readingMore = true;\n    process.nextTick(maybeReadMore_, stream, state);\n  }\n}\nfunction maybeReadMore_(stream, state) {\n  // Attempt to read more data if we should.\n  //\n  // The conditions for reading more data are (one of):\n  // - Not enough data buffered (state.length < state.highWaterMark). The loop\n  //   is responsible for filling the buffer with enough data if such data\n  //   is available. If highWaterMark is 0 and we are not in the flowing mode\n  //   we should _not_ attempt to buffer any extra data. We'll get more data\n  //   when the stream consumer calls read() instead.\n  // - No data in the buffer, and the stream is in flowing mode. In this mode\n  //   the loop below is responsible for ensuring read() is called. Failing to\n  //   call read here would abort the flow and there's no other mechanism for\n  //   continuing the flow if the stream consumer has just subscribed to the\n  //   'data' event.\n  //\n  // In addition to the above conditions to keep reading data, the following\n  // conditions prevent the data from being read:\n  // - The stream has ended (state.ended).\n  // - There is already a pending 'read' operation (state.reading). This is a\n  //   case where the the stream has called the implementation defined _read()\n  //   method, but they are processing the call asynchronously and have _not_\n  //   called push() with new data. In this case we skip performing more\n  //   read()s. The execution ends in this method again after the _read() ends\n  //   up calling push() with more data.\n  while (!state.reading && !state.ended && (state.length < state.highWaterMark || state.flowing && state.length === 0)) {\n    var len = state.length;\n    debug('maybeReadMore read 0');\n    stream.read(0);\n    if (len === state.length)\n      // didn't get any data, stop spinning.\n      break;\n  }\n  state.readingMore = false;\n}\n\n// abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\nReadable.prototype._read = function (n) {\n  errorOrDestroy(this, new ERR_METHOD_NOT_IMPLEMENTED('_read()'));\n};\nReadable.prototype.pipe = function (dest, pipeOpts) {\n  var src = this;\n  var state = this._readableState;\n  switch (state.pipesCount) {\n    case 0:\n      state.pipes = dest;\n      break;\n    case 1:\n      state.pipes = [state.pipes, dest];\n      break;\n    default:\n      state.pipes.push(dest);\n      break;\n  }\n  state.pipesCount += 1;\n  debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);\n  var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;\n  var endFn = doEnd ? onend : unpipe;\n  if (state.endEmitted) process.nextTick(endFn);else src.once('end', endFn);\n  dest.on('unpipe', onunpipe);\n  function onunpipe(readable, unpipeInfo) {\n    debug('onunpipe');\n    if (readable === src) {\n      if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n        unpipeInfo.hasUnpiped = true;\n        cleanup();\n      }\n    }\n  }\n  function onend() {\n    debug('onend');\n    dest.end();\n  }\n\n  // when the dest drains, it reduces the awaitDrain counter\n  // on the source.  This would be more elegant with a .once()\n  // handler in flow(), but adding and removing repeatedly is\n  // too slow.\n  var ondrain = pipeOnDrain(src);\n  dest.on('drain', ondrain);\n  var cleanedUp = false;\n  function cleanup() {\n    debug('cleanup');\n    // cleanup event handlers once the pipe is broken\n    dest.removeListener('close', onclose);\n    dest.removeListener('finish', onfinish);\n    dest.removeListener('drain', ondrain);\n    dest.removeListener('error', onerror);\n    dest.removeListener('unpipe', onunpipe);\n    src.removeListener('end', onend);\n    src.removeListener('end', unpipe);\n    src.removeListener('data', ondata);\n    cleanedUp = true;\n\n    // if the reader is waiting for a drain event from this\n    // specific writer, then it would cause it to never start\n    // flowing again.\n    // So, if this is awaiting a drain, then we just call it now.\n    // If we don't know, then assume that we are waiting for one.\n    if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();\n  }\n  src.on('data', ondata);\n  function ondata(chunk) {\n    debug('ondata');\n    var ret = dest.write(chunk);\n    debug('dest.write', ret);\n    if (ret === false) {\n      // If the user unpiped during `dest.write()`, it is possible\n      // to get stuck in a permanently paused state if that write\n      // also returned false.\n      // => Check whether `dest` is still a piping destination.\n      if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {\n        debug('false write response, pause', state.awaitDrain);\n        state.awaitDrain++;\n      }\n      src.pause();\n    }\n  }\n\n  // if the dest has an error, then stop piping into it.\n  // however, don't suppress the throwing behavior for this.\n  function onerror(er) {\n    debug('onerror', er);\n    unpipe();\n    dest.removeListener('error', onerror);\n    if (EElistenerCount(dest, 'error') === 0) errorOrDestroy(dest, er);\n  }\n\n  // Make sure our error handler is attached before userland ones.\n  prependListener(dest, 'error', onerror);\n\n  // Both close and finish should trigger unpipe, but only once.\n  function onclose() {\n    dest.removeListener('finish', onfinish);\n    unpipe();\n  }\n  dest.once('close', onclose);\n  function onfinish() {\n    debug('onfinish');\n    dest.removeListener('close', onclose);\n    unpipe();\n  }\n  dest.once('finish', onfinish);\n  function unpipe() {\n    debug('unpipe');\n    src.unpipe(dest);\n  }\n\n  // tell the dest that it's being piped to\n  dest.emit('pipe', src);\n\n  // start the flow if it hasn't been started already.\n  if (!state.flowing) {\n    debug('pipe resume');\n    src.resume();\n  }\n  return dest;\n};\nfunction pipeOnDrain(src) {\n  return function pipeOnDrainFunctionResult() {\n    var state = src._readableState;\n    debug('pipeOnDrain', state.awaitDrain);\n    if (state.awaitDrain) state.awaitDrain--;\n    if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {\n      state.flowing = true;\n      flow(src);\n    }\n  };\n}\nReadable.prototype.unpipe = function (dest) {\n  var state = this._readableState;\n  var unpipeInfo = {\n    hasUnpiped: false\n  };\n\n  // if we're not piping anywhere, then do nothing.\n  if (state.pipesCount === 0) return this;\n\n  // just one destination.  most common case.\n  if (state.pipesCount === 1) {\n    // passed in one, but it's not the right one.\n    if (dest && dest !== state.pipes) return this;\n    if (!dest) dest = state.pipes;\n\n    // got a match.\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    if (dest) dest.emit('unpipe', this, unpipeInfo);\n    return this;\n  }\n\n  // slow case. multiple pipe destinations.\n\n  if (!dest) {\n    // remove all.\n    var dests = state.pipes;\n    var len = state.pipesCount;\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    for (var i = 0; i < len; i++) dests[i].emit('unpipe', this, {\n      hasUnpiped: false\n    });\n    return this;\n  }\n\n  // try to find the right one.\n  var index = indexOf(state.pipes, dest);\n  if (index === -1) return this;\n  state.pipes.splice(index, 1);\n  state.pipesCount -= 1;\n  if (state.pipesCount === 1) state.pipes = state.pipes[0];\n  dest.emit('unpipe', this, unpipeInfo);\n  return this;\n};\n\n// set up data events if they are asked for\n// Ensure readable listeners eventually get something\nReadable.prototype.on = function (ev, fn) {\n  var res = Stream.prototype.on.call(this, ev, fn);\n  var state = this._readableState;\n  if (ev === 'data') {\n    // update readableListening so that resume() may be a no-op\n    // a few lines down. This is needed to support once('readable').\n    state.readableListening = this.listenerCount('readable') > 0;\n\n    // Try start flowing on next tick if stream isn't explicitly paused\n    if (state.flowing !== false) this.resume();\n  } else if (ev === 'readable') {\n    if (!state.endEmitted && !state.readableListening) {\n      state.readableListening = state.needReadable = true;\n      state.flowing = false;\n      state.emittedReadable = false;\n      debug('on readable', state.length, state.reading);\n      if (state.length) {\n        emitReadable(this);\n      } else if (!state.reading) {\n        process.nextTick(nReadingNextTick, this);\n      }\n    }\n  }\n  return res;\n};\nReadable.prototype.addListener = Readable.prototype.on;\nReadable.prototype.removeListener = function (ev, fn) {\n  var res = Stream.prototype.removeListener.call(this, ev, fn);\n  if (ev === 'readable') {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n  return res;\n};\nReadable.prototype.removeAllListeners = function (ev) {\n  var res = Stream.prototype.removeAllListeners.apply(this, arguments);\n  if (ev === 'readable' || ev === undefined) {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n  return res;\n};\nfunction updateReadableListening(self) {\n  var state = self._readableState;\n  state.readableListening = self.listenerCount('readable') > 0;\n  if (state.resumeScheduled && !state.paused) {\n    // flowing needs to be set to true now, otherwise\n    // the upcoming resume will not flow.\n    state.flowing = true;\n\n    // crude way to check if we should resume\n  } else if (self.listenerCount('data') > 0) {\n    self.resume();\n  }\n}\nfunction nReadingNextTick(self) {\n  debug('readable nexttick read 0');\n  self.read(0);\n}\n\n// pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\nReadable.prototype.resume = function () {\n  var state = this._readableState;\n  if (!state.flowing) {\n    debug('resume');\n    // we flow only if there is no one listening\n    // for readable, but we still have to call\n    // resume()\n    state.flowing = !state.readableListening;\n    resume(this, state);\n  }\n  state.paused = false;\n  return this;\n};\nfunction resume(stream, state) {\n  if (!state.resumeScheduled) {\n    state.resumeScheduled = true;\n    process.nextTick(resume_, stream, state);\n  }\n}\nfunction resume_(stream, state) {\n  debug('resume', state.reading);\n  if (!state.reading) {\n    stream.read(0);\n  }\n  state.resumeScheduled = false;\n  stream.emit('resume');\n  flow(stream);\n  if (state.flowing && !state.reading) stream.read(0);\n}\nReadable.prototype.pause = function () {\n  debug('call pause flowing=%j', this._readableState.flowing);\n  if (this._readableState.flowing !== false) {\n    debug('pause');\n    this._readableState.flowing = false;\n    this.emit('pause');\n  }\n  this._readableState.paused = true;\n  return this;\n};\nfunction flow(stream) {\n  var state = stream._readableState;\n  debug('flow', state.flowing);\n  while (state.flowing && stream.read() !== null);\n}\n\n// wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\nReadable.prototype.wrap = function (stream) {\n  var _this = this;\n  var state = this._readableState;\n  var paused = false;\n  stream.on('end', function () {\n    debug('wrapped end');\n    if (state.decoder && !state.ended) {\n      var chunk = state.decoder.end();\n      if (chunk && chunk.length) _this.push(chunk);\n    }\n    _this.push(null);\n  });\n  stream.on('data', function (chunk) {\n    debug('wrapped data');\n    if (state.decoder) chunk = state.decoder.write(chunk);\n\n    // don't skip over falsy values in objectMode\n    if (state.objectMode && (chunk === null || chunk === undefined)) return;else if (!state.objectMode && (!chunk || !chunk.length)) return;\n    var ret = _this.push(chunk);\n    if (!ret) {\n      paused = true;\n      stream.pause();\n    }\n  });\n\n  // proxy all the other methods.\n  // important when wrapping filters and duplexes.\n  for (var i in stream) {\n    if (this[i] === undefined && typeof stream[i] === 'function') {\n      this[i] = function methodWrap(method) {\n        return function methodWrapReturnFunction() {\n          return stream[method].apply(stream, arguments);\n        };\n      }(i);\n    }\n  }\n\n  // proxy certain important events.\n  for (var n = 0; n < kProxyEvents.length; n++) {\n    stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));\n  }\n\n  // when we try to consume some more bytes, simply unpause the\n  // underlying stream.\n  this._read = function (n) {\n    debug('wrapped _read', n);\n    if (paused) {\n      paused = false;\n      stream.resume();\n    }\n  };\n  return this;\n};\nif (typeof Symbol === 'function') {\n  Readable.prototype[Symbol.asyncIterator] = function () {\n    if (createReadableStreamAsyncIterator === undefined) {\n      createReadableStreamAsyncIterator = require('./internal/streams/async_iterator');\n    }\n    return createReadableStreamAsyncIterator(this);\n  };\n}\nObject.defineProperty(Readable.prototype, 'readableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.highWaterMark;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState && this._readableState.buffer;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableFlowing', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.flowing;\n  },\n  set: function set(state) {\n    if (this._readableState) {\n      this._readableState.flowing = state;\n    }\n  }\n});\n\n// exposed for testing purposes only.\nReadable._fromList = fromList;\nObject.defineProperty(Readable.prototype, 'readableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.length;\n  }\n});\n\n// Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromList(n, state) {\n  // nothing buffered\n  if (state.length === 0) return null;\n  var ret;\n  if (state.objectMode) ret = state.buffer.shift();else if (!n || n >= state.length) {\n    // read it all, truncate the list\n    if (state.decoder) ret = state.buffer.join('');else if (state.buffer.length === 1) ret = state.buffer.first();else ret = state.buffer.concat(state.length);\n    state.buffer.clear();\n  } else {\n    // read part of list\n    ret = state.buffer.consume(n, state.decoder);\n  }\n  return ret;\n}\nfunction endReadable(stream) {\n  var state = stream._readableState;\n  debug('endReadable', state.endEmitted);\n  if (!state.endEmitted) {\n    state.ended = true;\n    process.nextTick(endReadableNT, state, stream);\n  }\n}\nfunction endReadableNT(state, stream) {\n  debug('endReadableNT', state.endEmitted, state.length);\n\n  // Check that we didn't get one last unshift.\n  if (!state.endEmitted && state.length === 0) {\n    state.endEmitted = true;\n    stream.readable = false;\n    stream.emit('end');\n    if (state.autoDestroy) {\n      // In case of duplex streams we need a way to detect\n      // if the writable side is ready for autoDestroy as well\n      var wState = stream._writableState;\n      if (!wState || wState.autoDestroy && wState.finished) {\n        stream.destroy();\n      }\n    }\n  }\n}\nif (typeof Symbol === 'function') {\n  Readable.from = function (iterable, opts) {\n    if (from === undefined) {\n      from = require('./internal/streams/from');\n    }\n    return from(Readable, iterable, opts);\n  };\n}\nfunction indexOf(xs, x) {\n  for (var i = 0, l = xs.length; i < l; i++) {\n    if (xs[i] === x) return i;\n  }\n  return -1;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;;AAEzB;AACA,IAAIC,MAAM;AACV;;AAEAD,QAAQ,CAACE,aAAa,GAAGA,aAAa;;AAEtC;AACA,IAAIC,EAAE,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY;AACvC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC5D,OAAOD,OAAO,CAACE,SAAS,CAACD,IAAI,CAAC,CAACE,MAAM;AACvC,CAAC;AACD;;AAEA;AACA,IAAIC,MAAM,GAAGP,OAAO,CAAC,2BAA2B,CAAC;AACjD;;AAEA,IAAIQ,MAAM,GAAGR,OAAO,CAAC,QAAQ,CAAC,CAACQ,MAAM;AACrC,IAAIC,aAAa,GAAG,CAAC,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,CAAC,CAAC,EAAEC,UAAU,IAAI,YAAY,CAAC,CAAC;AAC5K,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,OAAOP,MAAM,CAACQ,IAAI,CAACD,KAAK,CAAC;AAC3B;AACA,SAASE,aAAaA,CAACC,GAAG,EAAE;EAC1B,OAAOV,MAAM,CAACW,QAAQ,CAACD,GAAG,CAAC,IAAIA,GAAG,YAAYT,aAAa;AAC7D;;AAEA;AACA,IAAIW,SAAS,GAAGpB,OAAO,CAAC,MAAM,CAAC;AAC/B,IAAIqB,KAAK;AACT,IAAID,SAAS,IAAIA,SAAS,CAACE,QAAQ,EAAE;EACnCD,KAAK,GAAGD,SAAS,CAACE,QAAQ,CAAC,QAAQ,CAAC;AACtC,CAAC,MAAM;EACLD,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG,CAAC,CAAC;AAC7B;AACA;;AAEA,IAAIE,UAAU,GAAGvB,OAAO,CAAC,gCAAgC,CAAC;AAC1D,IAAIwB,WAAW,GAAGxB,OAAO,CAAC,4BAA4B,CAAC;AACvD,IAAIyB,QAAQ,GAAGzB,OAAO,CAAC,0BAA0B,CAAC;EAChD0B,gBAAgB,GAAGD,QAAQ,CAACC,gBAAgB;AAC9C,IAAIC,cAAc,GAAG3B,OAAO,CAAC,WAAW,CAAC,CAAC4B,KAAK;EAC7CC,oBAAoB,GAAGF,cAAc,CAACE,oBAAoB;EAC1DC,yBAAyB,GAAGH,cAAc,CAACG,yBAAyB;EACpEC,0BAA0B,GAAGJ,cAAc,CAACI,0BAA0B;EACtEC,kCAAkC,GAAGL,cAAc,CAACK,kCAAkC;;AAExF;AACA,IAAIC,aAAa;AACjB,IAAIC,iCAAiC;AACrC,IAAIlB,IAAI;AACRhB,OAAO,CAAC,UAAU,CAAC,CAACJ,QAAQ,EAAEW,MAAM,CAAC;AACrC,IAAI4B,cAAc,GAAGX,WAAW,CAACW,cAAc;AAC/C,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;AACnE,SAASC,eAAeA,CAAClC,OAAO,EAAEmC,KAAK,EAAEC,EAAE,EAAE;EAC3C;EACA;EACA,IAAI,OAAOpC,OAAO,CAACkC,eAAe,KAAK,UAAU,EAAE,OAAOlC,OAAO,CAACkC,eAAe,CAACC,KAAK,EAAEC,EAAE,CAAC;;EAE5F;EACA;EACA;EACA;EACA,IAAI,CAACpC,OAAO,CAACqC,OAAO,IAAI,CAACrC,OAAO,CAACqC,OAAO,CAACF,KAAK,CAAC,EAAEnC,OAAO,CAACsC,EAAE,CAACH,KAAK,EAAEC,EAAE,CAAC,CAAC,KAAK,IAAIG,KAAK,CAACC,OAAO,CAACxC,OAAO,CAACqC,OAAO,CAACF,KAAK,CAAC,CAAC,EAAEnC,OAAO,CAACqC,OAAO,CAACF,KAAK,CAAC,CAACM,OAAO,CAACL,EAAE,CAAC,CAAC,KAAKpC,OAAO,CAACqC,OAAO,CAACF,KAAK,CAAC,GAAG,CAACC,EAAE,EAAEpC,OAAO,CAACqC,OAAO,CAACF,KAAK,CAAC,CAAC;AACtN;AACA,SAASxC,aAAaA,CAAC+C,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAChDlD,MAAM,GAAGA,MAAM,IAAIG,OAAO,CAAC,kBAAkB,CAAC;EAC9C6C,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;;EAEvB;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOE,QAAQ,KAAK,SAAS,EAAEA,QAAQ,GAAGD,MAAM,YAAYjD,MAAM;;EAEtE;EACA;EACA,IAAI,CAACmD,UAAU,GAAG,CAAC,CAACH,OAAO,CAACG,UAAU;EACtC,IAAID,QAAQ,EAAE,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,CAAC,CAACH,OAAO,CAACI,kBAAkB;;EAE/E;EACA;EACA,IAAI,CAACC,aAAa,GAAGxB,gBAAgB,CAAC,IAAI,EAAEmB,OAAO,EAAE,uBAAuB,EAAEE,QAAQ,CAAC;;EAEvF;EACA;EACA;EACA,IAAI,CAACI,MAAM,GAAG,IAAI5B,UAAU,CAAC,CAAC;EAC9B,IAAI,CAACjB,MAAM,GAAG,CAAC;EACf,IAAI,CAAC8C,KAAK,GAAG,IAAI;EACjB,IAAI,CAACC,UAAU,GAAG,CAAC;EACnB,IAAI,CAACC,OAAO,GAAG,IAAI;EACnB,IAAI,CAACC,KAAK,GAAG,KAAK;EAClB,IAAI,CAACC,UAAU,GAAG,KAAK;EACvB,IAAI,CAACC,OAAO,GAAG,KAAK;;EAEpB;EACA;EACA;EACA;EACA,IAAI,CAACC,IAAI,GAAG,IAAI;;EAEhB;EACA;EACA,IAAI,CAACC,YAAY,GAAG,KAAK;EACzB,IAAI,CAACC,eAAe,GAAG,KAAK;EAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK;EAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;EAC5B,IAAI,CAACC,MAAM,GAAG,IAAI;;EAElB;EACA,IAAI,CAACC,SAAS,GAAGnB,OAAO,CAACmB,SAAS,KAAK,KAAK;;EAE5C;EACA,IAAI,CAACC,WAAW,GAAG,CAAC,CAACpB,OAAO,CAACoB,WAAW;;EAExC;EACA,IAAI,CAACC,SAAS,GAAG,KAAK;;EAEtB;EACA;EACA;EACA,IAAI,CAACC,eAAe,GAAGtB,OAAO,CAACsB,eAAe,IAAI,MAAM;;EAExD;EACA,IAAI,CAACC,UAAU,GAAG,CAAC;;EAEnB;EACA,IAAI,CAACC,WAAW,GAAG,KAAK;EACxB,IAAI,CAACC,OAAO,GAAG,IAAI;EACnB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI1B,OAAO,CAAC0B,QAAQ,EAAE;IACpB,IAAI,CAACtC,aAAa,EAAEA,aAAa,GAAGjC,OAAO,CAAC,iBAAiB,CAAC,CAACiC,aAAa;IAC5E,IAAI,CAACqC,OAAO,GAAG,IAAIrC,aAAa,CAACY,OAAO,CAAC0B,QAAQ,CAAC;IAClD,IAAI,CAACA,QAAQ,GAAG1B,OAAO,CAAC0B,QAAQ;EAClC;AACF;AACA,SAAS3E,QAAQA,CAACiD,OAAO,EAAE;EACzBhD,MAAM,GAAGA,MAAM,IAAIG,OAAO,CAAC,kBAAkB,CAAC;EAC9C,IAAI,EAAE,IAAI,YAAYJ,QAAQ,CAAC,EAAE,OAAO,IAAIA,QAAQ,CAACiD,OAAO,CAAC;;EAE7D;EACA;EACA,IAAIE,QAAQ,GAAG,IAAI,YAAYlD,MAAM;EACrC,IAAI,CAAC2E,cAAc,GAAG,IAAI1E,aAAa,CAAC+C,OAAO,EAAE,IAAI,EAAEE,QAAQ,CAAC;;EAEhE;EACA,IAAI,CAAC0B,QAAQ,GAAG,IAAI;EACpB,IAAI5B,OAAO,EAAE;IACX,IAAI,OAAOA,OAAO,CAAC6B,IAAI,KAAK,UAAU,EAAE,IAAI,CAACC,KAAK,GAAG9B,OAAO,CAAC6B,IAAI;IACjE,IAAI,OAAO7B,OAAO,CAAC+B,OAAO,KAAK,UAAU,EAAE,IAAI,CAACC,QAAQ,GAAGhC,OAAO,CAAC+B,OAAO;EAC5E;EACArE,MAAM,CAACuE,IAAI,CAAC,IAAI,CAAC;AACnB;AACAC,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAACqF,SAAS,EAAE,WAAW,EAAE;EACrD;EACA;EACA;EACAC,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACX,cAAc,KAAKY,SAAS,EAAE;MACrC,OAAO,KAAK;IACd;IACA,OAAO,IAAI,CAACZ,cAAc,CAACN,SAAS;EACtC,CAAC;EACDmB,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;IACvB;IACA;IACA,IAAI,CAAC,IAAI,CAACd,cAAc,EAAE;MACxB;IACF;;IAEA;IACA;IACA,IAAI,CAACA,cAAc,CAACN,SAAS,GAAGoB,KAAK;EACvC;AACF,CAAC,CAAC;AACF1F,QAAQ,CAACqF,SAAS,CAACL,OAAO,GAAGpD,WAAW,CAACoD,OAAO;AAChDhF,QAAQ,CAACqF,SAAS,CAACM,UAAU,GAAG/D,WAAW,CAACgE,SAAS;AACrD5F,QAAQ,CAACqF,SAAS,CAACJ,QAAQ,GAAG,UAAUY,GAAG,EAAEC,EAAE,EAAE;EAC/CA,EAAE,CAACD,GAAG,CAAC;AACT,CAAC;;AAED;AACA;AACA;AACA;AACA7F,QAAQ,CAACqF,SAAS,CAACU,IAAI,GAAG,UAAU5E,KAAK,EAAEwD,QAAQ,EAAE;EACnD,IAAIqB,KAAK,GAAG,IAAI,CAACpB,cAAc;EAC/B,IAAIqB,cAAc;EAClB,IAAI,CAACD,KAAK,CAAC5C,UAAU,EAAE;IACrB,IAAI,OAAOjC,KAAK,KAAK,QAAQ,EAAE;MAC7BwD,QAAQ,GAAGA,QAAQ,IAAIqB,KAAK,CAACzB,eAAe;MAC5C,IAAII,QAAQ,KAAKqB,KAAK,CAACrB,QAAQ,EAAE;QAC/BxD,KAAK,GAAGP,MAAM,CAACQ,IAAI,CAACD,KAAK,EAAEwD,QAAQ,CAAC;QACpCA,QAAQ,GAAG,EAAE;MACf;MACAsB,cAAc,GAAG,IAAI;IACvB;EACF,CAAC,MAAM;IACLA,cAAc,GAAG,IAAI;EACvB;EACA,OAAOC,gBAAgB,CAAC,IAAI,EAAE/E,KAAK,EAAEwD,QAAQ,EAAE,KAAK,EAAEsB,cAAc,CAAC;AACvE,CAAC;;AAED;AACAjG,QAAQ,CAACqF,SAAS,CAACrC,OAAO,GAAG,UAAU7B,KAAK,EAAE;EAC5C,OAAO+E,gBAAgB,CAAC,IAAI,EAAE/E,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;AACzD,CAAC;AACD,SAAS+E,gBAAgBA,CAAChD,MAAM,EAAE/B,KAAK,EAAEwD,QAAQ,EAAEwB,UAAU,EAAEF,cAAc,EAAE;EAC7ExE,KAAK,CAAC,kBAAkB,EAAEN,KAAK,CAAC;EAChC,IAAI6E,KAAK,GAAG9C,MAAM,CAAC0B,cAAc;EACjC,IAAIzD,KAAK,KAAK,IAAI,EAAE;IAClB6E,KAAK,CAACnC,OAAO,GAAG,KAAK;IACrBuC,UAAU,CAAClD,MAAM,EAAE8C,KAAK,CAAC;EAC3B,CAAC,MAAM;IACL,IAAIK,EAAE;IACN,IAAI,CAACJ,cAAc,EAAEI,EAAE,GAAGC,YAAY,CAACN,KAAK,EAAE7E,KAAK,CAAC;IACpD,IAAIkF,EAAE,EAAE;MACN9D,cAAc,CAACW,MAAM,EAAEmD,EAAE,CAAC;IAC5B,CAAC,MAAM,IAAIL,KAAK,CAAC5C,UAAU,IAAIjC,KAAK,IAAIA,KAAK,CAACT,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,OAAOS,KAAK,KAAK,QAAQ,IAAI,CAAC6E,KAAK,CAAC5C,UAAU,IAAI+B,MAAM,CAACoB,cAAc,CAACpF,KAAK,CAAC,KAAKP,MAAM,CAACyE,SAAS,EAAE;QACvGlE,KAAK,GAAGD,mBAAmB,CAACC,KAAK,CAAC;MACpC;MACA,IAAIgF,UAAU,EAAE;QACd,IAAIH,KAAK,CAACpC,UAAU,EAAErB,cAAc,CAACW,MAAM,EAAE,IAAId,kCAAkC,CAAC,CAAC,CAAC,CAAC,KAAKoE,QAAQ,CAACtD,MAAM,EAAE8C,KAAK,EAAE7E,KAAK,EAAE,IAAI,CAAC;MAClI,CAAC,MAAM,IAAI6E,KAAK,CAACrC,KAAK,EAAE;QACtBpB,cAAc,CAACW,MAAM,EAAE,IAAIhB,yBAAyB,CAAC,CAAC,CAAC;MACzD,CAAC,MAAM,IAAI8D,KAAK,CAAC1B,SAAS,EAAE;QAC1B,OAAO,KAAK;MACd,CAAC,MAAM;QACL0B,KAAK,CAACnC,OAAO,GAAG,KAAK;QACrB,IAAImC,KAAK,CAACtB,OAAO,IAAI,CAACC,QAAQ,EAAE;UAC9BxD,KAAK,GAAG6E,KAAK,CAACtB,OAAO,CAAC+B,KAAK,CAACtF,KAAK,CAAC;UAClC,IAAI6E,KAAK,CAAC5C,UAAU,IAAIjC,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE8F,QAAQ,CAACtD,MAAM,EAAE8C,KAAK,EAAE7E,KAAK,EAAE,KAAK,CAAC,CAAC,KAAKuF,aAAa,CAACxD,MAAM,EAAE8C,KAAK,CAAC;QACrH,CAAC,MAAM;UACLQ,QAAQ,CAACtD,MAAM,EAAE8C,KAAK,EAAE7E,KAAK,EAAE,KAAK,CAAC;QACvC;MACF;IACF,CAAC,MAAM,IAAI,CAACgF,UAAU,EAAE;MACtBH,KAAK,CAACnC,OAAO,GAAG,KAAK;MACrB6C,aAAa,CAACxD,MAAM,EAAE8C,KAAK,CAAC;IAC9B;EACF;;EAEA;EACA;EACA;EACA,OAAO,CAACA,KAAK,CAACrC,KAAK,KAAKqC,KAAK,CAACtF,MAAM,GAAGsF,KAAK,CAAC1C,aAAa,IAAI0C,KAAK,CAACtF,MAAM,KAAK,CAAC,CAAC;AACnF;AACA,SAAS8F,QAAQA,CAACtD,MAAM,EAAE8C,KAAK,EAAE7E,KAAK,EAAEgF,UAAU,EAAE;EAClD,IAAIH,KAAK,CAACtC,OAAO,IAAIsC,KAAK,CAACtF,MAAM,KAAK,CAAC,IAAI,CAACsF,KAAK,CAAClC,IAAI,EAAE;IACtDkC,KAAK,CAACxB,UAAU,GAAG,CAAC;IACpBtB,MAAM,CAACyD,IAAI,CAAC,MAAM,EAAExF,KAAK,CAAC;EAC5B,CAAC,MAAM;IACL;IACA6E,KAAK,CAACtF,MAAM,IAAIsF,KAAK,CAAC5C,UAAU,GAAG,CAAC,GAAGjC,KAAK,CAACT,MAAM;IACnD,IAAIyF,UAAU,EAAEH,KAAK,CAACzC,MAAM,CAACP,OAAO,CAAC7B,KAAK,CAAC,CAAC,KAAK6E,KAAK,CAACzC,MAAM,CAACwC,IAAI,CAAC5E,KAAK,CAAC;IACzE,IAAI6E,KAAK,CAACjC,YAAY,EAAE6C,YAAY,CAAC1D,MAAM,CAAC;EAC9C;EACAwD,aAAa,CAACxD,MAAM,EAAE8C,KAAK,CAAC;AAC9B;AACA,SAASM,YAAYA,CAACN,KAAK,EAAE7E,KAAK,EAAE;EAClC,IAAIkF,EAAE;EACN,IAAI,CAAChF,aAAa,CAACF,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAKqE,SAAS,IAAI,CAACQ,KAAK,CAAC5C,UAAU,EAAE;IAClGiD,EAAE,GAAG,IAAIpE,oBAAoB,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,EAAEd,KAAK,CAAC;EACnF;EACA,OAAOkF,EAAE;AACX;AACArG,QAAQ,CAACqF,SAAS,CAACwB,QAAQ,GAAG,YAAY;EACxC,OAAO,IAAI,CAACjC,cAAc,CAAClB,OAAO,KAAK,KAAK;AAC9C,CAAC;;AAED;AACA1D,QAAQ,CAACqF,SAAS,CAACyB,WAAW,GAAG,UAAUC,GAAG,EAAE;EAC9C,IAAI,CAAC1E,aAAa,EAAEA,aAAa,GAAGjC,OAAO,CAAC,iBAAiB,CAAC,CAACiC,aAAa;EAC5E,IAAIqC,OAAO,GAAG,IAAIrC,aAAa,CAAC0E,GAAG,CAAC;EACpC,IAAI,CAACnC,cAAc,CAACF,OAAO,GAAGA,OAAO;EACrC;EACA,IAAI,CAACE,cAAc,CAACD,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACF,OAAO,CAACC,QAAQ;;EAEnE;EACA,IAAIqC,CAAC,GAAG,IAAI,CAACpC,cAAc,CAACrB,MAAM,CAAC0D,IAAI;EACvC,IAAIC,OAAO,GAAG,EAAE;EAChB,OAAOF,CAAC,KAAK,IAAI,EAAE;IACjBE,OAAO,IAAIxC,OAAO,CAAC+B,KAAK,CAACO,CAAC,CAACG,IAAI,CAAC;IAChCH,CAAC,GAAGA,CAAC,CAACI,IAAI;EACZ;EACA,IAAI,CAACxC,cAAc,CAACrB,MAAM,CAAC8D,KAAK,CAAC,CAAC;EAClC,IAAIH,OAAO,KAAK,EAAE,EAAE,IAAI,CAACtC,cAAc,CAACrB,MAAM,CAACwC,IAAI,CAACmB,OAAO,CAAC;EAC5D,IAAI,CAACtC,cAAc,CAAClE,MAAM,GAAGwG,OAAO,CAACxG,MAAM;EAC3C,OAAO,IAAI;AACb,CAAC;;AAED;AACA,IAAI4G,OAAO,GAAG,UAAU;AACxB,SAASC,uBAAuBA,CAACC,CAAC,EAAE;EAClC,IAAIA,CAAC,IAAIF,OAAO,EAAE;IAChB;IACAE,CAAC,GAAGF,OAAO;EACb,CAAC,MAAM;IACL;IACA;IACAE,CAAC,EAAE;IACHA,CAAC,IAAIA,CAAC,KAAK,CAAC;IACZA,CAAC,IAAIA,CAAC,KAAK,CAAC;IACZA,CAAC,IAAIA,CAAC,KAAK,CAAC;IACZA,CAAC,IAAIA,CAAC,KAAK,CAAC;IACZA,CAAC,IAAIA,CAAC,KAAK,EAAE;IACbA,CAAC,EAAE;EACL;EACA,OAAOA,CAAC;AACV;;AAEA;AACA;AACA,SAASC,aAAaA,CAACD,CAAC,EAAExB,KAAK,EAAE;EAC/B,IAAIwB,CAAC,IAAI,CAAC,IAAIxB,KAAK,CAACtF,MAAM,KAAK,CAAC,IAAIsF,KAAK,CAACrC,KAAK,EAAE,OAAO,CAAC;EACzD,IAAIqC,KAAK,CAAC5C,UAAU,EAAE,OAAO,CAAC;EAC9B,IAAIoE,CAAC,KAAKA,CAAC,EAAE;IACX;IACA,IAAIxB,KAAK,CAACtC,OAAO,IAAIsC,KAAK,CAACtF,MAAM,EAAE,OAAOsF,KAAK,CAACzC,MAAM,CAAC0D,IAAI,CAACE,IAAI,CAACzG,MAAM,CAAC,KAAK,OAAOsF,KAAK,CAACtF,MAAM;EAClG;EACA;EACA,IAAI8G,CAAC,GAAGxB,KAAK,CAAC1C,aAAa,EAAE0C,KAAK,CAAC1C,aAAa,GAAGiE,uBAAuB,CAACC,CAAC,CAAC;EAC7E,IAAIA,CAAC,IAAIxB,KAAK,CAACtF,MAAM,EAAE,OAAO8G,CAAC;EAC/B;EACA,IAAI,CAACxB,KAAK,CAACrC,KAAK,EAAE;IAChBqC,KAAK,CAACjC,YAAY,GAAG,IAAI;IACzB,OAAO,CAAC;EACV;EACA,OAAOiC,KAAK,CAACtF,MAAM;AACrB;;AAEA;AACAV,QAAQ,CAACqF,SAAS,CAACP,IAAI,GAAG,UAAU0C,CAAC,EAAE;EACrC/F,KAAK,CAAC,MAAM,EAAE+F,CAAC,CAAC;EAChBA,CAAC,GAAGE,QAAQ,CAACF,CAAC,EAAE,EAAE,CAAC;EACnB,IAAIxB,KAAK,GAAG,IAAI,CAACpB,cAAc;EAC/B,IAAI+C,KAAK,GAAGH,CAAC;EACb,IAAIA,CAAC,KAAK,CAAC,EAAExB,KAAK,CAAChC,eAAe,GAAG,KAAK;;EAE1C;EACA;EACA;EACA,IAAIwD,CAAC,KAAK,CAAC,IAAIxB,KAAK,CAACjC,YAAY,KAAK,CAACiC,KAAK,CAAC1C,aAAa,KAAK,CAAC,GAAG0C,KAAK,CAACtF,MAAM,IAAIsF,KAAK,CAAC1C,aAAa,GAAG0C,KAAK,CAACtF,MAAM,GAAG,CAAC,KAAKsF,KAAK,CAACrC,KAAK,CAAC,EAAE;IAC1IlC,KAAK,CAAC,oBAAoB,EAAEuE,KAAK,CAACtF,MAAM,EAAEsF,KAAK,CAACrC,KAAK,CAAC;IACtD,IAAIqC,KAAK,CAACtF,MAAM,KAAK,CAAC,IAAIsF,KAAK,CAACrC,KAAK,EAAEiE,WAAW,CAAC,IAAI,CAAC,CAAC,KAAKhB,YAAY,CAAC,IAAI,CAAC;IAChF,OAAO,IAAI;EACb;EACAY,CAAC,GAAGC,aAAa,CAACD,CAAC,EAAExB,KAAK,CAAC;;EAE3B;EACA,IAAIwB,CAAC,KAAK,CAAC,IAAIxB,KAAK,CAACrC,KAAK,EAAE;IAC1B,IAAIqC,KAAK,CAACtF,MAAM,KAAK,CAAC,EAAEkH,WAAW,CAAC,IAAI,CAAC;IACzC,OAAO,IAAI;EACb;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,IAAIC,MAAM,GAAG7B,KAAK,CAACjC,YAAY;EAC/BtC,KAAK,CAAC,eAAe,EAAEoG,MAAM,CAAC;;EAE9B;EACA,IAAI7B,KAAK,CAACtF,MAAM,KAAK,CAAC,IAAIsF,KAAK,CAACtF,MAAM,GAAG8G,CAAC,GAAGxB,KAAK,CAAC1C,aAAa,EAAE;IAChEuE,MAAM,GAAG,IAAI;IACbpG,KAAK,CAAC,4BAA4B,EAAEoG,MAAM,CAAC;EAC7C;;EAEA;EACA;EACA,IAAI7B,KAAK,CAACrC,KAAK,IAAIqC,KAAK,CAACnC,OAAO,EAAE;IAChCgE,MAAM,GAAG,KAAK;IACdpG,KAAK,CAAC,kBAAkB,EAAEoG,MAAM,CAAC;EACnC,CAAC,MAAM,IAAIA,MAAM,EAAE;IACjBpG,KAAK,CAAC,SAAS,CAAC;IAChBuE,KAAK,CAACnC,OAAO,GAAG,IAAI;IACpBmC,KAAK,CAAClC,IAAI,GAAG,IAAI;IACjB;IACA,IAAIkC,KAAK,CAACtF,MAAM,KAAK,CAAC,EAAEsF,KAAK,CAACjC,YAAY,GAAG,IAAI;IACjD;IACA,IAAI,CAACgB,KAAK,CAACiB,KAAK,CAAC1C,aAAa,CAAC;IAC/B0C,KAAK,CAAClC,IAAI,GAAG,KAAK;IAClB;IACA;IACA,IAAI,CAACkC,KAAK,CAACnC,OAAO,EAAE2D,CAAC,GAAGC,aAAa,CAACE,KAAK,EAAE3B,KAAK,CAAC;EACrD;EACA,IAAI8B,GAAG;EACP,IAAIN,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGC,QAAQ,CAACP,CAAC,EAAExB,KAAK,CAAC,CAAC,KAAK8B,GAAG,GAAG,IAAI;EACnD,IAAIA,GAAG,KAAK,IAAI,EAAE;IAChB9B,KAAK,CAACjC,YAAY,GAAGiC,KAAK,CAACtF,MAAM,IAAIsF,KAAK,CAAC1C,aAAa;IACxDkE,CAAC,GAAG,CAAC;EACP,CAAC,MAAM;IACLxB,KAAK,CAACtF,MAAM,IAAI8G,CAAC;IACjBxB,KAAK,CAACxB,UAAU,GAAG,CAAC;EACtB;EACA,IAAIwB,KAAK,CAACtF,MAAM,KAAK,CAAC,EAAE;IACtB;IACA;IACA,IAAI,CAACsF,KAAK,CAACrC,KAAK,EAAEqC,KAAK,CAACjC,YAAY,GAAG,IAAI;;IAE3C;IACA,IAAI4D,KAAK,KAAKH,CAAC,IAAIxB,KAAK,CAACrC,KAAK,EAAEiE,WAAW,CAAC,IAAI,CAAC;EACnD;EACA,IAAIE,GAAG,KAAK,IAAI,EAAE,IAAI,CAACnB,IAAI,CAAC,MAAM,EAAEmB,GAAG,CAAC;EACxC,OAAOA,GAAG;AACZ,CAAC;AACD,SAAS1B,UAAUA,CAAClD,MAAM,EAAE8C,KAAK,EAAE;EACjCvE,KAAK,CAAC,YAAY,CAAC;EACnB,IAAIuE,KAAK,CAACrC,KAAK,EAAE;EACjB,IAAIqC,KAAK,CAACtB,OAAO,EAAE;IACjB,IAAIvD,KAAK,GAAG6E,KAAK,CAACtB,OAAO,CAACsD,GAAG,CAAC,CAAC;IAC/B,IAAI7G,KAAK,IAAIA,KAAK,CAACT,MAAM,EAAE;MACzBsF,KAAK,CAACzC,MAAM,CAACwC,IAAI,CAAC5E,KAAK,CAAC;MACxB6E,KAAK,CAACtF,MAAM,IAAIsF,KAAK,CAAC5C,UAAU,GAAG,CAAC,GAAGjC,KAAK,CAACT,MAAM;IACrD;EACF;EACAsF,KAAK,CAACrC,KAAK,GAAG,IAAI;EAClB,IAAIqC,KAAK,CAAClC,IAAI,EAAE;IACd;IACA;IACA;IACA8C,YAAY,CAAC1D,MAAM,CAAC;EACtB,CAAC,MAAM;IACL;IACA8C,KAAK,CAACjC,YAAY,GAAG,KAAK;IAC1B,IAAI,CAACiC,KAAK,CAAChC,eAAe,EAAE;MAC1BgC,KAAK,CAAChC,eAAe,GAAG,IAAI;MAC5BiE,aAAa,CAAC/E,MAAM,CAAC;IACvB;EACF;AACF;;AAEA;AACA;AACA;AACA,SAAS0D,YAAYA,CAAC1D,MAAM,EAAE;EAC5B,IAAI8C,KAAK,GAAG9C,MAAM,CAAC0B,cAAc;EACjCnD,KAAK,CAAC,cAAc,EAAEuE,KAAK,CAACjC,YAAY,EAAEiC,KAAK,CAAChC,eAAe,CAAC;EAChEgC,KAAK,CAACjC,YAAY,GAAG,KAAK;EAC1B,IAAI,CAACiC,KAAK,CAAChC,eAAe,EAAE;IAC1BvC,KAAK,CAAC,cAAc,EAAEuE,KAAK,CAACtC,OAAO,CAAC;IACpCsC,KAAK,CAAChC,eAAe,GAAG,IAAI;IAC5BkE,OAAO,CAACC,QAAQ,CAACF,aAAa,EAAE/E,MAAM,CAAC;EACzC;AACF;AACA,SAAS+E,aAAaA,CAAC/E,MAAM,EAAE;EAC7B,IAAI8C,KAAK,GAAG9C,MAAM,CAAC0B,cAAc;EACjCnD,KAAK,CAAC,eAAe,EAAEuE,KAAK,CAAC1B,SAAS,EAAE0B,KAAK,CAACtF,MAAM,EAAEsF,KAAK,CAACrC,KAAK,CAAC;EAClE,IAAI,CAACqC,KAAK,CAAC1B,SAAS,KAAK0B,KAAK,CAACtF,MAAM,IAAIsF,KAAK,CAACrC,KAAK,CAAC,EAAE;IACrDT,MAAM,CAACyD,IAAI,CAAC,UAAU,CAAC;IACvBX,KAAK,CAAChC,eAAe,GAAG,KAAK;EAC/B;;EAEA;EACA;EACA;EACA;EACA;EACA;EACAgC,KAAK,CAACjC,YAAY,GAAG,CAACiC,KAAK,CAACtC,OAAO,IAAI,CAACsC,KAAK,CAACrC,KAAK,IAAIqC,KAAK,CAACtF,MAAM,IAAIsF,KAAK,CAAC1C,aAAa;EAC1F8E,IAAI,CAAClF,MAAM,CAAC;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwD,aAAaA,CAACxD,MAAM,EAAE8C,KAAK,EAAE;EACpC,IAAI,CAACA,KAAK,CAACvB,WAAW,EAAE;IACtBuB,KAAK,CAACvB,WAAW,GAAG,IAAI;IACxByD,OAAO,CAACC,QAAQ,CAACE,cAAc,EAAEnF,MAAM,EAAE8C,KAAK,CAAC;EACjD;AACF;AACA,SAASqC,cAAcA,CAACnF,MAAM,EAAE8C,KAAK,EAAE;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAO,CAACA,KAAK,CAACnC,OAAO,IAAI,CAACmC,KAAK,CAACrC,KAAK,KAAKqC,KAAK,CAACtF,MAAM,GAAGsF,KAAK,CAAC1C,aAAa,IAAI0C,KAAK,CAACtC,OAAO,IAAIsC,KAAK,CAACtF,MAAM,KAAK,CAAC,CAAC,EAAE;IACpH,IAAI4H,GAAG,GAAGtC,KAAK,CAACtF,MAAM;IACtBe,KAAK,CAAC,sBAAsB,CAAC;IAC7ByB,MAAM,CAAC4B,IAAI,CAAC,CAAC,CAAC;IACd,IAAIwD,GAAG,KAAKtC,KAAK,CAACtF,MAAM;MACtB;MACA;EACJ;EACAsF,KAAK,CAACvB,WAAW,GAAG,KAAK;AAC3B;;AAEA;AACA;AACA;AACA;AACAzE,QAAQ,CAACqF,SAAS,CAACN,KAAK,GAAG,UAAUyC,CAAC,EAAE;EACtCjF,cAAc,CAAC,IAAI,EAAE,IAAIJ,0BAA0B,CAAC,SAAS,CAAC,CAAC;AACjE,CAAC;AACDnC,QAAQ,CAACqF,SAAS,CAACkD,IAAI,GAAG,UAAUC,IAAI,EAAEC,QAAQ,EAAE;EAClD,IAAIC,GAAG,GAAG,IAAI;EACd,IAAI1C,KAAK,GAAG,IAAI,CAACpB,cAAc;EAC/B,QAAQoB,KAAK,CAACvC,UAAU;IACtB,KAAK,CAAC;MACJuC,KAAK,CAACxC,KAAK,GAAGgF,IAAI;MAClB;IACF,KAAK,CAAC;MACJxC,KAAK,CAACxC,KAAK,GAAG,CAACwC,KAAK,CAACxC,KAAK,EAAEgF,IAAI,CAAC;MACjC;IACF;MACExC,KAAK,CAACxC,KAAK,CAACuC,IAAI,CAACyC,IAAI,CAAC;MACtB;EACJ;EACAxC,KAAK,CAACvC,UAAU,IAAI,CAAC;EACrBhC,KAAK,CAAC,uBAAuB,EAAEuE,KAAK,CAACvC,UAAU,EAAEgF,QAAQ,CAAC;EAC1D,IAAIE,KAAK,GAAG,CAAC,CAACF,QAAQ,IAAIA,QAAQ,CAACT,GAAG,KAAK,KAAK,KAAKQ,IAAI,KAAKN,OAAO,CAACU,MAAM,IAAIJ,IAAI,KAAKN,OAAO,CAACW,MAAM;EACvG,IAAIC,KAAK,GAAGH,KAAK,GAAGI,KAAK,GAAGC,MAAM;EAClC,IAAIhD,KAAK,CAACpC,UAAU,EAAEsE,OAAO,CAACC,QAAQ,CAACW,KAAK,CAAC,CAAC,KAAKJ,GAAG,CAACO,IAAI,CAAC,KAAK,EAAEH,KAAK,CAAC;EACzEN,IAAI,CAAC3F,EAAE,CAAC,QAAQ,EAAEqG,QAAQ,CAAC;EAC3B,SAASA,QAAQA,CAACrE,QAAQ,EAAEsE,UAAU,EAAE;IACtC1H,KAAK,CAAC,UAAU,CAAC;IACjB,IAAIoD,QAAQ,KAAK6D,GAAG,EAAE;MACpB,IAAIS,UAAU,IAAIA,UAAU,CAACC,UAAU,KAAK,KAAK,EAAE;QACjDD,UAAU,CAACC,UAAU,GAAG,IAAI;QAC5BC,OAAO,CAAC,CAAC;MACX;IACF;EACF;EACA,SAASN,KAAKA,CAAA,EAAG;IACftH,KAAK,CAAC,OAAO,CAAC;IACd+G,IAAI,CAACR,GAAG,CAAC,CAAC;EACZ;;EAEA;EACA;EACA;EACA;EACA,IAAIsB,OAAO,GAAGC,WAAW,CAACb,GAAG,CAAC;EAC9BF,IAAI,CAAC3F,EAAE,CAAC,OAAO,EAAEyG,OAAO,CAAC;EACzB,IAAIE,SAAS,GAAG,KAAK;EACrB,SAASH,OAAOA,CAAA,EAAG;IACjB5H,KAAK,CAAC,SAAS,CAAC;IAChB;IACA+G,IAAI,CAACiB,cAAc,CAAC,OAAO,EAAEC,OAAO,CAAC;IACrClB,IAAI,CAACiB,cAAc,CAAC,QAAQ,EAAEE,QAAQ,CAAC;IACvCnB,IAAI,CAACiB,cAAc,CAAC,OAAO,EAAEH,OAAO,CAAC;IACrCd,IAAI,CAACiB,cAAc,CAAC,OAAO,EAAEG,OAAO,CAAC;IACrCpB,IAAI,CAACiB,cAAc,CAAC,QAAQ,EAAEP,QAAQ,CAAC;IACvCR,GAAG,CAACe,cAAc,CAAC,KAAK,EAAEV,KAAK,CAAC;IAChCL,GAAG,CAACe,cAAc,CAAC,KAAK,EAAET,MAAM,CAAC;IACjCN,GAAG,CAACe,cAAc,CAAC,MAAM,EAAEI,MAAM,CAAC;IAClCL,SAAS,GAAG,IAAI;;IAEhB;IACA;IACA;IACA;IACA;IACA,IAAIxD,KAAK,CAACxB,UAAU,KAAK,CAACgE,IAAI,CAACsB,cAAc,IAAItB,IAAI,CAACsB,cAAc,CAACC,SAAS,CAAC,EAAET,OAAO,CAAC,CAAC;EAC5F;EACAZ,GAAG,CAAC7F,EAAE,CAAC,MAAM,EAAEgH,MAAM,CAAC;EACtB,SAASA,MAAMA,CAAC1I,KAAK,EAAE;IACrBM,KAAK,CAAC,QAAQ,CAAC;IACf,IAAIqG,GAAG,GAAGU,IAAI,CAAC/B,KAAK,CAACtF,KAAK,CAAC;IAC3BM,KAAK,CAAC,YAAY,EAAEqG,GAAG,CAAC;IACxB,IAAIA,GAAG,KAAK,KAAK,EAAE;MACjB;MACA;MACA;MACA;MACA,IAAI,CAAC9B,KAAK,CAACvC,UAAU,KAAK,CAAC,IAAIuC,KAAK,CAACxC,KAAK,KAAKgF,IAAI,IAAIxC,KAAK,CAACvC,UAAU,GAAG,CAAC,IAAIuG,OAAO,CAAChE,KAAK,CAACxC,KAAK,EAAEgF,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAACgB,SAAS,EAAE;QAC/H/H,KAAK,CAAC,6BAA6B,EAAEuE,KAAK,CAACxB,UAAU,CAAC;QACtDwB,KAAK,CAACxB,UAAU,EAAE;MACpB;MACAkE,GAAG,CAACuB,KAAK,CAAC,CAAC;IACb;EACF;;EAEA;EACA;EACA,SAASL,OAAOA,CAACvD,EAAE,EAAE;IACnB5E,KAAK,CAAC,SAAS,EAAE4E,EAAE,CAAC;IACpB2C,MAAM,CAAC,CAAC;IACRR,IAAI,CAACiB,cAAc,CAAC,OAAO,EAAEG,OAAO,CAAC;IACrC,IAAItJ,eAAe,CAACkI,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EAAEjG,cAAc,CAACiG,IAAI,EAAEnC,EAAE,CAAC;EACpE;;EAEA;EACA5D,eAAe,CAAC+F,IAAI,EAAE,OAAO,EAAEoB,OAAO,CAAC;;EAEvC;EACA,SAASF,OAAOA,CAAA,EAAG;IACjBlB,IAAI,CAACiB,cAAc,CAAC,QAAQ,EAAEE,QAAQ,CAAC;IACvCX,MAAM,CAAC,CAAC;EACV;EACAR,IAAI,CAACS,IAAI,CAAC,OAAO,EAAES,OAAO,CAAC;EAC3B,SAASC,QAAQA,CAAA,EAAG;IAClBlI,KAAK,CAAC,UAAU,CAAC;IACjB+G,IAAI,CAACiB,cAAc,CAAC,OAAO,EAAEC,OAAO,CAAC;IACrCV,MAAM,CAAC,CAAC;EACV;EACAR,IAAI,CAACS,IAAI,CAAC,QAAQ,EAAEU,QAAQ,CAAC;EAC7B,SAASX,MAAMA,CAAA,EAAG;IAChBvH,KAAK,CAAC,QAAQ,CAAC;IACfiH,GAAG,CAACM,MAAM,CAACR,IAAI,CAAC;EAClB;;EAEA;EACAA,IAAI,CAAC7B,IAAI,CAAC,MAAM,EAAE+B,GAAG,CAAC;;EAEtB;EACA,IAAI,CAAC1C,KAAK,CAACtC,OAAO,EAAE;IAClBjC,KAAK,CAAC,aAAa,CAAC;IACpBiH,GAAG,CAACwB,MAAM,CAAC,CAAC;EACd;EACA,OAAO1B,IAAI;AACb,CAAC;AACD,SAASe,WAAWA,CAACb,GAAG,EAAE;EACxB,OAAO,SAASyB,yBAAyBA,CAAA,EAAG;IAC1C,IAAInE,KAAK,GAAG0C,GAAG,CAAC9D,cAAc;IAC9BnD,KAAK,CAAC,aAAa,EAAEuE,KAAK,CAACxB,UAAU,CAAC;IACtC,IAAIwB,KAAK,CAACxB,UAAU,EAAEwB,KAAK,CAACxB,UAAU,EAAE;IACxC,IAAIwB,KAAK,CAACxB,UAAU,KAAK,CAAC,IAAIlE,eAAe,CAACoI,GAAG,EAAE,MAAM,CAAC,EAAE;MAC1D1C,KAAK,CAACtC,OAAO,GAAG,IAAI;MACpB0E,IAAI,CAACM,GAAG,CAAC;IACX;EACF,CAAC;AACH;AACA1I,QAAQ,CAACqF,SAAS,CAAC2D,MAAM,GAAG,UAAUR,IAAI,EAAE;EAC1C,IAAIxC,KAAK,GAAG,IAAI,CAACpB,cAAc;EAC/B,IAAIuE,UAAU,GAAG;IACfC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,IAAIpD,KAAK,CAACvC,UAAU,KAAK,CAAC,EAAE,OAAO,IAAI;;EAEvC;EACA,IAAIuC,KAAK,CAACvC,UAAU,KAAK,CAAC,EAAE;IAC1B;IACA,IAAI+E,IAAI,IAAIA,IAAI,KAAKxC,KAAK,CAACxC,KAAK,EAAE,OAAO,IAAI;IAC7C,IAAI,CAACgF,IAAI,EAAEA,IAAI,GAAGxC,KAAK,CAACxC,KAAK;;IAE7B;IACAwC,KAAK,CAACxC,KAAK,GAAG,IAAI;IAClBwC,KAAK,CAACvC,UAAU,GAAG,CAAC;IACpBuC,KAAK,CAACtC,OAAO,GAAG,KAAK;IACrB,IAAI8E,IAAI,EAAEA,IAAI,CAAC7B,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAEwC,UAAU,CAAC;IAC/C,OAAO,IAAI;EACb;;EAEA;;EAEA,IAAI,CAACX,IAAI,EAAE;IACT;IACA,IAAI4B,KAAK,GAAGpE,KAAK,CAACxC,KAAK;IACvB,IAAI8E,GAAG,GAAGtC,KAAK,CAACvC,UAAU;IAC1BuC,KAAK,CAACxC,KAAK,GAAG,IAAI;IAClBwC,KAAK,CAACvC,UAAU,GAAG,CAAC;IACpBuC,KAAK,CAACtC,OAAO,GAAG,KAAK;IACrB,KAAK,IAAI2G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,GAAG,EAAE+B,CAAC,EAAE,EAAED,KAAK,CAACC,CAAC,CAAC,CAAC1D,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE;MAC1DyC,UAAU,EAAE;IACd,CAAC,CAAC;IACF,OAAO,IAAI;EACb;;EAEA;EACA,IAAIkB,KAAK,GAAGN,OAAO,CAAChE,KAAK,CAACxC,KAAK,EAAEgF,IAAI,CAAC;EACtC,IAAI8B,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;EAC7BtE,KAAK,CAACxC,KAAK,CAAC+G,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EAC5BtE,KAAK,CAACvC,UAAU,IAAI,CAAC;EACrB,IAAIuC,KAAK,CAACvC,UAAU,KAAK,CAAC,EAAEuC,KAAK,CAACxC,KAAK,GAAGwC,KAAK,CAACxC,KAAK,CAAC,CAAC,CAAC;EACxDgF,IAAI,CAAC7B,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAEwC,UAAU,CAAC;EACrC,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACAnJ,QAAQ,CAACqF,SAAS,CAACxC,EAAE,GAAG,UAAU2H,EAAE,EAAE7H,EAAE,EAAE;EACxC,IAAI8H,GAAG,GAAG9J,MAAM,CAAC0E,SAAS,CAACxC,EAAE,CAACqC,IAAI,CAAC,IAAI,EAAEsF,EAAE,EAAE7H,EAAE,CAAC;EAChD,IAAIqD,KAAK,GAAG,IAAI,CAACpB,cAAc;EAC/B,IAAI4F,EAAE,KAAK,MAAM,EAAE;IACjB;IACA;IACAxE,KAAK,CAAC/B,iBAAiB,GAAG,IAAI,CAACyG,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC;;IAE5D;IACA,IAAI1E,KAAK,CAACtC,OAAO,KAAK,KAAK,EAAE,IAAI,CAACwG,MAAM,CAAC,CAAC;EAC5C,CAAC,MAAM,IAAIM,EAAE,KAAK,UAAU,EAAE;IAC5B,IAAI,CAACxE,KAAK,CAACpC,UAAU,IAAI,CAACoC,KAAK,CAAC/B,iBAAiB,EAAE;MACjD+B,KAAK,CAAC/B,iBAAiB,GAAG+B,KAAK,CAACjC,YAAY,GAAG,IAAI;MACnDiC,KAAK,CAACtC,OAAO,GAAG,KAAK;MACrBsC,KAAK,CAAChC,eAAe,GAAG,KAAK;MAC7BvC,KAAK,CAAC,aAAa,EAAEuE,KAAK,CAACtF,MAAM,EAAEsF,KAAK,CAACnC,OAAO,CAAC;MACjD,IAAImC,KAAK,CAACtF,MAAM,EAAE;QAChBkG,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,MAAM,IAAI,CAACZ,KAAK,CAACnC,OAAO,EAAE;QACzBqE,OAAO,CAACC,QAAQ,CAACwC,gBAAgB,EAAE,IAAI,CAAC;MAC1C;IACF;EACF;EACA,OAAOF,GAAG;AACZ,CAAC;AACDzK,QAAQ,CAACqF,SAAS,CAACuF,WAAW,GAAG5K,QAAQ,CAACqF,SAAS,CAACxC,EAAE;AACtD7C,QAAQ,CAACqF,SAAS,CAACoE,cAAc,GAAG,UAAUe,EAAE,EAAE7H,EAAE,EAAE;EACpD,IAAI8H,GAAG,GAAG9J,MAAM,CAAC0E,SAAS,CAACoE,cAAc,CAACvE,IAAI,CAAC,IAAI,EAAEsF,EAAE,EAAE7H,EAAE,CAAC;EAC5D,IAAI6H,EAAE,KAAK,UAAU,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACAtC,OAAO,CAACC,QAAQ,CAAC0C,uBAAuB,EAAE,IAAI,CAAC;EACjD;EACA,OAAOJ,GAAG;AACZ,CAAC;AACDzK,QAAQ,CAACqF,SAAS,CAACyF,kBAAkB,GAAG,UAAUN,EAAE,EAAE;EACpD,IAAIC,GAAG,GAAG9J,MAAM,CAAC0E,SAAS,CAACyF,kBAAkB,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACpE,IAAIR,EAAE,KAAK,UAAU,IAAIA,EAAE,KAAKhF,SAAS,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA;IACA0C,OAAO,CAACC,QAAQ,CAAC0C,uBAAuB,EAAE,IAAI,CAAC;EACjD;EACA,OAAOJ,GAAG;AACZ,CAAC;AACD,SAASI,uBAAuBA,CAAC7J,IAAI,EAAE;EACrC,IAAIgF,KAAK,GAAGhF,IAAI,CAAC4D,cAAc;EAC/BoB,KAAK,CAAC/B,iBAAiB,GAAGjD,IAAI,CAAC0J,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC;EAC5D,IAAI1E,KAAK,CAAC9B,eAAe,IAAI,CAAC8B,KAAK,CAAC7B,MAAM,EAAE;IAC1C;IACA;IACA6B,KAAK,CAACtC,OAAO,GAAG,IAAI;;IAEpB;EACF,CAAC,MAAM,IAAI1C,IAAI,CAAC0J,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;IACzC1J,IAAI,CAACkJ,MAAM,CAAC,CAAC;EACf;AACF;AACA,SAASS,gBAAgBA,CAAC3J,IAAI,EAAE;EAC9BS,KAAK,CAAC,0BAA0B,CAAC;EACjCT,IAAI,CAAC8D,IAAI,CAAC,CAAC,CAAC;AACd;;AAEA;AACA;AACA9E,QAAQ,CAACqF,SAAS,CAAC6E,MAAM,GAAG,YAAY;EACtC,IAAIlE,KAAK,GAAG,IAAI,CAACpB,cAAc;EAC/B,IAAI,CAACoB,KAAK,CAACtC,OAAO,EAAE;IAClBjC,KAAK,CAAC,QAAQ,CAAC;IACf;IACA;IACA;IACAuE,KAAK,CAACtC,OAAO,GAAG,CAACsC,KAAK,CAAC/B,iBAAiB;IACxCiG,MAAM,CAAC,IAAI,EAAElE,KAAK,CAAC;EACrB;EACAA,KAAK,CAAC7B,MAAM,GAAG,KAAK;EACpB,OAAO,IAAI;AACb,CAAC;AACD,SAAS+F,MAAMA,CAAChH,MAAM,EAAE8C,KAAK,EAAE;EAC7B,IAAI,CAACA,KAAK,CAAC9B,eAAe,EAAE;IAC1B8B,KAAK,CAAC9B,eAAe,GAAG,IAAI;IAC5BgE,OAAO,CAACC,QAAQ,CAAC8C,OAAO,EAAE/H,MAAM,EAAE8C,KAAK,CAAC;EAC1C;AACF;AACA,SAASiF,OAAOA,CAAC/H,MAAM,EAAE8C,KAAK,EAAE;EAC9BvE,KAAK,CAAC,QAAQ,EAAEuE,KAAK,CAACnC,OAAO,CAAC;EAC9B,IAAI,CAACmC,KAAK,CAACnC,OAAO,EAAE;IAClBX,MAAM,CAAC4B,IAAI,CAAC,CAAC,CAAC;EAChB;EACAkB,KAAK,CAAC9B,eAAe,GAAG,KAAK;EAC7BhB,MAAM,CAACyD,IAAI,CAAC,QAAQ,CAAC;EACrByB,IAAI,CAAClF,MAAM,CAAC;EACZ,IAAI8C,KAAK,CAACtC,OAAO,IAAI,CAACsC,KAAK,CAACnC,OAAO,EAAEX,MAAM,CAAC4B,IAAI,CAAC,CAAC,CAAC;AACrD;AACA9E,QAAQ,CAACqF,SAAS,CAAC4E,KAAK,GAAG,YAAY;EACrCxI,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAACmD,cAAc,CAAClB,OAAO,CAAC;EAC3D,IAAI,IAAI,CAACkB,cAAc,CAAClB,OAAO,KAAK,KAAK,EAAE;IACzCjC,KAAK,CAAC,OAAO,CAAC;IACd,IAAI,CAACmD,cAAc,CAAClB,OAAO,GAAG,KAAK;IACnC,IAAI,CAACiD,IAAI,CAAC,OAAO,CAAC;EACpB;EACA,IAAI,CAAC/B,cAAc,CAACT,MAAM,GAAG,IAAI;EACjC,OAAO,IAAI;AACb,CAAC;AACD,SAASiE,IAAIA,CAAClF,MAAM,EAAE;EACpB,IAAI8C,KAAK,GAAG9C,MAAM,CAAC0B,cAAc;EACjCnD,KAAK,CAAC,MAAM,EAAEuE,KAAK,CAACtC,OAAO,CAAC;EAC5B,OAAOsC,KAAK,CAACtC,OAAO,IAAIR,MAAM,CAAC4B,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC;AACjD;;AAEA;AACA;AACA;AACA9E,QAAQ,CAACqF,SAAS,CAAC6F,IAAI,GAAG,UAAUhI,MAAM,EAAE;EAC1C,IAAIiI,KAAK,GAAG,IAAI;EAChB,IAAInF,KAAK,GAAG,IAAI,CAACpB,cAAc;EAC/B,IAAIT,MAAM,GAAG,KAAK;EAClBjB,MAAM,CAACL,EAAE,CAAC,KAAK,EAAE,YAAY;IAC3BpB,KAAK,CAAC,aAAa,CAAC;IACpB,IAAIuE,KAAK,CAACtB,OAAO,IAAI,CAACsB,KAAK,CAACrC,KAAK,EAAE;MACjC,IAAIxC,KAAK,GAAG6E,KAAK,CAACtB,OAAO,CAACsD,GAAG,CAAC,CAAC;MAC/B,IAAI7G,KAAK,IAAIA,KAAK,CAACT,MAAM,EAAEyK,KAAK,CAACpF,IAAI,CAAC5E,KAAK,CAAC;IAC9C;IACAgK,KAAK,CAACpF,IAAI,CAAC,IAAI,CAAC;EAClB,CAAC,CAAC;EACF7C,MAAM,CAACL,EAAE,CAAC,MAAM,EAAE,UAAU1B,KAAK,EAAE;IACjCM,KAAK,CAAC,cAAc,CAAC;IACrB,IAAIuE,KAAK,CAACtB,OAAO,EAAEvD,KAAK,GAAG6E,KAAK,CAACtB,OAAO,CAAC+B,KAAK,CAACtF,KAAK,CAAC;;IAErD;IACA,IAAI6E,KAAK,CAAC5C,UAAU,KAAKjC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKqE,SAAS,CAAC,EAAE,OAAO,KAAK,IAAI,CAACQ,KAAK,CAAC5C,UAAU,KAAK,CAACjC,KAAK,IAAI,CAACA,KAAK,CAACT,MAAM,CAAC,EAAE;IACjI,IAAIoH,GAAG,GAAGqD,KAAK,CAACpF,IAAI,CAAC5E,KAAK,CAAC;IAC3B,IAAI,CAAC2G,GAAG,EAAE;MACR3D,MAAM,GAAG,IAAI;MACbjB,MAAM,CAAC+G,KAAK,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;;EAEF;EACA;EACA,KAAK,IAAII,CAAC,IAAInH,MAAM,EAAE;IACpB,IAAI,IAAI,CAACmH,CAAC,CAAC,KAAK7E,SAAS,IAAI,OAAOtC,MAAM,CAACmH,CAAC,CAAC,KAAK,UAAU,EAAE;MAC5D,IAAI,CAACA,CAAC,CAAC,GAAG,SAASe,UAAUA,CAACC,MAAM,EAAE;QACpC,OAAO,SAASC,wBAAwBA,CAAA,EAAG;UACzC,OAAOpI,MAAM,CAACmI,MAAM,CAAC,CAACN,KAAK,CAAC7H,MAAM,EAAE8H,SAAS,CAAC;QAChD,CAAC;MACH,CAAC,CAACX,CAAC,CAAC;IACN;EACF;;EAEA;EACA,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhF,YAAY,CAAC9B,MAAM,EAAE8G,CAAC,EAAE,EAAE;IAC5CtE,MAAM,CAACL,EAAE,CAACL,YAAY,CAACgF,CAAC,CAAC,EAAE,IAAI,CAACb,IAAI,CAAC4E,IAAI,CAAC,IAAI,EAAE/I,YAAY,CAACgF,CAAC,CAAC,CAAC,CAAC;EACnE;;EAEA;EACA;EACA,IAAI,CAACzC,KAAK,GAAG,UAAUyC,CAAC,EAAE;IACxB/F,KAAK,CAAC,eAAe,EAAE+F,CAAC,CAAC;IACzB,IAAIrD,MAAM,EAAE;MACVA,MAAM,GAAG,KAAK;MACdjB,MAAM,CAACgH,MAAM,CAAC,CAAC;IACjB;EACF,CAAC;EACD,OAAO,IAAI;AACb,CAAC;AACD,IAAI,OAAOsB,MAAM,KAAK,UAAU,EAAE;EAChCxL,QAAQ,CAACqF,SAAS,CAACmG,MAAM,CAACC,aAAa,CAAC,GAAG,YAAY;IACrD,IAAInJ,iCAAiC,KAAKkD,SAAS,EAAE;MACnDlD,iCAAiC,GAAGlC,OAAO,CAAC,mCAAmC,CAAC;IAClF;IACA,OAAOkC,iCAAiC,CAAC,IAAI,CAAC;EAChD,CAAC;AACH;AACA6C,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAACqF,SAAS,EAAE,uBAAuB,EAAE;EACjE;EACA;EACA;EACAC,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACX,cAAc,CAACtB,aAAa;EAC1C;AACF,CAAC,CAAC;AACF6B,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAACqF,SAAS,EAAE,gBAAgB,EAAE;EAC1D;EACA;EACA;EACAC,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACX,cAAc,IAAI,IAAI,CAACA,cAAc,CAACrB,MAAM;EAC1D;AACF,CAAC,CAAC;AACF4B,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAACqF,SAAS,EAAE,iBAAiB,EAAE;EAC3D;EACA;EACA;EACAC,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACX,cAAc,CAAClB,OAAO;EACpC,CAAC;EACD+B,GAAG,EAAE,SAASA,GAAGA,CAACO,KAAK,EAAE;IACvB,IAAI,IAAI,CAACpB,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAClB,OAAO,GAAGsC,KAAK;IACrC;EACF;AACF,CAAC,CAAC;;AAEF;AACAhG,QAAQ,CAAC0L,SAAS,GAAG3D,QAAQ;AAC7B5C,MAAM,CAACC,cAAc,CAACpF,QAAQ,CAACqF,SAAS,EAAE,gBAAgB,EAAE;EAC1D;EACA;EACA;EACAC,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACX,cAAc,CAAClE,MAAM;EACnC;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,SAASqH,QAAQA,CAACP,CAAC,EAAExB,KAAK,EAAE;EAC1B;EACA,IAAIA,KAAK,CAACtF,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EACnC,IAAIoH,GAAG;EACP,IAAI9B,KAAK,CAAC5C,UAAU,EAAE0E,GAAG,GAAG9B,KAAK,CAACzC,MAAM,CAACoI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAACnE,CAAC,IAAIA,CAAC,IAAIxB,KAAK,CAACtF,MAAM,EAAE;IACjF;IACA,IAAIsF,KAAK,CAACtB,OAAO,EAAEoD,GAAG,GAAG9B,KAAK,CAACzC,MAAM,CAACqI,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI5F,KAAK,CAACzC,MAAM,CAAC7C,MAAM,KAAK,CAAC,EAAEoH,GAAG,GAAG9B,KAAK,CAACzC,MAAM,CAACsI,KAAK,CAAC,CAAC,CAAC,KAAK/D,GAAG,GAAG9B,KAAK,CAACzC,MAAM,CAACuI,MAAM,CAAC9F,KAAK,CAACtF,MAAM,CAAC;IAC1JsF,KAAK,CAACzC,MAAM,CAAC8D,KAAK,CAAC,CAAC;EACtB,CAAC,MAAM;IACL;IACAS,GAAG,GAAG9B,KAAK,CAACzC,MAAM,CAACwI,OAAO,CAACvE,CAAC,EAAExB,KAAK,CAACtB,OAAO,CAAC;EAC9C;EACA,OAAOoD,GAAG;AACZ;AACA,SAASF,WAAWA,CAAC1E,MAAM,EAAE;EAC3B,IAAI8C,KAAK,GAAG9C,MAAM,CAAC0B,cAAc;EACjCnD,KAAK,CAAC,aAAa,EAAEuE,KAAK,CAACpC,UAAU,CAAC;EACtC,IAAI,CAACoC,KAAK,CAACpC,UAAU,EAAE;IACrBoC,KAAK,CAACrC,KAAK,GAAG,IAAI;IAClBuE,OAAO,CAACC,QAAQ,CAAC6D,aAAa,EAAEhG,KAAK,EAAE9C,MAAM,CAAC;EAChD;AACF;AACA,SAAS8I,aAAaA,CAAChG,KAAK,EAAE9C,MAAM,EAAE;EACpCzB,KAAK,CAAC,eAAe,EAAEuE,KAAK,CAACpC,UAAU,EAAEoC,KAAK,CAACtF,MAAM,CAAC;;EAEtD;EACA,IAAI,CAACsF,KAAK,CAACpC,UAAU,IAAIoC,KAAK,CAACtF,MAAM,KAAK,CAAC,EAAE;IAC3CsF,KAAK,CAACpC,UAAU,GAAG,IAAI;IACvBV,MAAM,CAAC2B,QAAQ,GAAG,KAAK;IACvB3B,MAAM,CAACyD,IAAI,CAAC,KAAK,CAAC;IAClB,IAAIX,KAAK,CAAC3B,WAAW,EAAE;MACrB;MACA;MACA,IAAI4H,MAAM,GAAG/I,MAAM,CAAC4G,cAAc;MAClC,IAAI,CAACmC,MAAM,IAAIA,MAAM,CAAC5H,WAAW,IAAI4H,MAAM,CAACC,QAAQ,EAAE;QACpDhJ,MAAM,CAAC8B,OAAO,CAAC,CAAC;MAClB;IACF;EACF;AACF;AACA,IAAI,OAAOwG,MAAM,KAAK,UAAU,EAAE;EAChCxL,QAAQ,CAACoB,IAAI,GAAG,UAAU+K,QAAQ,EAAEC,IAAI,EAAE;IACxC,IAAIhL,IAAI,KAAKoE,SAAS,EAAE;MACtBpE,IAAI,GAAGhB,OAAO,CAAC,yBAAyB,CAAC;IAC3C;IACA,OAAOgB,IAAI,CAACpB,QAAQ,EAAEmM,QAAQ,EAAEC,IAAI,CAAC;EACvC,CAAC;AACH;AACA,SAASpC,OAAOA,CAACqC,EAAE,EAAEC,CAAC,EAAE;EACtB,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEkC,CAAC,GAAGF,EAAE,CAAC3L,MAAM,EAAE2J,CAAC,GAAGkC,CAAC,EAAElC,CAAC,EAAE,EAAE;IACzC,IAAIgC,EAAE,CAAChC,CAAC,CAAC,KAAKiC,CAAC,EAAE,OAAOjC,CAAC;EAC3B;EACA,OAAO,CAAC,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
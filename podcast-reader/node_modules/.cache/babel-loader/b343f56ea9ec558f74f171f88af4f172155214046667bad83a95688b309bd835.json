{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLDummy,\n    XMLNode,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  XMLNode = require('./XMLNode');\n  NodeType = require('./NodeType');\n  module.exports = XMLDummy = function (superClass) {\n    extend(XMLDummy, superClass);\n    function XMLDummy(parent) {\n      XMLDummy.__super__.constructor.call(this, parent);\n      this.type = NodeType.Dummy;\n    }\n    XMLDummy.prototype.clone = function () {\n      return Object.create(this);\n    };\n    XMLDummy.prototype.toString = function (options) {\n      return '';\n    };\n    return XMLDummy;\n  }(XMLNode);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLDummy", "XMLNode", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "type", "Dummy", "clone", "Object", "create", "toString", "options"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xmlbuilder/lib/XMLDummy.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDummy, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDummy = (function(superClass) {\n    extend(XMLDummy, superClass);\n\n    function XMLDummy(parent) {\n      XMLDummy.__super__.constructor.call(this, parent);\n      this.type = NodeType.Dummy;\n    }\n\n    XMLDummy.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLDummy.prototype.toString = function(options) {\n      return '';\n    };\n\n    return XMLDummy;\n\n  })(XMLNode);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,QAAQ;IAAEC,OAAO;IAC7BC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BX,OAAO,GAAGY,OAAO,CAAC,WAAW,CAAC;EAE9Bd,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCC,MAAM,CAACC,OAAO,GAAGf,QAAQ,GAAI,UAASgB,UAAU,EAAE;IAChDd,MAAM,CAACF,QAAQ,EAAEgB,UAAU,CAAC;IAE5B,SAAShB,QAAQA,CAACI,MAAM,EAAE;MACxBJ,QAAQ,CAACW,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACjD,IAAI,CAACa,IAAI,GAAGlB,QAAQ,CAACmB,KAAK;IAC5B;IAEAlB,QAAQ,CAACU,SAAS,CAACS,KAAK,GAAG,YAAW;MACpC,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAEDrB,QAAQ,CAACU,SAAS,CAACY,QAAQ,GAAG,UAASC,OAAO,EAAE;MAC9C,OAAO,EAAE;IACX,CAAC;IAED,OAAOvB,QAAQ;EAEjB,CAAC,CAAEC,OAAO,CAAC;AAEb,CAAC,EAAEM,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
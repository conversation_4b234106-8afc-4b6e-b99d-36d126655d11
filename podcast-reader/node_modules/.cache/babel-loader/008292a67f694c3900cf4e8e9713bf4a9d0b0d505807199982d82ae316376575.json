{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototypal inheritance, this class\n// prototypally inherits from Readable, and then parasitically from\n// Writable.\n\n'use strict';\n\n/*<replacement>*/\nvar objectKeys = Object.keys || function (obj) {\n  var keys = [];\n  for (var key in obj) keys.push(key);\n  return keys;\n};\n/*</replacement>*/\n\nmodule.exports = Duplex;\nvar Readable = require('./_stream_readable');\nvar Writable = require('./_stream_writable');\nrequire('inherits')(Duplex, Readable);\n{\n  // Allow the keys array to be GC'ed.\n  var keys = objectKeys(Writable.prototype);\n  for (var v = 0; v < keys.length; v++) {\n    var method = keys[v];\n    if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];\n  }\n}\nfunction Duplex(options) {\n  if (!(this instanceof Duplex)) return new Duplex(options);\n  Readable.call(this, options);\n  Writable.call(this, options);\n  this.allowHalfOpen = true;\n  if (options) {\n    if (options.readable === false) this.readable = false;\n    if (options.writable === false) this.writable = false;\n    if (options.allowHalfOpen === false) {\n      this.allowHalfOpen = false;\n      this.once('end', onend);\n    }\n  }\n}\nObject.defineProperty(Duplex.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n});\n\n// the no-half-open enforcer\nfunction onend() {\n  // If the writable side ended, then we're ok.\n  if (this._writableState.ended) return;\n\n  // no more data can be written.\n  // But allow more writes to happen in this tick.\n  process.nextTick(onEndNT, this);\n}\nfunction onEndNT(self) {\n  self.end();\n}\nObject.defineProperty(Duplex.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed && this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n    this._writableState.destroyed = value;\n  }\n});", "map": {"version": 3, "names": ["objectKeys", "Object", "keys", "obj", "key", "push", "module", "exports", "Duplex", "Readable", "require", "Writable", "prototype", "v", "length", "method", "options", "call", "allowHalfOpen", "readable", "writable", "once", "onend", "defineProperty", "enumerable", "get", "_writableState", "highWaterMark", "<PERSON><PERSON><PERSON><PERSON>", "ended", "process", "nextTick", "onEndNT", "self", "end", "_readableState", "undefined", "destroyed", "set", "value"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/readable-stream/lib/_stream_duplex.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototypal inheritance, this class\n// prototypally inherits from Readable, and then parasitically from\n// Writable.\n\n'use strict';\n\n/*<replacement>*/\nvar objectKeys = Object.keys || function (obj) {\n  var keys = [];\n  for (var key in obj) keys.push(key);\n  return keys;\n};\n/*</replacement>*/\n\nmodule.exports = Duplex;\nvar Readable = require('./_stream_readable');\nvar Writable = require('./_stream_writable');\nrequire('inherits')(Duplex, Readable);\n{\n  // Allow the keys array to be GC'ed.\n  var keys = objectKeys(Writable.prototype);\n  for (var v = 0; v < keys.length; v++) {\n    var method = keys[v];\n    if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];\n  }\n}\nfunction Duplex(options) {\n  if (!(this instanceof Duplex)) return new Duplex(options);\n  Readable.call(this, options);\n  Writable.call(this, options);\n  this.allowHalfOpen = true;\n  if (options) {\n    if (options.readable === false) this.readable = false;\n    if (options.writable === false) this.writable = false;\n    if (options.allowHalfOpen === false) {\n      this.allowHalfOpen = false;\n      this.once('end', onend);\n    }\n  }\n}\nObject.defineProperty(Duplex.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n});\n\n// the no-half-open enforcer\nfunction onend() {\n  // If the writable side ended, then we're ok.\n  if (this._writableState.ended) return;\n\n  // no more data can be written.\n  // But allow more writes to happen in this tick.\n  process.nextTick(onEndNT, this);\n}\nfunction onEndNT(self) {\n  self.end();\n}\nObject.defineProperty(Duplex.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed && this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n    this._writableState.destroyed = value;\n  }\n});"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA,IAAIA,UAAU,GAAGC,MAAM,CAACC,IAAI,IAAI,UAAUC,GAAG,EAAE;EAC7C,IAAID,IAAI,GAAG,EAAE;EACb,KAAK,IAAIE,GAAG,IAAID,GAAG,EAAED,IAAI,CAACG,IAAI,CAACD,GAAG,CAAC;EACnC,OAAOF,IAAI;AACb,CAAC;AACD;;AAEAI,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvB,IAAIC,QAAQ,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC5C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAC5CA,OAAO,CAAC,UAAU,CAAC,CAACF,MAAM,EAAEC,QAAQ,CAAC;AACrC;EACE;EACA,IAAIP,IAAI,GAAGF,UAAU,CAACW,QAAQ,CAACC,SAAS,CAAC;EACzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACY,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIE,MAAM,GAAGb,IAAI,CAACW,CAAC,CAAC;IACpB,IAAI,CAACL,MAAM,CAACI,SAAS,CAACG,MAAM,CAAC,EAAEP,MAAM,CAACI,SAAS,CAACG,MAAM,CAAC,GAAGJ,QAAQ,CAACC,SAAS,CAACG,MAAM,CAAC;EACtF;AACF;AACA,SAASP,MAAMA,CAACQ,OAAO,EAAE;EACvB,IAAI,EAAE,IAAI,YAAYR,MAAM,CAAC,EAAE,OAAO,IAAIA,MAAM,CAACQ,OAAO,CAAC;EACzDP,QAAQ,CAACQ,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;EAC5BL,QAAQ,CAACM,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;EAC5B,IAAI,CAACE,aAAa,GAAG,IAAI;EACzB,IAAIF,OAAO,EAAE;IACX,IAAIA,OAAO,CAACG,QAAQ,KAAK,KAAK,EAAE,IAAI,CAACA,QAAQ,GAAG,KAAK;IACrD,IAAIH,OAAO,CAACI,QAAQ,KAAK,KAAK,EAAE,IAAI,CAACA,QAAQ,GAAG,KAAK;IACrD,IAAIJ,OAAO,CAACE,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAACA,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACG,IAAI,CAAC,KAAK,EAAEC,KAAK,CAAC;IACzB;EACF;AACF;AACArB,MAAM,CAACsB,cAAc,CAACf,MAAM,CAACI,SAAS,EAAE,uBAAuB,EAAE;EAC/D;EACA;EACA;EACAY,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,cAAc,CAACC,aAAa;EAC1C;AACF,CAAC,CAAC;AACF1B,MAAM,CAACsB,cAAc,CAACf,MAAM,CAACI,SAAS,EAAE,gBAAgB,EAAE;EACxD;EACA;EACA;EACAY,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACE,SAAS,CAAC,CAAC;EAC/D;AACF,CAAC,CAAC;AACF3B,MAAM,CAACsB,cAAc,CAACf,MAAM,CAACI,SAAS,EAAE,gBAAgB,EAAE;EACxD;EACA;EACA;EACAY,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,cAAc,CAACZ,MAAM;EACnC;AACF,CAAC,CAAC;;AAEF;AACA,SAASQ,KAAKA,CAAA,EAAG;EACf;EACA,IAAI,IAAI,CAACI,cAAc,CAACG,KAAK,EAAE;;EAE/B;EACA;EACAC,OAAO,CAACC,QAAQ,CAACC,OAAO,EAAE,IAAI,CAAC;AACjC;AACA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrBA,IAAI,CAACC,GAAG,CAAC,CAAC;AACZ;AACAjC,MAAM,CAACsB,cAAc,CAACf,MAAM,CAACI,SAAS,EAAE,WAAW,EAAE;EACnD;EACA;EACA;EACAY,UAAU,EAAE,KAAK;EACjBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACU,cAAc,KAAKC,SAAS,IAAI,IAAI,CAACV,cAAc,KAAKU,SAAS,EAAE;MAC1E,OAAO,KAAK;IACd;IACA,OAAO,IAAI,CAACD,cAAc,CAACE,SAAS,IAAI,IAAI,CAACX,cAAc,CAACW,SAAS;EACvE,CAAC;EACDC,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;IACvB;IACA;IACA,IAAI,IAAI,CAACJ,cAAc,KAAKC,SAAS,IAAI,IAAI,CAACV,cAAc,KAAKU,SAAS,EAAE;MAC1E;IACF;;IAEA;IACA;IACA,IAAI,CAACD,cAAc,CAACE,SAAS,GAAGE,KAAK;IACrC,IAAI,CAACb,cAAc,CAACW,SAAS,GAAGE,KAAK;EACvC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
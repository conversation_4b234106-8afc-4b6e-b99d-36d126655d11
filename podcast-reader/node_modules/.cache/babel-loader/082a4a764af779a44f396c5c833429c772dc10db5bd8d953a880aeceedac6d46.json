{"ast": null, "code": "// Ported from https://github.com/mafin<PERSON>h/pump with\n// permission from the author, <PERSON> (@mafintosh).\n\n'use strict';\n\nvar eos;\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n    callback.apply(void 0, arguments);\n  };\n}\nvar _require$codes = require('../../../errors').codes,\n  ERR_MISSING_ARGS = _require$codes.ERR_MISSING_ARGS,\n  ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED;\nfunction noop(err) {\n  // Rethrow the error if it exists to avoid swallowing it\n  if (err) throw err;\n}\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\nfunction destroyer(stream, reading, writing, callback) {\n  callback = once(callback);\n  var closed = false;\n  stream.on('close', function () {\n    closed = true;\n  });\n  if (eos === undefined) eos = require('./end-of-stream');\n  eos(stream, {\n    readable: reading,\n    writable: writing\n  }, function (err) {\n    if (err) return callback(err);\n    closed = true;\n    callback();\n  });\n  var destroyed = false;\n  return function (err) {\n    if (closed) return;\n    if (destroyed) return;\n    destroyed = true;\n\n    // request.destroy just do .end - .abort is what we want\n    if (isRequest(stream)) return stream.abort();\n    if (typeof stream.destroy === 'function') return stream.destroy();\n    callback(err || new ERR_STREAM_DESTROYED('pipe'));\n  };\n}\nfunction call(fn) {\n  fn();\n}\nfunction pipe(from, to) {\n  return from.pipe(to);\n}\nfunction popCallback(streams) {\n  if (!streams.length) return noop;\n  if (typeof streams[streams.length - 1] !== 'function') return noop;\n  return streams.pop();\n}\nfunction pipeline() {\n  for (var _len = arguments.length, streams = new Array(_len), _key = 0; _key < _len; _key++) {\n    streams[_key] = arguments[_key];\n  }\n  var callback = popCallback(streams);\n  if (Array.isArray(streams[0])) streams = streams[0];\n  if (streams.length < 2) {\n    throw new ERR_MISSING_ARGS('streams');\n  }\n  var error;\n  var destroys = streams.map(function (stream, i) {\n    var reading = i < streams.length - 1;\n    var writing = i > 0;\n    return destroyer(stream, reading, writing, function (err) {\n      if (!error) error = err;\n      if (err) destroys.forEach(call);\n      if (reading) return;\n      destroys.forEach(call);\n      callback(error);\n    });\n  });\n  return streams.reduce(pipe);\n}\nmodule.exports = pipeline;", "map": {"version": 3, "names": ["eos", "once", "callback", "called", "apply", "arguments", "_require$codes", "require", "codes", "ERR_MISSING_ARGS", "ERR_STREAM_DESTROYED", "noop", "err", "isRequest", "stream", "<PERSON><PERSON><PERSON><PERSON>", "abort", "destroyer", "reading", "writing", "closed", "on", "undefined", "readable", "writable", "destroyed", "destroy", "call", "fn", "pipe", "from", "to", "popCallback", "streams", "length", "pop", "pipeline", "_len", "Array", "_key", "isArray", "error", "destroys", "map", "i", "for<PERSON>ach", "reduce", "module", "exports"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/readable-stream/lib/internal/streams/pipeline.js"], "sourcesContent": ["// Ported from https://github.com/mafin<PERSON>h/pump with\n// permission from the author, <PERSON> (@mafintosh).\n\n'use strict';\n\nvar eos;\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n    callback.apply(void 0, arguments);\n  };\n}\nvar _require$codes = require('../../../errors').codes,\n  ERR_MISSING_ARGS = _require$codes.ERR_MISSING_ARGS,\n  ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED;\nfunction noop(err) {\n  // Rethrow the error if it exists to avoid swallowing it\n  if (err) throw err;\n}\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\nfunction destroyer(stream, reading, writing, callback) {\n  callback = once(callback);\n  var closed = false;\n  stream.on('close', function () {\n    closed = true;\n  });\n  if (eos === undefined) eos = require('./end-of-stream');\n  eos(stream, {\n    readable: reading,\n    writable: writing\n  }, function (err) {\n    if (err) return callback(err);\n    closed = true;\n    callback();\n  });\n  var destroyed = false;\n  return function (err) {\n    if (closed) return;\n    if (destroyed) return;\n    destroyed = true;\n\n    // request.destroy just do .end - .abort is what we want\n    if (isRequest(stream)) return stream.abort();\n    if (typeof stream.destroy === 'function') return stream.destroy();\n    callback(err || new ERR_STREAM_DESTROYED('pipe'));\n  };\n}\nfunction call(fn) {\n  fn();\n}\nfunction pipe(from, to) {\n  return from.pipe(to);\n}\nfunction popCallback(streams) {\n  if (!streams.length) return noop;\n  if (typeof streams[streams.length - 1] !== 'function') return noop;\n  return streams.pop();\n}\nfunction pipeline() {\n  for (var _len = arguments.length, streams = new Array(_len), _key = 0; _key < _len; _key++) {\n    streams[_key] = arguments[_key];\n  }\n  var callback = popCallback(streams);\n  if (Array.isArray(streams[0])) streams = streams[0];\n  if (streams.length < 2) {\n    throw new ERR_MISSING_ARGS('streams');\n  }\n  var error;\n  var destroys = streams.map(function (stream, i) {\n    var reading = i < streams.length - 1;\n    var writing = i > 0;\n    return destroyer(stream, reading, writing, function (err) {\n      if (!error) error = err;\n      if (err) destroys.forEach(call);\n      if (reading) return;\n      destroys.forEach(call);\n      callback(error);\n    });\n  });\n  return streams.reduce(pipe);\n}\nmodule.exports = pipeline;"], "mappings": "AAAA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,GAAG;AACP,SAASC,IAAIA,CAACC,QAAQ,EAAE;EACtB,IAAIC,MAAM,GAAG,KAAK;EAClB,OAAO,YAAY;IACjB,IAAIA,MAAM,EAAE;IACZA,MAAM,GAAG,IAAI;IACbD,QAAQ,CAACE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;EACnC,CAAC;AACH;AACA,IAAIC,cAAc,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACC,KAAK;EACnDC,gBAAgB,GAAGH,cAAc,CAACG,gBAAgB;EAClDC,oBAAoB,GAAGJ,cAAc,CAACI,oBAAoB;AAC5D,SAASC,IAAIA,CAACC,GAAG,EAAE;EACjB;EACA,IAAIA,GAAG,EAAE,MAAMA,GAAG;AACpB;AACA,SAASC,SAASA,CAACC,MAAM,EAAE;EACzB,OAAOA,MAAM,CAACC,SAAS,IAAI,OAAOD,MAAM,CAACE,KAAK,KAAK,UAAU;AAC/D;AACA,SAASC,SAASA,CAACH,MAAM,EAAEI,OAAO,EAAEC,OAAO,EAAEjB,QAAQ,EAAE;EACrDA,QAAQ,GAAGD,IAAI,CAACC,QAAQ,CAAC;EACzB,IAAIkB,MAAM,GAAG,KAAK;EAClBN,MAAM,CAACO,EAAE,CAAC,OAAO,EAAE,YAAY;IAC7BD,MAAM,GAAG,IAAI;EACf,CAAC,CAAC;EACF,IAAIpB,GAAG,KAAKsB,SAAS,EAAEtB,GAAG,GAAGO,OAAO,CAAC,iBAAiB,CAAC;EACvDP,GAAG,CAACc,MAAM,EAAE;IACVS,QAAQ,EAAEL,OAAO;IACjBM,QAAQ,EAAEL;EACZ,CAAC,EAAE,UAAUP,GAAG,EAAE;IAChB,IAAIA,GAAG,EAAE,OAAOV,QAAQ,CAACU,GAAG,CAAC;IAC7BQ,MAAM,GAAG,IAAI;IACblB,QAAQ,CAAC,CAAC;EACZ,CAAC,CAAC;EACF,IAAIuB,SAAS,GAAG,KAAK;EACrB,OAAO,UAAUb,GAAG,EAAE;IACpB,IAAIQ,MAAM,EAAE;IACZ,IAAIK,SAAS,EAAE;IACfA,SAAS,GAAG,IAAI;;IAEhB;IACA,IAAIZ,SAAS,CAACC,MAAM,CAAC,EAAE,OAAOA,MAAM,CAACE,KAAK,CAAC,CAAC;IAC5C,IAAI,OAAOF,MAAM,CAACY,OAAO,KAAK,UAAU,EAAE,OAAOZ,MAAM,CAACY,OAAO,CAAC,CAAC;IACjExB,QAAQ,CAACU,GAAG,IAAI,IAAIF,oBAAoB,CAAC,MAAM,CAAC,CAAC;EACnD,CAAC;AACH;AACA,SAASiB,IAAIA,CAACC,EAAE,EAAE;EAChBA,EAAE,CAAC,CAAC;AACN;AACA,SAASC,IAAIA,CAACC,IAAI,EAAEC,EAAE,EAAE;EACtB,OAAOD,IAAI,CAACD,IAAI,CAACE,EAAE,CAAC;AACtB;AACA,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC5B,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE,OAAOvB,IAAI;EAChC,IAAI,OAAOsB,OAAO,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE,OAAOvB,IAAI;EAClE,OAAOsB,OAAO,CAACE,GAAG,CAAC,CAAC;AACtB;AACA,SAASC,QAAQA,CAAA,EAAG;EAClB,KAAK,IAAIC,IAAI,GAAGhC,SAAS,CAAC6B,MAAM,EAAED,OAAO,GAAG,IAAIK,KAAK,CAACD,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;IAC1FN,OAAO,CAACM,IAAI,CAAC,GAAGlC,SAAS,CAACkC,IAAI,CAAC;EACjC;EACA,IAAIrC,QAAQ,GAAG8B,WAAW,CAACC,OAAO,CAAC;EACnC,IAAIK,KAAK,CAACE,OAAO,CAACP,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEA,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;EACnD,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;IACtB,MAAM,IAAIzB,gBAAgB,CAAC,SAAS,CAAC;EACvC;EACA,IAAIgC,KAAK;EACT,IAAIC,QAAQ,GAAGT,OAAO,CAACU,GAAG,CAAC,UAAU7B,MAAM,EAAE8B,CAAC,EAAE;IAC9C,IAAI1B,OAAO,GAAG0B,CAAC,GAAGX,OAAO,CAACC,MAAM,GAAG,CAAC;IACpC,IAAIf,OAAO,GAAGyB,CAAC,GAAG,CAAC;IACnB,OAAO3B,SAAS,CAACH,MAAM,EAAEI,OAAO,EAAEC,OAAO,EAAE,UAAUP,GAAG,EAAE;MACxD,IAAI,CAAC6B,KAAK,EAAEA,KAAK,GAAG7B,GAAG;MACvB,IAAIA,GAAG,EAAE8B,QAAQ,CAACG,OAAO,CAAClB,IAAI,CAAC;MAC/B,IAAIT,OAAO,EAAE;MACbwB,QAAQ,CAACG,OAAO,CAAClB,IAAI,CAAC;MACtBzB,QAAQ,CAACuC,KAAK,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOR,OAAO,CAACa,MAAM,CAACjB,IAAI,CAAC;AAC7B;AACAkB,MAAM,CAACC,OAAO,GAAGZ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
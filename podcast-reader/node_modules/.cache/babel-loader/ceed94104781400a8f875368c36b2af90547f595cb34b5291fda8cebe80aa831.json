{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLDTDEntity,\n    XMLNode,\n    isObject,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  isObject = require('./Utility').isObject;\n  XMLNode = require('./XMLNode');\n  NodeType = require('./NodeType');\n  module.exports = XMLDTDEntity = function (superClass) {\n    extend(XMLDTDEntity, superClass);\n    function XMLDTDEntity(parent, pe, name, value) {\n      XMLDTDEntity.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n      }\n      this.pe = !!pe;\n      this.name = this.stringify.name(name);\n      this.type = NodeType.EntityDeclaration;\n      if (!isObject(value)) {\n        this.value = this.stringify.dtdEntityValue(value);\n        this.internal = true;\n      } else {\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID && !value.sysID) {\n          throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n        }\n        this.internal = false;\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n        if (value.nData != null) {\n          this.nData = this.stringify.dtdNData(value.nData);\n        }\n        if (this.pe && this.nData) {\n          throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n        }\n      }\n    }\n    Object.defineProperty(XMLDTDEntity.prototype, 'publicId', {\n      get: function () {\n        return this.pubID;\n      }\n    });\n    Object.defineProperty(XMLDTDEntity.prototype, 'systemId', {\n      get: function () {\n        return this.sysID;\n      }\n    });\n    Object.defineProperty(XMLDTDEntity.prototype, 'notationName', {\n      get: function () {\n        return this.nData || null;\n      }\n    });\n    Object.defineProperty(XMLDTDEntity.prototype, 'inputEncoding', {\n      get: function () {\n        return null;\n      }\n    });\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlEncoding', {\n      get: function () {\n        return null;\n      }\n    });\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlVersion', {\n      get: function () {\n        return null;\n      }\n    });\n    XMLDTDEntity.prototype.toString = function (options) {\n      return this.options.writer.dtdEntity(this, this.options.writer.filterOptions(options));\n    };\n    return XMLDTDEntity;\n  }(XMLNode);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLDTDEntity", "XMLNode", "isObject", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "pe", "name", "value", "Error", "debugInfo", "stringify", "type", "EntityDeclaration", "dtdEntityValue", "internal", "pubID", "sysID", "dtdPubID", "dtdSysID", "nData", "dtdNData", "Object", "defineProperty", "get", "toString", "options", "writer", "dtdEntity", "filterOptions"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xmlbuilder/lib/XMLDTDEntity.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDEntity, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = require('./Utility').isObject;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDTDEntity = (function(superClass) {\n    extend(XMLDTDEntity, superClass);\n\n    function XMLDTDEntity(parent, pe, name, value) {\n      XMLDTDEntity.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n      }\n      this.pe = !!pe;\n      this.name = this.stringify.name(name);\n      this.type = NodeType.EntityDeclaration;\n      if (!isObject(value)) {\n        this.value = this.stringify.dtdEntityValue(value);\n        this.internal = true;\n      } else {\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID && !value.sysID) {\n          throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n        }\n        this.internal = false;\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n        if (value.nData != null) {\n          this.nData = this.stringify.dtdNData(value.nData);\n        }\n        if (this.pe && this.nData) {\n          throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n        }\n      }\n    }\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'notationName', {\n      get: function() {\n        return this.nData || null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlVersion', {\n      get: function() {\n        return null;\n      }\n    });\n\n    XMLDTDEntity.prototype.toString = function(options) {\n      return this.options.writer.dtdEntity(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDEntity;\n\n  })(XMLNode);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,YAAY;IAAEC,OAAO;IAAEC,QAAQ;IAC3CC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BX,QAAQ,GAAGY,OAAO,CAAC,WAAW,CAAC,CAACZ,QAAQ;EAExCD,OAAO,GAAGa,OAAO,CAAC,WAAW,CAAC;EAE9Bf,QAAQ,GAAGe,OAAO,CAAC,YAAY,CAAC;EAEhCC,MAAM,CAACC,OAAO,GAAGhB,YAAY,GAAI,UAASiB,UAAU,EAAE;IACpDd,MAAM,CAACH,YAAY,EAAEiB,UAAU,CAAC;IAEhC,SAASjB,YAAYA,CAACK,MAAM,EAAEa,EAAE,EAAEC,IAAI,EAAEC,KAAK,EAAE;MAC7CpB,YAAY,CAACY,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACrD,IAAIc,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAIE,KAAK,CAAC,2BAA2B,GAAG,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;MACrE;MACA,IAAIC,KAAK,IAAI,IAAI,EAAE;QACjB,MAAM,IAAIC,KAAK,CAAC,4BAA4B,GAAG,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;MACtE;MACA,IAAI,CAACD,EAAE,GAAG,CAAC,CAACA,EAAE;MACd,IAAI,CAACC,IAAI,GAAG,IAAI,CAACI,SAAS,CAACJ,IAAI,CAACA,IAAI,CAAC;MACrC,IAAI,CAACK,IAAI,GAAGzB,QAAQ,CAAC0B,iBAAiB;MACtC,IAAI,CAACvB,QAAQ,CAACkB,KAAK,CAAC,EAAE;QACpB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACG,SAAS,CAACG,cAAc,CAACN,KAAK,CAAC;QACjD,IAAI,CAACO,QAAQ,GAAG,IAAI;MACtB,CAAC,MAAM;QACL,IAAI,CAACP,KAAK,CAACQ,KAAK,IAAI,CAACR,KAAK,CAACS,KAAK,EAAE;UAChC,MAAM,IAAIR,KAAK,CAAC,wEAAwE,GAAG,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;QAClH;QACA,IAAIC,KAAK,CAACQ,KAAK,IAAI,CAACR,KAAK,CAACS,KAAK,EAAE;UAC/B,MAAM,IAAIR,KAAK,CAAC,8DAA8D,GAAG,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;QACxG;QACA,IAAI,CAACQ,QAAQ,GAAG,KAAK;QACrB,IAAIP,KAAK,CAACQ,KAAK,IAAI,IAAI,EAAE;UACvB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACL,SAAS,CAACO,QAAQ,CAACV,KAAK,CAACQ,KAAK,CAAC;QACnD;QACA,IAAIR,KAAK,CAACS,KAAK,IAAI,IAAI,EAAE;UACvB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACN,SAAS,CAACQ,QAAQ,CAACX,KAAK,CAACS,KAAK,CAAC;QACnD;QACA,IAAIT,KAAK,CAACY,KAAK,IAAI,IAAI,EAAE;UACvB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACT,SAAS,CAACU,QAAQ,CAACb,KAAK,CAACY,KAAK,CAAC;QACnD;QACA,IAAI,IAAI,CAACd,EAAE,IAAI,IAAI,CAACc,KAAK,EAAE;UACzB,MAAM,IAAIX,KAAK,CAAC,6DAA6D,GAAG,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;QACvG;MACF;IACF;IAEAe,MAAM,CAACC,cAAc,CAACnC,YAAY,CAACW,SAAS,EAAE,UAAU,EAAE;MACxDyB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACR,KAAK;MACnB;IACF,CAAC,CAAC;IAEFM,MAAM,CAACC,cAAc,CAACnC,YAAY,CAACW,SAAS,EAAE,UAAU,EAAE;MACxDyB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACP,KAAK;MACnB;IACF,CAAC,CAAC;IAEFK,MAAM,CAACC,cAAc,CAACnC,YAAY,CAACW,SAAS,EAAE,cAAc,EAAE;MAC5DyB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI,CAACJ,KAAK,IAAI,IAAI;MAC3B;IACF,CAAC,CAAC;IAEFE,MAAM,CAACC,cAAc,CAACnC,YAAY,CAACW,SAAS,EAAE,eAAe,EAAE;MAC7DyB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI;MACb;IACF,CAAC,CAAC;IAEFF,MAAM,CAACC,cAAc,CAACnC,YAAY,CAACW,SAAS,EAAE,aAAa,EAAE;MAC3DyB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI;MACb;IACF,CAAC,CAAC;IAEFF,MAAM,CAACC,cAAc,CAACnC,YAAY,CAACW,SAAS,EAAE,YAAY,EAAE;MAC1DyB,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO,IAAI;MACb;IACF,CAAC,CAAC;IAEFpC,YAAY,CAACW,SAAS,CAAC0B,QAAQ,GAAG,UAASC,OAAO,EAAE;MAClD,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACC,SAAS,CAAC,IAAI,EAAE,IAAI,CAACF,OAAO,CAACC,MAAM,CAACE,aAAa,CAACH,OAAO,CAAC,CAAC;IACxF,CAAC;IAED,OAAOtC,YAAY;EAErB,CAAC,CAAEC,OAAO,CAAC;AAEb,CAAC,EAAEO,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
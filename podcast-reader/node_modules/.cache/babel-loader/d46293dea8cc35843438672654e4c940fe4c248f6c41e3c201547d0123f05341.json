{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/AudioPlayer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './AudioPlayer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AudioPlayer = ({\n  episode,\n  onClose\n}) => {\n  _s();\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [volume, setVolume] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const audioRef = useRef(null);\n  useEffect(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    const updateTime = () => setCurrentTime(audio.currentTime);\n    const updateDuration = () => setDuration(audio.duration);\n    const handleLoadStart = () => setIsLoading(true);\n    const handleCanPlay = () => setIsLoading(false);\n    const handleEnded = () => setIsPlaying(false);\n    audio.addEventListener('timeupdate', updateTime);\n    audio.addEventListener('loadedmetadata', updateDuration);\n    audio.addEventListener('loadstart', handleLoadStart);\n    audio.addEventListener('canplay', handleCanPlay);\n    audio.addEventListener('ended', handleEnded);\n    return () => {\n      audio.removeEventListener('timeupdate', updateTime);\n      audio.removeEventListener('loadedmetadata', updateDuration);\n      audio.removeEventListener('loadstart', handleLoadStart);\n      audio.removeEventListener('canplay', handleCanPlay);\n      audio.removeEventListener('ended', handleEnded);\n    };\n  }, [episode]);\n\n  // 键盘快捷键支持\n  useEffect(() => {\n    const handleKeyPress = e => {\n      // 只在没有输入框聚焦时响应空格键\n      if (e.code === 'Space' && !['INPUT', 'TEXTAREA'].includes(e.target.tagName)) {\n        e.preventDefault();\n        togglePlay();\n      }\n    };\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, [isPlaying]);\n  const togglePlay = () => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    if (isPlaying) {\n      audio.pause();\n    } else {\n      audio.play();\n    }\n    setIsPlaying(!isPlaying);\n  };\n  const handleSeek = e => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    const rect = e.currentTarget.getBoundingClientRect();\n    const percent = (e.clientX - rect.left) / rect.width;\n    const newTime = percent * duration;\n    audio.currentTime = newTime;\n    setCurrentTime(newTime);\n  };\n  const handleVolumeChange = e => {\n    const newVolume = parseFloat(e.target.value);\n    setVolume(newVolume);\n    if (audioRef.current) {\n      audioRef.current.volume = newVolume;\n    }\n  };\n  const formatTime = time => {\n    if (isNaN(time)) return '0:00';\n    const minutes = Math.floor(time / 60);\n    const seconds = Math.floor(time % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n  const progressPercent = duration > 0 ? currentTime / duration * 100 : 0;\n  if (!episode) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"audio-player\",\n    children: [/*#__PURE__*/_jsxDEV(\"audio\", {\n      ref: audioRef,\n      src: episode.audioUrl,\n      preload: \"metadata\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"player-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"player-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"player-title\",\n          children: episode.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"player-time\",\n          children: [formatTime(currentTime), \" / \", formatTime(duration)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"player-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"control-button play-pause\",\n          onClick: togglePlay,\n          disabled: isLoading || !episode.audioUrl,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this) : isPlaying ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M6 4h4v16H6V4zm8 0h4v16h-4V4z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8 5v14l11-7z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-container\",\n          onClick: handleSeek,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${progressPercent}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"volume-control\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"1\",\n            step: \"0.1\",\n            value: volume,\n            onChange: handleVolumeChange,\n            className: \"volume-slider\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"control-button close-button\",\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(AudioPlayer, \"7sngudbrCCwR/AXRIb3iFej5ftM=\");\n_c = AudioPlayer;\nexport default AudioPlayer;\nvar _c;\n$RefreshReg$(_c, \"AudioPlayer\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "AudioPlayer", "episode", "onClose", "_s", "isPlaying", "setIsPlaying", "currentTime", "setCurrentTime", "duration", "setDuration", "volume", "setVolume", "isLoading", "setIsLoading", "audioRef", "audio", "current", "updateTime", "updateDuration", "handleLoadStart", "handleCanPlay", "handleEnded", "addEventListener", "removeEventListener", "handleKeyPress", "e", "code", "includes", "target", "tagName", "preventDefault", "togglePlay", "document", "pause", "play", "handleSeek", "rect", "currentTarget", "getBoundingClientRect", "percent", "clientX", "left", "width", "newTime", "handleVolumeChange", "newVolume", "parseFloat", "value", "formatTime", "time", "isNaN", "minutes", "Math", "floor", "seconds", "toString", "padStart", "progressPercent", "className", "children", "ref", "src", "audioUrl", "preload", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onClick", "disabled", "height", "viewBox", "fill", "d", "style", "type", "min", "max", "step", "onChange", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/AudioPlayer.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './AudioPlayer.css';\n\nconst AudioPlayer = ({ episode, onClose }) => {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [volume, setVolume] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const audioRef = useRef(null);\n\n  useEffect(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    const updateTime = () => setCurrentTime(audio.currentTime);\n    const updateDuration = () => setDuration(audio.duration);\n    const handleLoadStart = () => setIsLoading(true);\n    const handleCanPlay = () => setIsLoading(false);\n    const handleEnded = () => setIsPlaying(false);\n\n    audio.addEventListener('timeupdate', updateTime);\n    audio.addEventListener('loadedmetadata', updateDuration);\n    audio.addEventListener('loadstart', handleLoadStart);\n    audio.addEventListener('canplay', handleCanPlay);\n    audio.addEventListener('ended', handleEnded);\n\n    return () => {\n      audio.removeEventListener('timeupdate', updateTime);\n      audio.removeEventListener('loadedmetadata', updateDuration);\n      audio.removeEventListener('loadstart', handleLoadStart);\n      audio.removeEventListener('canplay', handleCanPlay);\n      audio.removeEventListener('ended', handleEnded);\n    };\n  }, [episode]);\n\n  // 键盘快捷键支持\n  useEffect(() => {\n    const handleKeyPress = (e) => {\n      // 只在没有输入框聚焦时响应空格键\n      if (e.code === 'Space' && !['INPUT', 'TEXTAREA'].includes(e.target.tagName)) {\n        e.preventDefault();\n        togglePlay();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, [isPlaying]);\n\n  const togglePlay = () => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    if (isPlaying) {\n      audio.pause();\n    } else {\n      audio.play();\n    }\n    setIsPlaying(!isPlaying);\n  };\n\n  const handleSeek = (e) => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    const rect = e.currentTarget.getBoundingClientRect();\n    const percent = (e.clientX - rect.left) / rect.width;\n    const newTime = percent * duration;\n    audio.currentTime = newTime;\n    setCurrentTime(newTime);\n  };\n\n  const handleVolumeChange = (e) => {\n    const newVolume = parseFloat(e.target.value);\n    setVolume(newVolume);\n    if (audioRef.current) {\n      audioRef.current.volume = newVolume;\n    }\n  };\n\n  const formatTime = (time) => {\n    if (isNaN(time)) return '0:00';\n    const minutes = Math.floor(time / 60);\n    const seconds = Math.floor(time % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercent = duration > 0 ? (currentTime / duration) * 100 : 0;\n\n  if (!episode) return null;\n\n  return (\n    <div className=\"audio-player\">\n      <audio\n        ref={audioRef}\n        src={episode.audioUrl}\n        preload=\"metadata\"\n      />\n\n      <div className=\"player-content\">\n        <div className=\"player-info\">\n          <h4 className=\"player-title\">{episode.title}</h4>\n          <div className=\"player-time\">\n            {formatTime(currentTime)} / {formatTime(duration)}\n          </div>\n        </div>\n\n        <div className=\"player-controls\">\n          <button\n            className=\"control-button play-pause\"\n            onClick={togglePlay}\n            disabled={isLoading || !episode.audioUrl}\n          >\n            {isLoading ? (\n              <div className=\"loading-spinner\"></div>\n            ) : isPlaying ? (\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M6 4h4v16H6V4zm8 0h4v16h-4V4z\"/>\n              </svg>\n            ) : (\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            )}\n          </button>\n\n          <div className=\"progress-container\" onClick={handleSeek}>\n            <div className=\"progress-bar\">\n              <div\n                className=\"progress-fill\"\n                style={{ width: `${progressPercent}%` }}\n              ></div>\n            </div>\n          </div>\n\n          <div className=\"volume-control\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z\"/>\n            </svg>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={volume}\n              onChange={handleVolumeChange}\n              className=\"volume-slider\"\n            />\n          </div>\n\n          <button className=\"control-button close-button\" onClick={onClose}>\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AudioPlayer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMmB,QAAQ,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAE7BC,SAAS,CAAC,MAAM;IACd,MAAMkB,KAAK,GAAGD,QAAQ,CAACE,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,MAAME,UAAU,GAAGA,CAAA,KAAMV,cAAc,CAACQ,KAAK,CAACT,WAAW,CAAC;IAC1D,MAAMY,cAAc,GAAGA,CAAA,KAAMT,WAAW,CAACM,KAAK,CAACP,QAAQ,CAAC;IACxD,MAAMW,eAAe,GAAGA,CAAA,KAAMN,YAAY,CAAC,IAAI,CAAC;IAChD,MAAMO,aAAa,GAAGA,CAAA,KAAMP,YAAY,CAAC,KAAK,CAAC;IAC/C,MAAMQ,WAAW,GAAGA,CAAA,KAAMhB,YAAY,CAAC,KAAK,CAAC;IAE7CU,KAAK,CAACO,gBAAgB,CAAC,YAAY,EAAEL,UAAU,CAAC;IAChDF,KAAK,CAACO,gBAAgB,CAAC,gBAAgB,EAAEJ,cAAc,CAAC;IACxDH,KAAK,CAACO,gBAAgB,CAAC,WAAW,EAAEH,eAAe,CAAC;IACpDJ,KAAK,CAACO,gBAAgB,CAAC,SAAS,EAAEF,aAAa,CAAC;IAChDL,KAAK,CAACO,gBAAgB,CAAC,OAAO,EAAED,WAAW,CAAC;IAE5C,OAAO,MAAM;MACXN,KAAK,CAACQ,mBAAmB,CAAC,YAAY,EAAEN,UAAU,CAAC;MACnDF,KAAK,CAACQ,mBAAmB,CAAC,gBAAgB,EAAEL,cAAc,CAAC;MAC3DH,KAAK,CAACQ,mBAAmB,CAAC,WAAW,EAAEJ,eAAe,CAAC;MACvDJ,KAAK,CAACQ,mBAAmB,CAAC,SAAS,EAAEH,aAAa,CAAC;MACnDL,KAAK,CAACQ,mBAAmB,CAAC,OAAO,EAAEF,WAAW,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,CAACpB,OAAO,CAAC,CAAC;;EAEb;EACAJ,SAAS,CAAC,MAAM;IACd,MAAM2B,cAAc,GAAIC,CAAC,IAAK;MAC5B;MACA,IAAIA,CAAC,CAACC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACC,OAAO,CAAC,EAAE;QAC3EJ,CAAC,CAACK,cAAc,CAAC,CAAC;QAClBC,UAAU,CAAC,CAAC;MACd;IACF,CAAC;IAEDC,QAAQ,CAACV,gBAAgB,CAAC,SAAS,EAAEE,cAAc,CAAC;IACpD,OAAO,MAAMQ,QAAQ,CAACT,mBAAmB,CAAC,SAAS,EAAEC,cAAc,CAAC;EACtE,CAAC,EAAE,CAACpB,SAAS,CAAC,CAAC;EAEf,MAAM2B,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMhB,KAAK,GAAGD,QAAQ,CAACE,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,IAAIX,SAAS,EAAE;MACbW,KAAK,CAACkB,KAAK,CAAC,CAAC;IACf,CAAC,MAAM;MACLlB,KAAK,CAACmB,IAAI,CAAC,CAAC;IACd;IACA7B,YAAY,CAAC,CAACD,SAAS,CAAC;EAC1B,CAAC;EAED,MAAM+B,UAAU,GAAIV,CAAC,IAAK;IACxB,MAAMV,KAAK,GAAGD,QAAQ,CAACE,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,MAAMqB,IAAI,GAAGX,CAAC,CAACY,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACpD,MAAMC,OAAO,GAAG,CAACd,CAAC,CAACe,OAAO,GAAGJ,IAAI,CAACK,IAAI,IAAIL,IAAI,CAACM,KAAK;IACpD,MAAMC,OAAO,GAAGJ,OAAO,GAAG/B,QAAQ;IAClCO,KAAK,CAACT,WAAW,GAAGqC,OAAO;IAC3BpC,cAAc,CAACoC,OAAO,CAAC;EACzB,CAAC;EAED,MAAMC,kBAAkB,GAAInB,CAAC,IAAK;IAChC,MAAMoB,SAAS,GAAGC,UAAU,CAACrB,CAAC,CAACG,MAAM,CAACmB,KAAK,CAAC;IAC5CpC,SAAS,CAACkC,SAAS,CAAC;IACpB,IAAI/B,QAAQ,CAACE,OAAO,EAAE;MACpBF,QAAQ,CAACE,OAAO,CAACN,MAAM,GAAGmC,SAAS;IACrC;EACF,CAAC;EAED,MAAMG,UAAU,GAAIC,IAAI,IAAK;IAC3B,IAAIC,KAAK,CAACD,IAAI,CAAC,EAAE,OAAO,MAAM;IAC9B,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,EAAE,CAAC;IACrC,MAAMK,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,EAAE,CAAC;IACrC,OAAO,GAAGE,OAAO,IAAIG,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D,CAAC;EAED,MAAMC,eAAe,GAAGjD,QAAQ,GAAG,CAAC,GAAIF,WAAW,GAAGE,QAAQ,GAAI,GAAG,GAAG,CAAC;EAEzE,IAAI,CAACP,OAAO,EAAE,OAAO,IAAI;EAEzB,oBACEF,OAAA;IAAK2D,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B5D,OAAA;MACE6D,GAAG,EAAE9C,QAAS;MACd+C,GAAG,EAAE5D,OAAO,CAAC6D,QAAS;MACtBC,OAAO,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAEFpE,OAAA;MAAK2D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5D,OAAA;QAAK2D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5D,OAAA;UAAI2D,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAE1D,OAAO,CAACmE;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjDpE,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,GACzBX,UAAU,CAAC1C,WAAW,CAAC,EAAC,KAAG,EAAC0C,UAAU,CAACxC,QAAQ,CAAC;QAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpE,OAAA;QAAK2D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B5D,OAAA;UACE2D,SAAS,EAAC,2BAA2B;UACrCW,OAAO,EAAEtC,UAAW;UACpBuC,QAAQ,EAAE1D,SAAS,IAAI,CAACX,OAAO,CAAC6D,QAAS;UAAAH,QAAA,EAExC/C,SAAS,gBACRb,OAAA;YAAK2D,SAAS,EAAC;UAAiB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,GACrC/D,SAAS,gBACXL,OAAA;YAAK2C,KAAK,EAAC,IAAI;YAAC6B,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAd,QAAA,eACjE5D,OAAA;cAAM2E,CAAC,EAAC;YAA+B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,gBAENpE,OAAA;YAAK2C,KAAK,EAAC,IAAI;YAAC6B,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAd,QAAA,eACjE5D,OAAA;cAAM2E,CAAC,EAAC;YAAe;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAETpE,OAAA;UAAK2D,SAAS,EAAC,oBAAoB;UAACW,OAAO,EAAElC,UAAW;UAAAwB,QAAA,eACtD5D,OAAA;YAAK2D,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B5D,OAAA;cACE2D,SAAS,EAAC,eAAe;cACzBiB,KAAK,EAAE;gBAAEjC,KAAK,EAAE,GAAGe,eAAe;cAAI;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpE,OAAA;UAAK2D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5D,OAAA;YAAK2C,KAAK,EAAC,IAAI;YAAC6B,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAd,QAAA,eACjE5D,OAAA;cAAM2E,CAAC,EAAC;YAAyF;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG,CAAC,eACNpE,OAAA;YACE6E,IAAI,EAAC,OAAO;YACZC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC,KAAK;YACVhC,KAAK,EAAErC,MAAO;YACdsE,QAAQ,EAAEpC,kBAAmB;YAC7Bc,SAAS,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpE,OAAA;UAAQ2D,SAAS,EAAC,6BAA6B;UAACW,OAAO,EAAEnE,OAAQ;UAAAyD,QAAA,eAC/D5D,OAAA;YAAK2C,KAAK,EAAC,IAAI;YAAC6B,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAd,QAAA,eACjE5D,OAAA;cAAM2E,CAAC,EAAC;YAAuG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChE,EAAA,CA7JIH,WAAW;AAAAiF,EAAA,GAAXjF,WAAW;AA+JjB,eAAeA,WAAW;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
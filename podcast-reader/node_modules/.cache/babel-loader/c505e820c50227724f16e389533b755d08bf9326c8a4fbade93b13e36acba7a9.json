{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nmodule.exports = Stream;\nvar EE = require('events').EventEmitter;\nvar inherits = require('inherits');\ninherits(Stream, EE);\nStream.Readable = require('readable-stream/lib/_stream_readable.js');\nStream.Writable = require('readable-stream/lib/_stream_writable.js');\nStream.Duplex = require('readable-stream/lib/_stream_duplex.js');\nStream.Transform = require('readable-stream/lib/_stream_transform.js');\nStream.PassThrough = require('readable-stream/lib/_stream_passthrough.js');\nStream.finished = require('readable-stream/lib/internal/streams/end-of-stream.js');\nStream.pipeline = require('readable-stream/lib/internal/streams/pipeline.js');\n\n// Backwards-compat with node 0.4.x\nStream.Stream = Stream;\n\n// old-style streams.  Note that the pipe method (the only relevant\n// part of this class) is overridden in the Readable class.\n\nfunction Stream() {\n  EE.call(this);\n}\nStream.prototype.pipe = function (dest, options) {\n  var source = this;\n  function ondata(chunk) {\n    if (dest.writable) {\n      if (false === dest.write(chunk) && source.pause) {\n        source.pause();\n      }\n    }\n  }\n  source.on('data', ondata);\n  function ondrain() {\n    if (source.readable && source.resume) {\n      source.resume();\n    }\n  }\n  dest.on('drain', ondrain);\n\n  // If the 'end' option is not supplied, dest.end() will be called when\n  // source gets the 'end' or 'close' events.  Only dest.end() once.\n  if (!dest._isStdio && (!options || options.end !== false)) {\n    source.on('end', onend);\n    source.on('close', onclose);\n  }\n  var didOnEnd = false;\n  function onend() {\n    if (didOnEnd) return;\n    didOnEnd = true;\n    dest.end();\n  }\n  function onclose() {\n    if (didOnEnd) return;\n    didOnEnd = true;\n    if (typeof dest.destroy === 'function') dest.destroy();\n  }\n\n  // don't leave dangling pipes when there are errors.\n  function onerror(er) {\n    cleanup();\n    if (EE.listenerCount(this, 'error') === 0) {\n      throw er; // Unhandled stream error in pipe.\n    }\n  }\n  source.on('error', onerror);\n  dest.on('error', onerror);\n\n  // remove all the event listeners that were added.\n  function cleanup() {\n    source.removeListener('data', ondata);\n    dest.removeListener('drain', ondrain);\n    source.removeListener('end', onend);\n    source.removeListener('close', onclose);\n    source.removeListener('error', onerror);\n    dest.removeListener('error', onerror);\n    source.removeListener('end', cleanup);\n    source.removeListener('close', cleanup);\n    dest.removeListener('close', cleanup);\n  }\n  source.on('end', cleanup);\n  source.on('close', cleanup);\n  dest.on('close', cleanup);\n  dest.emit('pipe', source);\n\n  // Allow for unix-like usage: A.pipe(B).pipe(C)\n  return dest;\n};", "map": {"version": 3, "names": ["module", "exports", "Stream", "EE", "require", "EventEmitter", "inherits", "Readable", "Writable", "Duplex", "Transform", "PassThrough", "finished", "pipeline", "call", "prototype", "pipe", "dest", "options", "source", "ondata", "chunk", "writable", "write", "pause", "on", "ondrain", "readable", "resume", "_isStdio", "end", "onend", "onclose", "didOnEnd", "destroy", "onerror", "er", "cleanup", "listenerCount", "removeListener", "emit"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/stream-browserify/index.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nmodule.exports = Stream;\n\nvar EE = require('events').EventEmitter;\nvar inherits = require('inherits');\n\ninherits(Stream, EE);\nStream.Readable = require('readable-stream/lib/_stream_readable.js');\nStream.Writable = require('readable-stream/lib/_stream_writable.js');\nStream.Duplex = require('readable-stream/lib/_stream_duplex.js');\nStream.Transform = require('readable-stream/lib/_stream_transform.js');\nStream.PassThrough = require('readable-stream/lib/_stream_passthrough.js');\nStream.finished = require('readable-stream/lib/internal/streams/end-of-stream.js')\nStream.pipeline = require('readable-stream/lib/internal/streams/pipeline.js')\n\n// Backwards-compat with node 0.4.x\nStream.Stream = Stream;\n\n\n\n// old-style streams.  Note that the pipe method (the only relevant\n// part of this class) is overridden in the Readable class.\n\nfunction Stream() {\n  EE.call(this);\n}\n\nStream.prototype.pipe = function(dest, options) {\n  var source = this;\n\n  function ondata(chunk) {\n    if (dest.writable) {\n      if (false === dest.write(chunk) && source.pause) {\n        source.pause();\n      }\n    }\n  }\n\n  source.on('data', ondata);\n\n  function ondrain() {\n    if (source.readable && source.resume) {\n      source.resume();\n    }\n  }\n\n  dest.on('drain', ondrain);\n\n  // If the 'end' option is not supplied, dest.end() will be called when\n  // source gets the 'end' or 'close' events.  Only dest.end() once.\n  if (!dest._isStdio && (!options || options.end !== false)) {\n    source.on('end', onend);\n    source.on('close', onclose);\n  }\n\n  var didOnEnd = false;\n  function onend() {\n    if (didOnEnd) return;\n    didOnEnd = true;\n\n    dest.end();\n  }\n\n\n  function onclose() {\n    if (didOnEnd) return;\n    didOnEnd = true;\n\n    if (typeof dest.destroy === 'function') dest.destroy();\n  }\n\n  // don't leave dangling pipes when there are errors.\n  function onerror(er) {\n    cleanup();\n    if (EE.listenerCount(this, 'error') === 0) {\n      throw er; // Unhandled stream error in pipe.\n    }\n  }\n\n  source.on('error', onerror);\n  dest.on('error', onerror);\n\n  // remove all the event listeners that were added.\n  function cleanup() {\n    source.removeListener('data', ondata);\n    dest.removeListener('drain', ondrain);\n\n    source.removeListener('end', onend);\n    source.removeListener('close', onclose);\n\n    source.removeListener('error', onerror);\n    dest.removeListener('error', onerror);\n\n    source.removeListener('end', cleanup);\n    source.removeListener('close', cleanup);\n\n    dest.removeListener('close', cleanup);\n  }\n\n  source.on('end', cleanup);\n  source.on('close', cleanup);\n\n  dest.on('close', cleanup);\n\n  dest.emit('pipe', source);\n\n  // Allow for unix-like usage: A.pipe(B).pipe(C)\n  return dest;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAGC,MAAM;AAEvB,IAAIC,EAAE,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY;AACvC,IAAIC,QAAQ,GAAGF,OAAO,CAAC,UAAU,CAAC;AAElCE,QAAQ,CAACJ,MAAM,EAAEC,EAAE,CAAC;AACpBD,MAAM,CAACK,QAAQ,GAAGH,OAAO,CAAC,yCAAyC,CAAC;AACpEF,MAAM,CAACM,QAAQ,GAAGJ,OAAO,CAAC,yCAAyC,CAAC;AACpEF,MAAM,CAACO,MAAM,GAAGL,OAAO,CAAC,uCAAuC,CAAC;AAChEF,MAAM,CAACQ,SAAS,GAAGN,OAAO,CAAC,0CAA0C,CAAC;AACtEF,MAAM,CAACS,WAAW,GAAGP,OAAO,CAAC,4CAA4C,CAAC;AAC1EF,MAAM,CAACU,QAAQ,GAAGR,OAAO,CAAC,uDAAuD,CAAC;AAClFF,MAAM,CAACW,QAAQ,GAAGT,OAAO,CAAC,kDAAkD,CAAC;;AAE7E;AACAF,MAAM,CAACA,MAAM,GAAGA,MAAM;;AAItB;AACA;;AAEA,SAASA,MAAMA,CAAA,EAAG;EAChBC,EAAE,CAACW,IAAI,CAAC,IAAI,CAAC;AACf;AAEAZ,MAAM,CAACa,SAAS,CAACC,IAAI,GAAG,UAASC,IAAI,EAAEC,OAAO,EAAE;EAC9C,IAAIC,MAAM,GAAG,IAAI;EAEjB,SAASC,MAAMA,CAACC,KAAK,EAAE;IACrB,IAAIJ,IAAI,CAACK,QAAQ,EAAE;MACjB,IAAI,KAAK,KAAKL,IAAI,CAACM,KAAK,CAACF,KAAK,CAAC,IAAIF,MAAM,CAACK,KAAK,EAAE;QAC/CL,MAAM,CAACK,KAAK,CAAC,CAAC;MAChB;IACF;EACF;EAEAL,MAAM,CAACM,EAAE,CAAC,MAAM,EAAEL,MAAM,CAAC;EAEzB,SAASM,OAAOA,CAAA,EAAG;IACjB,IAAIP,MAAM,CAACQ,QAAQ,IAAIR,MAAM,CAACS,MAAM,EAAE;MACpCT,MAAM,CAACS,MAAM,CAAC,CAAC;IACjB;EACF;EAEAX,IAAI,CAACQ,EAAE,CAAC,OAAO,EAAEC,OAAO,CAAC;;EAEzB;EACA;EACA,IAAI,CAACT,IAAI,CAACY,QAAQ,KAAK,CAACX,OAAO,IAAIA,OAAO,CAACY,GAAG,KAAK,KAAK,CAAC,EAAE;IACzDX,MAAM,CAACM,EAAE,CAAC,KAAK,EAAEM,KAAK,CAAC;IACvBZ,MAAM,CAACM,EAAE,CAAC,OAAO,EAAEO,OAAO,CAAC;EAC7B;EAEA,IAAIC,QAAQ,GAAG,KAAK;EACpB,SAASF,KAAKA,CAAA,EAAG;IACf,IAAIE,QAAQ,EAAE;IACdA,QAAQ,GAAG,IAAI;IAEfhB,IAAI,CAACa,GAAG,CAAC,CAAC;EACZ;EAGA,SAASE,OAAOA,CAAA,EAAG;IACjB,IAAIC,QAAQ,EAAE;IACdA,QAAQ,GAAG,IAAI;IAEf,IAAI,OAAOhB,IAAI,CAACiB,OAAO,KAAK,UAAU,EAAEjB,IAAI,CAACiB,OAAO,CAAC,CAAC;EACxD;;EAEA;EACA,SAASC,OAAOA,CAACC,EAAE,EAAE;IACnBC,OAAO,CAAC,CAAC;IACT,IAAIlC,EAAE,CAACmC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE;MACzC,MAAMF,EAAE,CAAC,CAAC;IACZ;EACF;EAEAjB,MAAM,CAACM,EAAE,CAAC,OAAO,EAAEU,OAAO,CAAC;EAC3BlB,IAAI,CAACQ,EAAE,CAAC,OAAO,EAAEU,OAAO,CAAC;;EAEzB;EACA,SAASE,OAAOA,CAAA,EAAG;IACjBlB,MAAM,CAACoB,cAAc,CAAC,MAAM,EAAEnB,MAAM,CAAC;IACrCH,IAAI,CAACsB,cAAc,CAAC,OAAO,EAAEb,OAAO,CAAC;IAErCP,MAAM,CAACoB,cAAc,CAAC,KAAK,EAAER,KAAK,CAAC;IACnCZ,MAAM,CAACoB,cAAc,CAAC,OAAO,EAAEP,OAAO,CAAC;IAEvCb,MAAM,CAACoB,cAAc,CAAC,OAAO,EAAEJ,OAAO,CAAC;IACvClB,IAAI,CAACsB,cAAc,CAAC,OAAO,EAAEJ,OAAO,CAAC;IAErChB,MAAM,CAACoB,cAAc,CAAC,KAAK,EAAEF,OAAO,CAAC;IACrClB,MAAM,CAACoB,cAAc,CAAC,OAAO,EAAEF,OAAO,CAAC;IAEvCpB,IAAI,CAACsB,cAAc,CAAC,OAAO,EAAEF,OAAO,CAAC;EACvC;EAEAlB,MAAM,CAACM,EAAE,CAAC,KAAK,EAAEY,OAAO,CAAC;EACzBlB,MAAM,CAACM,EAAE,CAAC,OAAO,EAAEY,OAAO,CAAC;EAE3BpB,IAAI,CAACQ,EAAE,CAAC,OAAO,EAAEY,OAAO,CAAC;EAEzBpB,IAAI,CAACuB,IAAI,CAAC,MAAM,EAAErB,MAAM,CAAC;;EAEzB;EACA,OAAOF,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "'use strict';\n\nvar ERR_INVALID_OPT_VALUE = require('../../../errors').codes.ERR_INVALID_OPT_VALUE;\nfunction highWaterMarkFrom(options, isDuplex, duplexKey) {\n  return options.highWaterMark != null ? options.highWaterMark : isDuplex ? options[duplexKey] : null;\n}\nfunction getHighWaterMark(state, options, duplexKey, isDuplex) {\n  var hwm = highWaterMarkFrom(options, isDuplex, duplexKey);\n  if (hwm != null) {\n    if (!(isFinite(hwm) && Math.floor(hwm) === hwm) || hwm < 0) {\n      var name = isDuplex ? duplexKey : 'highWaterMark';\n      throw new ERR_INVALID_OPT_VALUE(name, hwm);\n    }\n    return Math.floor(hwm);\n  }\n\n  // Default value\n  return state.objectMode ? 16 : 16 * 1024;\n}\nmodule.exports = {\n  getHighWaterMark: getHighWaterMark\n};", "map": {"version": 3, "names": ["ERR_INVALID_OPT_VALUE", "require", "codes", "highWaterMarkFrom", "options", "isDuplex", "duplexKey", "highWaterMark", "getHighWaterMark", "state", "hwm", "isFinite", "Math", "floor", "name", "objectMode", "module", "exports"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/readable-stream/lib/internal/streams/state.js"], "sourcesContent": ["'use strict';\n\nvar ERR_INVALID_OPT_VALUE = require('../../../errors').codes.ERR_INVALID_OPT_VALUE;\nfunction highWaterMarkFrom(options, isDuplex, duplexKey) {\n  return options.highWaterMark != null ? options.highWaterMark : isDuplex ? options[duplexKey] : null;\n}\nfunction getHighWaterMark(state, options, duplexKey, isDuplex) {\n  var hwm = highWaterMarkFrom(options, isDuplex, duplexKey);\n  if (hwm != null) {\n    if (!(isFinite(hwm) && Math.floor(hwm) === hwm) || hwm < 0) {\n      var name = isDuplex ? duplexKey : 'highWaterMark';\n      throw new ERR_INVALID_OPT_VALUE(name, hwm);\n    }\n    return Math.floor(hwm);\n  }\n\n  // Default value\n  return state.objectMode ? 16 : 16 * 1024;\n}\nmodule.exports = {\n  getHighWaterMark: getHighWaterMark\n};"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,qBAAqB,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACC,KAAK,CAACF,qBAAqB;AAClF,SAASG,iBAAiBA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EACvD,OAAOF,OAAO,CAACG,aAAa,IAAI,IAAI,GAAGH,OAAO,CAACG,aAAa,GAAGF,QAAQ,GAAGD,OAAO,CAACE,SAAS,CAAC,GAAG,IAAI;AACrG;AACA,SAASE,gBAAgBA,CAACC,KAAK,EAAEL,OAAO,EAAEE,SAAS,EAAED,QAAQ,EAAE;EAC7D,IAAIK,GAAG,GAAGP,iBAAiB,CAACC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,CAAC;EACzD,IAAII,GAAG,IAAI,IAAI,EAAE;IACf,IAAI,EAAEC,QAAQ,CAACD,GAAG,CAAC,IAAIE,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,KAAKA,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,EAAE;MAC1D,IAAII,IAAI,GAAGT,QAAQ,GAAGC,SAAS,GAAG,eAAe;MACjD,MAAM,IAAIN,qBAAqB,CAACc,IAAI,EAAEJ,GAAG,CAAC;IAC5C;IACA,OAAOE,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;EACxB;;EAEA;EACA,OAAOD,KAAK,CAACM,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;AAC1C;AACAC,MAAM,CAACC,OAAO,GAAG;EACfT,gBAAgB,EAAEA;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
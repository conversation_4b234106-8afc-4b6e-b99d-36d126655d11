{"ast": null, "code": "// Ported from https://github.com/mafintosh/end-of-stream with\n// permission from the author, <PERSON> (@mafintosh).\n\n'use strict';\n\nvar ERR_STREAM_PREMATURE_CLOSE = require('../../../errors').codes.ERR_STREAM_PREMATURE_CLOSE;\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    callback.apply(this, args);\n  };\n}\nfunction noop() {}\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\nfunction eos(stream, opts, callback) {\n  if (typeof opts === 'function') return eos(stream, null, opts);\n  if (!opts) opts = {};\n  callback = once(callback || noop);\n  var readable = opts.readable || opts.readable !== false && stream.readable;\n  var writable = opts.writable || opts.writable !== false && stream.writable;\n  var onlegacyfinish = function onlegacyfinish() {\n    if (!stream.writable) onfinish();\n  };\n  var writableEnded = stream._writableState && stream._writableState.finished;\n  var onfinish = function onfinish() {\n    writable = false;\n    writableEnded = true;\n    if (!readable) callback.call(stream);\n  };\n  var readableEnded = stream._readableState && stream._readableState.endEmitted;\n  var onend = function onend() {\n    readable = false;\n    readableEnded = true;\n    if (!writable) callback.call(stream);\n  };\n  var onerror = function onerror(err) {\n    callback.call(stream, err);\n  };\n  var onclose = function onclose() {\n    var err;\n    if (readable && !readableEnded) {\n      if (!stream._readableState || !stream._readableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n    if (writable && !writableEnded) {\n      if (!stream._writableState || !stream._writableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n  };\n  var onrequest = function onrequest() {\n    stream.req.on('finish', onfinish);\n  };\n  if (isRequest(stream)) {\n    stream.on('complete', onfinish);\n    stream.on('abort', onclose);\n    if (stream.req) onrequest();else stream.on('request', onrequest);\n  } else if (writable && !stream._writableState) {\n    // legacy streams\n    stream.on('end', onlegacyfinish);\n    stream.on('close', onlegacyfinish);\n  }\n  stream.on('end', onend);\n  stream.on('finish', onfinish);\n  if (opts.error !== false) stream.on('error', onerror);\n  stream.on('close', onclose);\n  return function () {\n    stream.removeListener('complete', onfinish);\n    stream.removeListener('abort', onclose);\n    stream.removeListener('request', onrequest);\n    if (stream.req) stream.req.removeListener('finish', onfinish);\n    stream.removeListener('end', onlegacyfinish);\n    stream.removeListener('close', onlegacyfinish);\n    stream.removeListener('finish', onfinish);\n    stream.removeListener('end', onend);\n    stream.removeListener('error', onerror);\n    stream.removeListener('close', onclose);\n  };\n}\nmodule.exports = eos;", "map": {"version": 3, "names": ["ERR_STREAM_PREMATURE_CLOSE", "require", "codes", "once", "callback", "called", "_len", "arguments", "length", "args", "Array", "_key", "apply", "noop", "isRequest", "stream", "<PERSON><PERSON><PERSON><PERSON>", "abort", "eos", "opts", "readable", "writable", "onlegacyfinish", "onfinish", "writableEnded", "_writableState", "finished", "call", "readableEnded", "_readableState", "endEmitted", "onend", "onerror", "err", "onclose", "ended", "onrequest", "req", "on", "error", "removeListener", "module", "exports"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/readable-stream/lib/internal/streams/end-of-stream.js"], "sourcesContent": ["// Ported from https://github.com/mafintosh/end-of-stream with\n// permission from the author, <PERSON> (@mafintosh).\n\n'use strict';\n\nvar ERR_STREAM_PREMATURE_CLOSE = require('../../../errors').codes.ERR_STREAM_PREMATURE_CLOSE;\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    callback.apply(this, args);\n  };\n}\nfunction noop() {}\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\nfunction eos(stream, opts, callback) {\n  if (typeof opts === 'function') return eos(stream, null, opts);\n  if (!opts) opts = {};\n  callback = once(callback || noop);\n  var readable = opts.readable || opts.readable !== false && stream.readable;\n  var writable = opts.writable || opts.writable !== false && stream.writable;\n  var onlegacyfinish = function onlegacyfinish() {\n    if (!stream.writable) onfinish();\n  };\n  var writableEnded = stream._writableState && stream._writableState.finished;\n  var onfinish = function onfinish() {\n    writable = false;\n    writableEnded = true;\n    if (!readable) callback.call(stream);\n  };\n  var readableEnded = stream._readableState && stream._readableState.endEmitted;\n  var onend = function onend() {\n    readable = false;\n    readableEnded = true;\n    if (!writable) callback.call(stream);\n  };\n  var onerror = function onerror(err) {\n    callback.call(stream, err);\n  };\n  var onclose = function onclose() {\n    var err;\n    if (readable && !readableEnded) {\n      if (!stream._readableState || !stream._readableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n    if (writable && !writableEnded) {\n      if (!stream._writableState || !stream._writableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n  };\n  var onrequest = function onrequest() {\n    stream.req.on('finish', onfinish);\n  };\n  if (isRequest(stream)) {\n    stream.on('complete', onfinish);\n    stream.on('abort', onclose);\n    if (stream.req) onrequest();else stream.on('request', onrequest);\n  } else if (writable && !stream._writableState) {\n    // legacy streams\n    stream.on('end', onlegacyfinish);\n    stream.on('close', onlegacyfinish);\n  }\n  stream.on('end', onend);\n  stream.on('finish', onfinish);\n  if (opts.error !== false) stream.on('error', onerror);\n  stream.on('close', onclose);\n  return function () {\n    stream.removeListener('complete', onfinish);\n    stream.removeListener('abort', onclose);\n    stream.removeListener('request', onrequest);\n    if (stream.req) stream.req.removeListener('finish', onfinish);\n    stream.removeListener('end', onlegacyfinish);\n    stream.removeListener('close', onlegacyfinish);\n    stream.removeListener('finish', onfinish);\n    stream.removeListener('end', onend);\n    stream.removeListener('error', onerror);\n    stream.removeListener('close', onclose);\n  };\n}\nmodule.exports = eos;"], "mappings": "AAAA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,0BAA0B,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACC,KAAK,CAACF,0BAA0B;AAC5F,SAASG,IAAIA,CAACC,QAAQ,EAAE;EACtB,IAAIC,MAAM,GAAG,KAAK;EAClB,OAAO,YAAY;IACjB,IAAIA,MAAM,EAAE;IACZA,MAAM,GAAG,IAAI;IACb,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAP,QAAQ,CAACQ,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;EAC5B,CAAC;AACH;AACA,SAASI,IAAIA,CAAA,EAAG,CAAC;AACjB,SAASC,SAASA,CAACC,MAAM,EAAE;EACzB,OAAOA,MAAM,CAACC,SAAS,IAAI,OAAOD,MAAM,CAACE,KAAK,KAAK,UAAU;AAC/D;AACA,SAASC,GAAGA,CAACH,MAAM,EAAEI,IAAI,EAAEf,QAAQ,EAAE;EACnC,IAAI,OAAOe,IAAI,KAAK,UAAU,EAAE,OAAOD,GAAG,CAACH,MAAM,EAAE,IAAI,EAAEI,IAAI,CAAC;EAC9D,IAAI,CAACA,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACpBf,QAAQ,GAAGD,IAAI,CAACC,QAAQ,IAAIS,IAAI,CAAC;EACjC,IAAIO,QAAQ,GAAGD,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,KAAK,KAAK,IAAIL,MAAM,CAACK,QAAQ;EAC1E,IAAIC,QAAQ,GAAGF,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,KAAK,KAAK,IAAIN,MAAM,CAACM,QAAQ;EAC1E,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,IAAI,CAACP,MAAM,CAACM,QAAQ,EAAEE,QAAQ,CAAC,CAAC;EAClC,CAAC;EACD,IAAIC,aAAa,GAAGT,MAAM,CAACU,cAAc,IAAIV,MAAM,CAACU,cAAc,CAACC,QAAQ;EAC3E,IAAIH,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjCF,QAAQ,GAAG,KAAK;IAChBG,aAAa,GAAG,IAAI;IACpB,IAAI,CAACJ,QAAQ,EAAEhB,QAAQ,CAACuB,IAAI,CAACZ,MAAM,CAAC;EACtC,CAAC;EACD,IAAIa,aAAa,GAAGb,MAAM,CAACc,cAAc,IAAId,MAAM,CAACc,cAAc,CAACC,UAAU;EAC7E,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3BX,QAAQ,GAAG,KAAK;IAChBQ,aAAa,GAAG,IAAI;IACpB,IAAI,CAACP,QAAQ,EAAEjB,QAAQ,CAACuB,IAAI,CAACZ,MAAM,CAAC;EACtC,CAAC;EACD,IAAIiB,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;IAClC7B,QAAQ,CAACuB,IAAI,CAACZ,MAAM,EAAEkB,GAAG,CAAC;EAC5B,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,IAAID,GAAG;IACP,IAAIb,QAAQ,IAAI,CAACQ,aAAa,EAAE;MAC9B,IAAI,CAACb,MAAM,CAACc,cAAc,IAAI,CAACd,MAAM,CAACc,cAAc,CAACM,KAAK,EAAEF,GAAG,GAAG,IAAIjC,0BAA0B,CAAC,CAAC;MAClG,OAAOI,QAAQ,CAACuB,IAAI,CAACZ,MAAM,EAAEkB,GAAG,CAAC;IACnC;IACA,IAAIZ,QAAQ,IAAI,CAACG,aAAa,EAAE;MAC9B,IAAI,CAACT,MAAM,CAACU,cAAc,IAAI,CAACV,MAAM,CAACU,cAAc,CAACU,KAAK,EAAEF,GAAG,GAAG,IAAIjC,0BAA0B,CAAC,CAAC;MAClG,OAAOI,QAAQ,CAACuB,IAAI,CAACZ,MAAM,EAAEkB,GAAG,CAAC;IACnC;EACF,CAAC;EACD,IAAIG,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnCrB,MAAM,CAACsB,GAAG,CAACC,EAAE,CAAC,QAAQ,EAAEf,QAAQ,CAAC;EACnC,CAAC;EACD,IAAIT,SAAS,CAACC,MAAM,CAAC,EAAE;IACrBA,MAAM,CAACuB,EAAE,CAAC,UAAU,EAAEf,QAAQ,CAAC;IAC/BR,MAAM,CAACuB,EAAE,CAAC,OAAO,EAAEJ,OAAO,CAAC;IAC3B,IAAInB,MAAM,CAACsB,GAAG,EAAED,SAAS,CAAC,CAAC,CAAC,KAAKrB,MAAM,CAACuB,EAAE,CAAC,SAAS,EAAEF,SAAS,CAAC;EAClE,CAAC,MAAM,IAAIf,QAAQ,IAAI,CAACN,MAAM,CAACU,cAAc,EAAE;IAC7C;IACAV,MAAM,CAACuB,EAAE,CAAC,KAAK,EAAEhB,cAAc,CAAC;IAChCP,MAAM,CAACuB,EAAE,CAAC,OAAO,EAAEhB,cAAc,CAAC;EACpC;EACAP,MAAM,CAACuB,EAAE,CAAC,KAAK,EAAEP,KAAK,CAAC;EACvBhB,MAAM,CAACuB,EAAE,CAAC,QAAQ,EAAEf,QAAQ,CAAC;EAC7B,IAAIJ,IAAI,CAACoB,KAAK,KAAK,KAAK,EAAExB,MAAM,CAACuB,EAAE,CAAC,OAAO,EAAEN,OAAO,CAAC;EACrDjB,MAAM,CAACuB,EAAE,CAAC,OAAO,EAAEJ,OAAO,CAAC;EAC3B,OAAO,YAAY;IACjBnB,MAAM,CAACyB,cAAc,CAAC,UAAU,EAAEjB,QAAQ,CAAC;IAC3CR,MAAM,CAACyB,cAAc,CAAC,OAAO,EAAEN,OAAO,CAAC;IACvCnB,MAAM,CAACyB,cAAc,CAAC,SAAS,EAAEJ,SAAS,CAAC;IAC3C,IAAIrB,MAAM,CAACsB,GAAG,EAAEtB,MAAM,CAACsB,GAAG,CAACG,cAAc,CAAC,QAAQ,EAAEjB,QAAQ,CAAC;IAC7DR,MAAM,CAACyB,cAAc,CAAC,KAAK,EAAElB,cAAc,CAAC;IAC5CP,MAAM,CAACyB,cAAc,CAAC,OAAO,EAAElB,cAAc,CAAC;IAC9CP,MAAM,CAACyB,cAAc,CAAC,QAAQ,EAAEjB,QAAQ,CAAC;IACzCR,MAAM,CAACyB,cAAc,CAAC,KAAK,EAAET,KAAK,CAAC;IACnChB,MAAM,CAACyB,cAAc,CAAC,OAAO,EAAER,OAAO,CAAC;IACvCjB,MAAM,CAACyB,cAAc,CAAC,OAAO,EAAEN,OAAO,CAAC;EACzC,CAAC;AACH;AACAO,MAAM,CAACC,OAAO,GAAGxB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
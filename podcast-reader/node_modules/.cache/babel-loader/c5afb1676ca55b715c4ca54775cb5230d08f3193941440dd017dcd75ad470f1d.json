{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  var NodeType,\n    XMLDTDElement,\n    XMLNode,\n    extend = function (child, parent) {\n      for (var key in parent) {\n        if (hasProp.call(parent, key)) child[key] = parent[key];\n      }\n      function ctor() {\n        this.constructor = child;\n      }\n      ctor.prototype = parent.prototype;\n      child.prototype = new ctor();\n      child.__super__ = parent.prototype;\n      return child;\n    },\n    hasProp = {}.hasOwnProperty;\n  XMLNode = require('./XMLNode');\n  NodeType = require('./NodeType');\n  module.exports = XMLDTDElement = function (superClass) {\n    extend(XMLDTDElement, superClass);\n    function XMLDTDElement(parent, name, value) {\n      XMLDTDElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.ElementDeclaration;\n      this.value = this.stringify.dtdElementValue(value);\n    }\n    XMLDTDElement.prototype.toString = function (options) {\n      return this.options.writer.dtdElement(this, this.options.writer.filterOptions(options));\n    };\n    return XMLDTDElement;\n  }(XMLNode);\n}).call(this);", "map": {"version": 3, "names": ["NodeType", "XMLDTDElement", "XMLNode", "extend", "child", "parent", "key", "hasProp", "call", "ctor", "constructor", "prototype", "__super__", "hasOwnProperty", "require", "module", "exports", "superClass", "name", "value", "Error", "debugInfo", "Array", "isArray", "join", "stringify", "type", "ElementDeclaration", "dtdElementValue", "toString", "options", "writer", "dtdElement", "filterOptions"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xmlbuilder/lib/XMLDTDElement.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDElement, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDTDElement = (function(superClass) {\n    extend(XMLDTDElement, superClass);\n\n    function XMLDTDElement(parent, name, value) {\n      XMLDTDElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.ElementDeclaration;\n      this.value = this.stringify.dtdElementValue(value);\n    }\n\n    XMLDTDElement.prototype.toString = function(options) {\n      return this.options.writer.dtdElement(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDElement;\n\n  })(XMLNode);\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACV,IAAIA,QAAQ;IAAEC,aAAa;IAAEC,OAAO;IAClCC,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAEC,MAAM,EAAE;MAAE,KAAK,IAAIC,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIE,OAAO,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAEF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;MAAE,SAASG,IAAIA,CAAA,EAAG;QAAE,IAAI,CAACC,WAAW,GAAGN,KAAK;MAAE;MAAEK,IAAI,CAACE,SAAS,GAAGN,MAAM,CAACM,SAAS;MAAEP,KAAK,CAACO,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;MAAEL,KAAK,CAACQ,SAAS,GAAGP,MAAM,CAACM,SAAS;MAAE,OAAOP,KAAK;IAAE,CAAC;IAC1RG,OAAO,GAAG,CAAC,CAAC,CAACM,cAAc;EAE7BX,OAAO,GAAGY,OAAO,CAAC,WAAW,CAAC;EAE9Bd,QAAQ,GAAGc,OAAO,CAAC,YAAY,CAAC;EAEhCC,MAAM,CAACC,OAAO,GAAGf,aAAa,GAAI,UAASgB,UAAU,EAAE;IACrDd,MAAM,CAACF,aAAa,EAAEgB,UAAU,CAAC;IAEjC,SAAShB,aAAaA,CAACI,MAAM,EAAEa,IAAI,EAAEC,KAAK,EAAE;MAC1ClB,aAAa,CAACW,SAAS,CAACF,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEH,MAAM,CAAC;MACtD,IAAIa,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAIE,KAAK,CAAC,4BAA4B,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MAClE;MACA,IAAI,CAACF,KAAK,EAAE;QACVA,KAAK,GAAG,WAAW;MACrB;MACA,IAAIG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;QACxBA,KAAK,GAAG,GAAG,GAAGA,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;MACrC;MACA,IAAI,CAACN,IAAI,GAAG,IAAI,CAACO,SAAS,CAACP,IAAI,CAACA,IAAI,CAAC;MACrC,IAAI,CAACQ,IAAI,GAAG1B,QAAQ,CAAC2B,kBAAkB;MACvC,IAAI,CAACR,KAAK,GAAG,IAAI,CAACM,SAAS,CAACG,eAAe,CAACT,KAAK,CAAC;IACpD;IAEAlB,aAAa,CAACU,SAAS,CAACkB,QAAQ,GAAG,UAASC,OAAO,EAAE;MACnD,OAAO,IAAI,CAACA,OAAO,CAACC,MAAM,CAACC,UAAU,CAAC,IAAI,EAAE,IAAI,CAACF,OAAO,CAACC,MAAM,CAACE,aAAa,CAACH,OAAO,CAAC,CAAC;IACzF,CAAC;IAED,OAAO7B,aAAa;EAEtB,CAAC,CAAEC,OAAO,CAAC;AAEb,CAAC,EAAEM,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
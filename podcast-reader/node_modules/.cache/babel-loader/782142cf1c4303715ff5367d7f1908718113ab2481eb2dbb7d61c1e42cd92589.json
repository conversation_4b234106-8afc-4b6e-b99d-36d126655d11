{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport PodcastHeader from './components/PodcastHeader';\nimport SearchBar from './components/SearchBar';\nimport PodcastStats from './components/PodcastStats';\nimport EpisodeList from './components/EpisodeList';\nimport EpisodeDetail from './components/EpisodeDetail';\nimport AudioPlayer from './components/AudioPlayer';\nimport { parseRSSFeed } from './utils/rssParser';\nimport { mockPodcastData } from './data/mockData';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [podcastData, setPodcastData] = useState(mockPodcastData);\n  const [filteredEpisodes, setFilteredEpisodes] = useState(mockPodcastData.episodes);\n  const [currentEpisode, setCurrentEpisode] = useState(null);\n  const [selectedEpisode, setSelectedEpisode] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // 搜索功能\n  const handleSearch = searchTerm => {\n    if (!searchTerm.trim()) {\n      setFilteredEpisodes(podcastData.episodes);\n      return;\n    }\n    const filtered = podcastData.episodes.filter(episode => episode.title.toLowerCase().includes(searchTerm.toLowerCase()) || episode.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    setFilteredEpisodes(filtered);\n  };\n\n  // 加载RSS源\n  const handleRSSUrlSubmit = async rssUrl => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const data = await parseRSSFeed(rssUrl);\n      setPodcastData(data);\n      setFilteredEpisodes(data.episodes);\n      setCurrentEpisode(null);\n    } catch (err) {\n      console.error('Failed to load RSS feed:', err);\n      setError('无法加载RSS源，请检查URL是否正确或网络连接。正在显示示例数据。');\n      // 如果加载失败，保持使用模拟数据\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 选择节目播放\n  const handleEpisodeSelect = episode => {\n    setCurrentEpisode(episode);\n  };\n\n  // 显示节目详情\n  const handleShowDetail = episode => {\n    setSelectedEpisode(episode);\n  };\n\n  // 关闭节目详情\n  const handleCloseDetail = () => {\n    setSelectedEpisode(null);\n  };\n\n  // 关闭播放器\n  const handleClosePlayer = () => {\n    setCurrentEpisode(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(PodcastHeader, {\n      podcastInfo: podcastData.podcastInfo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SearchBar, {\n      onSearch: handleSearch,\n      onRSSUrlSubmit: handleRSSUrlSubmit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 9\n    }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7DRSS\\u6E90...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(EpisodeList, {\n      episodes: filteredEpisodes,\n      onEpisodeSelect: handleEpisodeSelect,\n      onShowDetail: handleShowDetail,\n      currentEpisode: currentEpisode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 9\n    }, this), selectedEpisode && /*#__PURE__*/_jsxDEV(EpisodeDetail, {\n      episode: selectedEpisode,\n      onClose: handleCloseDetail,\n      onPlay: handleEpisodeSelect\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this), currentEpisode && /*#__PURE__*/_jsxDEV(AudioPlayer, {\n      episode: currentEpisode,\n      onClose: handleClosePlayer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"Y7cyycfjvsEAo6KTKYTnBpSUElc=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "PodcastHeader", "SearchBar", "PodcastStats", "EpisodeList", "EpisodeDetail", "AudioPlayer", "parseRSSFeed", "mockPodcastData", "jsxDEV", "_jsxDEV", "App", "_s", "podcastData", "setPodcastData", "filteredEpisodes", "setFilteredEpisodes", "episodes", "currentEpisode", "setCurrentEpisode", "selectedEpisode", "setSelectedEpisode", "isLoading", "setIsLoading", "error", "setError", "handleSearch", "searchTerm", "trim", "filtered", "filter", "episode", "title", "toLowerCase", "includes", "description", "handleRSSUrlSubmit", "rssUrl", "data", "err", "console", "handleEpisodeSelect", "handleShowDetail", "handleCloseDetail", "handleClosePlayer", "className", "children", "podcastInfo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSearch", "onRSSUrlSubmit", "onEpisodeSelect", "onShowDetail", "onClose", "onPlay", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport PodcastHeader from './components/PodcastHeader';\nimport SearchBar from './components/SearchBar';\nimport PodcastStats from './components/PodcastStats';\nimport EpisodeList from './components/EpisodeList';\nimport EpisodeDetail from './components/EpisodeDetail';\nimport AudioPlayer from './components/AudioPlayer';\nimport { parseRSSFeed } from './utils/rssParser';\nimport { mockPodcastData } from './data/mockData';\nimport './App.css';\n\nfunction App() {\n  const [podcastData, setPodcastData] = useState(mockPodcastData);\n  const [filteredEpisodes, setFilteredEpisodes] = useState(mockPodcastData.episodes);\n  const [currentEpisode, setCurrentEpisode] = useState(null);\n  const [selectedEpisode, setSelectedEpisode] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // 搜索功能\n  const handleSearch = (searchTerm) => {\n    if (!searchTerm.trim()) {\n      setFilteredEpisodes(podcastData.episodes);\n      return;\n    }\n\n    const filtered = podcastData.episodes.filter(episode =>\n      episode.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      episode.description.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n    setFilteredEpisodes(filtered);\n  };\n\n  // 加载RSS源\n  const handleRSSUrlSubmit = async (rssUrl) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const data = await parseRSSFeed(rssUrl);\n      setPodcastData(data);\n      setFilteredEpisodes(data.episodes);\n      setCurrentEpisode(null);\n    } catch (err) {\n      console.error('Failed to load RSS feed:', err);\n      setError('无法加载RSS源，请检查URL是否正确或网络连接。正在显示示例数据。');\n      // 如果加载失败，保持使用模拟数据\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 选择节目播放\n  const handleEpisodeSelect = (episode) => {\n    setCurrentEpisode(episode);\n  };\n\n  // 显示节目详情\n  const handleShowDetail = (episode) => {\n    setSelectedEpisode(episode);\n  };\n\n  // 关闭节目详情\n  const handleCloseDetail = () => {\n    setSelectedEpisode(null);\n  };\n\n  // 关闭播放器\n  const handleClosePlayer = () => {\n    setCurrentEpisode(null);\n  };\n\n  return (\n    <div className=\"App\">\n      <PodcastHeader podcastInfo={podcastData.podcastInfo} />\n\n      <SearchBar\n        onSearch={handleSearch}\n        onRSSUrlSubmit={handleRSSUrlSubmit}\n      />\n\n      {error && (\n        <div className=\"error-message\">\n          <p>{error}</p>\n        </div>\n      )}\n\n      {isLoading ? (\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>正在加载RSS源...</p>\n        </div>\n      ) : (\n        <EpisodeList\n          episodes={filteredEpisodes}\n          onEpisodeSelect={handleEpisodeSelect}\n          onShowDetail={handleShowDetail}\n          currentEpisode={currentEpisode}\n        />\n      )}\n\n      {selectedEpisode && (\n        <EpisodeDetail\n          episode={selectedEpisode}\n          onClose={handleCloseDetail}\n          onPlay={handleEpisodeSelect}\n        />\n      )}\n\n      {currentEpisode && (\n        <AudioPlayer\n          episode={currentEpisode}\n          onClose={handleClosePlayer}\n        />\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,eAAe,QAAQ,iBAAiB;AACjD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAACQ,eAAe,CAAC;EAC/D,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAACQ,eAAe,CAACS,QAAQ,CAAC;EAClF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM0B,YAAY,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE;MACtBZ,mBAAmB,CAACH,WAAW,CAACI,QAAQ,CAAC;MACzC;IACF;IAEA,MAAMY,QAAQ,GAAGhB,WAAW,CAACI,QAAQ,CAACa,MAAM,CAACC,OAAO,IAClDA,OAAO,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,UAAU,CAACM,WAAW,CAAC,CAAC,CAAC,IAC9DF,OAAO,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,UAAU,CAACM,WAAW,CAAC,CAAC,CACrE,CAAC;IACDjB,mBAAmB,CAACa,QAAQ,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3Cd,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMa,IAAI,GAAG,MAAM/B,YAAY,CAAC8B,MAAM,CAAC;MACvCvB,cAAc,CAACwB,IAAI,CAAC;MACpBtB,mBAAmB,CAACsB,IAAI,CAACrB,QAAQ,CAAC;MAClCE,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAAC,0BAA0B,EAAEe,GAAG,CAAC;MAC9Cd,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMkB,mBAAmB,GAAIV,OAAO,IAAK;IACvCZ,iBAAiB,CAACY,OAAO,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAIX,OAAO,IAAK;IACpCV,kBAAkB,CAACU,OAAO,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;IAC9BtB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMuB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,oBACET,OAAA;IAAKmC,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBpC,OAAA,CAACT,aAAa;MAAC8C,WAAW,EAAElC,WAAW,CAACkC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEvDzC,OAAA,CAACR,SAAS;MACRkD,QAAQ,EAAE1B,YAAa;MACvB2B,cAAc,EAAEjB;IAAmB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,EAED3B,KAAK,iBACJd,OAAA;MAAKmC,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BpC,OAAA;QAAAoC,QAAA,EAAItB;MAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACN,EAEA7B,SAAS,gBACRZ,OAAA;MAAKmC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCpC,OAAA;QAAKmC,SAAS,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCzC,OAAA;QAAAoC,QAAA,EAAG;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,gBAENzC,OAAA,CAACN,WAAW;MACVa,QAAQ,EAAEF,gBAAiB;MAC3BuC,eAAe,EAAEb,mBAAoB;MACrCc,YAAY,EAAEb,gBAAiB;MAC/BxB,cAAc,EAAEA;IAAe;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF,EAEA/B,eAAe,iBACdV,OAAA,CAACL,aAAa;MACZ0B,OAAO,EAAEX,eAAgB;MACzBoC,OAAO,EAAEb,iBAAkB;MAC3Bc,MAAM,EAAEhB;IAAoB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACF,EAEAjC,cAAc,iBACbR,OAAA,CAACJ,WAAW;MACVyB,OAAO,EAAEb,cAAe;MACxBsC,OAAO,EAAEZ;IAAkB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACvC,EAAA,CA1GQD,GAAG;AAAA+C,EAAA,GAAH/C,GAAG;AA4GZ,eAAeA,GAAG;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
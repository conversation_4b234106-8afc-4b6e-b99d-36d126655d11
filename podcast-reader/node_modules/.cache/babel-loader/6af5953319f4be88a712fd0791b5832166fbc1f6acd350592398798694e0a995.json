{"ast": null, "code": "// Generated by CoffeeScript 1.12.7\n(function () {\n  exports.defaults = {\n    \"0.1\": {\n      explicitCharkey: false,\n      trim: true,\n      normalize: true,\n      normalizeTags: false,\n      attrkey: \"@\",\n      charkey: \"#\",\n      explicitArray: false,\n      ignoreAttrs: false,\n      mergeAttrs: false,\n      explicitRoot: false,\n      validator: null,\n      xmlns: false,\n      explicitChildren: false,\n      childkey: '@@',\n      charsAsChildren: false,\n      includeWhiteChars: false,\n      async: false,\n      strict: true,\n      attrNameProcessors: null,\n      attrValueProcessors: null,\n      tagNameProcessors: null,\n      valueProcessors: null,\n      emptyTag: ''\n    },\n    \"0.2\": {\n      explicitCharkey: false,\n      trim: false,\n      normalize: false,\n      normalizeTags: false,\n      attrkey: \"$\",\n      charkey: \"_\",\n      explicitArray: true,\n      ignoreAttrs: false,\n      mergeAttrs: false,\n      explicitRoot: true,\n      validator: null,\n      xmlns: false,\n      explicitChildren: false,\n      preserveChildrenOrder: false,\n      childkey: '$$',\n      charsAsChildren: false,\n      includeWhiteChars: false,\n      async: false,\n      strict: true,\n      attrNameProcessors: null,\n      attrValueProcessors: null,\n      tagNameProcessors: null,\n      valueProcessors: null,\n      rootName: 'root',\n      xmldec: {\n        'version': '1.0',\n        'encoding': 'UTF-8',\n        'standalone': true\n      },\n      doctype: null,\n      renderOpts: {\n        'pretty': true,\n        'indent': '  ',\n        'newline': '\\n'\n      },\n      headless: false,\n      chunkSize: 10000,\n      emptyTag: '',\n      cdata: false\n    }\n  };\n}).call(this);", "map": {"version": 3, "names": ["exports", "defaults", "<PERSON><PERSON><PERSON><PERSON>", "trim", "normalize", "normalizeTags", "attrkey", "charkey", "explicitArray", "ignoreAttrs", "mergeAttrs", "explicitRoot", "validator", "xmlns", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childkey", "chars<PERSON><PERSON><PERSON><PERSON><PERSON>", "includeWhiteChars", "async", "strict", "attrNameProcessors", "attrValueProcessors", "tagNameProcessors", "valueProcessors", "emptyTag", "preserveChildrenOrder", "rootName", "xmldec", "doctype", "renderOpts", "headless", "chunkSize", "cdata", "call"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/xml2js/lib/defaults.js"], "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  exports.defaults = {\n    \"0.1\": {\n      explicitCharkey: false,\n      trim: true,\n      normalize: true,\n      normalizeTags: false,\n      attrkey: \"@\",\n      charkey: \"#\",\n      explicitArray: false,\n      ignoreAttrs: false,\n      mergeAttrs: false,\n      explicitRoot: false,\n      validator: null,\n      xmlns: false,\n      explicitChildren: false,\n      childkey: '@@',\n      charsAsChildren: false,\n      includeWhiteChars: false,\n      async: false,\n      strict: true,\n      attrNameProcessors: null,\n      attrValueProcessors: null,\n      tagNameProcessors: null,\n      valueProcessors: null,\n      emptyTag: ''\n    },\n    \"0.2\": {\n      explicitCharkey: false,\n      trim: false,\n      normalize: false,\n      normalizeTags: false,\n      attrkey: \"$\",\n      charkey: \"_\",\n      explicitArray: true,\n      ignoreAttrs: false,\n      mergeAttrs: false,\n      explicitRoot: true,\n      validator: null,\n      xmlns: false,\n      explicitChildren: false,\n      preserveChildrenOrder: false,\n      childkey: '$$',\n      charsAsChildren: false,\n      includeWhiteChars: false,\n      async: false,\n      strict: true,\n      attrNameProcessors: null,\n      attrValueProcessors: null,\n      tagNameProcessors: null,\n      valueProcessors: null,\n      rootName: 'root',\n      xmldec: {\n        'version': '1.0',\n        'encoding': 'UTF-8',\n        'standalone': true\n      },\n      doctype: null,\n      renderOpts: {\n        'pretty': true,\n        'indent': '  ',\n        'newline': '\\n'\n      },\n      headless: false,\n      chunkSize: 10000,\n      emptyTag: '',\n      cdata: false\n    }\n  };\n\n}).call(this);\n"], "mappings": "AAAA;AACA,CAAC,YAAW;EACVA,OAAO,CAACC,QAAQ,GAAG;IACjB,KAAK,EAAE;MACLC,eAAe,EAAE,KAAK;MACtBC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,aAAa,EAAE,KAAK;MACpBC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,aAAa,EAAE,KAAK;MACpBC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,IAAI;MACfC,KAAK,EAAE,KAAK;MACZC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,KAAK;MACtBC,iBAAiB,EAAE,KAAK;MACxBC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,IAAI;MACZC,kBAAkB,EAAE,IAAI;MACxBC,mBAAmB,EAAE,IAAI;MACzBC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,IAAI;MACrBC,QAAQ,EAAE;IACZ,CAAC;IACD,KAAK,EAAE;MACLtB,eAAe,EAAE,KAAK;MACtBC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,KAAK;MACpBC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE,IAAI;MACfC,KAAK,EAAE,KAAK;MACZC,gBAAgB,EAAE,KAAK;MACvBW,qBAAqB,EAAE,KAAK;MAC5BV,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,KAAK;MACtBC,iBAAiB,EAAE,KAAK;MACxBC,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,IAAI;MACZC,kBAAkB,EAAE,IAAI;MACxBC,mBAAmB,EAAE,IAAI;MACzBC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,IAAI;MACrBG,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE;QACN,SAAS,EAAE,KAAK;QAChB,UAAU,EAAE,OAAO;QACnB,YAAY,EAAE;MAChB,CAAC;MACDC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;QACV,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE;MACb,CAAC;MACDC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,KAAK;MAChBP,QAAQ,EAAE,EAAE;MACZQ,KAAK,EAAE;IACT;EACF,CAAC;AAEH,CAAC,EAAEC,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
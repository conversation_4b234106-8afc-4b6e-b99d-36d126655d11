{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/PodcastStats.js\";\nimport React from 'react';\nimport './PodcastStats.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PodcastStats = ({\n  episodes,\n  filteredCount\n}) => {\n  if (!episodes || episodes.length === 0) return null;\n  const totalEpisodes = episodes.length;\n  const showingCount = filteredCount !== undefined ? filteredCount : totalEpisodes;\n\n  // 计算总时长（如果有时长信息）\n  const totalDuration = episodes.reduce((total, episode) => {\n    if (!episode.duration) return total;\n\n    // 解析时长格式 (HH:MM:SS 或 MM:SS)\n    const parts = episode.duration.split(':').map(Number);\n    let seconds = 0;\n    if (parts.length === 3) {\n      // HH:MM:SS\n      seconds = parts[0] * 3600 + parts[1] * 60 + parts[2];\n    } else if (parts.length === 2) {\n      // MM:SS\n      seconds = parts[0] * 60 + parts[1];\n    }\n    return total + seconds;\n  }, 0);\n  const formatTotalDuration = totalSeconds => {\n    if (totalSeconds === 0) return null;\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor(totalSeconds % 3600 / 60);\n    if (hours > 0) {\n      return `${hours}小时${minutes}分钟`;\n    } else {\n      return `${minutes}分钟`;\n    }\n  };\n  const formattedDuration = formatTotalDuration(totalDuration);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"podcast-stats\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-number\",\n          children: showingCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-label\",\n          children: showingCount !== totalEpisodes ? '筛选结果' : '总节目数'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), showingCount !== totalEpisodes && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-number\",\n          children: totalEpisodes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-label\",\n          children: \"\\u603B\\u8282\\u76EE\\u6570\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), formattedDuration && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-number\",\n          children: formattedDuration\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"stat-label\",\n          children: \"\\u603B\\u65F6\\u957F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_c = PodcastStats;\nexport default PodcastStats;\nvar _c;\n$RefreshReg$(_c, \"PodcastStats\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PodcastStats", "episodes", "filteredCount", "length", "totalEpisodes", "showingCount", "undefined", "totalDuration", "reduce", "total", "episode", "duration", "parts", "split", "map", "Number", "seconds", "formatTotalDuration", "totalSeconds", "hours", "Math", "floor", "minutes", "formattedDuration", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/PodcastStats.js"], "sourcesContent": ["import React from 'react';\nimport './PodcastStats.css';\n\nconst PodcastStats = ({ episodes, filteredCount }) => {\n  if (!episodes || episodes.length === 0) return null;\n\n  const totalEpisodes = episodes.length;\n  const showingCount = filteredCount !== undefined ? filteredCount : totalEpisodes;\n  \n  // 计算总时长（如果有时长信息）\n  const totalDuration = episodes.reduce((total, episode) => {\n    if (!episode.duration) return total;\n    \n    // 解析时长格式 (HH:MM:SS 或 MM:SS)\n    const parts = episode.duration.split(':').map(Number);\n    let seconds = 0;\n    \n    if (parts.length === 3) {\n      // HH:MM:SS\n      seconds = parts[0] * 3600 + parts[1] * 60 + parts[2];\n    } else if (parts.length === 2) {\n      // MM:SS\n      seconds = parts[0] * 60 + parts[1];\n    }\n    \n    return total + seconds;\n  }, 0);\n\n  const formatTotalDuration = (totalSeconds) => {\n    if (totalSeconds === 0) return null;\n    \n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor((totalSeconds % 3600) / 60);\n    \n    if (hours > 0) {\n      return `${hours}小时${minutes}分钟`;\n    } else {\n      return `${minutes}分钟`;\n    }\n  };\n\n  const formattedDuration = formatTotalDuration(totalDuration);\n\n  return (\n    <div className=\"podcast-stats\">\n      <div className=\"stats-container\">\n        <div className=\"stat-item\">\n          <span className=\"stat-number\">{showingCount}</span>\n          <span className=\"stat-label\">\n            {showingCount !== totalEpisodes ? '筛选结果' : '总节目数'}\n          </span>\n        </div>\n        \n        {showingCount !== totalEpisodes && (\n          <div className=\"stat-item\">\n            <span className=\"stat-number\">{totalEpisodes}</span>\n            <span className=\"stat-label\">总节目数</span>\n          </div>\n        )}\n        \n        {formattedDuration && (\n          <div className=\"stat-item\">\n            <span className=\"stat-number\">{formattedDuration}</span>\n            <span className=\"stat-label\">总时长</span>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default PodcastStats;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAc,CAAC,KAAK;EACpD,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAEnD,MAAMC,aAAa,GAAGH,QAAQ,CAACE,MAAM;EACrC,MAAME,YAAY,GAAGH,aAAa,KAAKI,SAAS,GAAGJ,aAAa,GAAGE,aAAa;;EAEhF;EACA,MAAMG,aAAa,GAAGN,QAAQ,CAACO,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAK;IACxD,IAAI,CAACA,OAAO,CAACC,QAAQ,EAAE,OAAOF,KAAK;;IAEnC;IACA,MAAMG,KAAK,GAAGF,OAAO,CAACC,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IACrD,IAAIC,OAAO,GAAG,CAAC;IAEf,IAAIJ,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE;MACtB;MACAa,OAAO,GAAGJ,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC,MAAM,IAAIA,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE;MAC7B;MACAa,OAAO,GAAGJ,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC;IACpC;IAEA,OAAOH,KAAK,GAAGO,OAAO;EACxB,CAAC,EAAE,CAAC,CAAC;EAEL,MAAMC,mBAAmB,GAAIC,YAAY,IAAK;IAC5C,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,IAAI;IAEnC,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,IAAI,CAAC;IAC7C,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEH,YAAY,GAAG,IAAI,GAAI,EAAE,CAAC;IAEtD,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,KAAKG,OAAO,IAAI;IACjC,CAAC,MAAM;MACL,OAAO,GAAGA,OAAO,IAAI;IACvB;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGN,mBAAmB,CAACV,aAAa,CAAC;EAE5D,oBACER,OAAA;IAAKyB,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5B1B,OAAA;MAAKyB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB1B,OAAA;UAAMyB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEpB;QAAY;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnD9B,OAAA;UAAMyB,SAAS,EAAC,YAAY;UAAAC,QAAA,EACzBpB,YAAY,KAAKD,aAAa,GAAG,MAAM,GAAG;QAAM;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAELxB,YAAY,KAAKD,aAAa,iBAC7BL,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB1B,OAAA;UAAMyB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAErB;QAAa;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpD9B,OAAA;UAAMyB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CACN,EAEAN,iBAAiB,iBAChBxB,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB1B,OAAA;UAAMyB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEF;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxD9B,OAAA;UAAMyB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAlEI9B,YAAY;AAoElB,eAAeA,YAAY;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
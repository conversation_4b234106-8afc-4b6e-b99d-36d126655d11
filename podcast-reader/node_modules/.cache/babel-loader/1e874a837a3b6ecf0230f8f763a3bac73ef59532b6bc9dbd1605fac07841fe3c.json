{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeCard.js\";\nimport React from 'react';\nimport { formatDate, formatDuration } from '../utils/rssParser';\nimport './EpisodeCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EpisodeCard = ({\n  episode,\n  onSelect,\n  isActive\n}) => {\n  const handleClick = () => {\n    onSelect(episode);\n  };\n  const handlePlayClick = e => {\n    e.stopPropagation();\n    onSelect(episode);\n  };\n\n  // 截取描述文本\n  const truncateDescription = (text, maxLength = 200) => {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `episode-card ${isActive ? 'active' : ''}`,\n    onClick: handleClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"episode-card-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"episode-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"episode-title\",\n          children: episode.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"episode-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"episode-date\",\n            children: formatDate(episode.pubDate)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), episode.duration && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"episode-duration\",\n            children: formatDuration(episode.duration)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"episode-description\",\n        children: truncateDescription(episode.description)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"episode-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"play-button\",\n          onClick: handlePlayClick,\n          disabled: !episode.audioUrl,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8 5v14l11-7z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), \"\\u64AD\\u653E\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), episode.link && /*#__PURE__*/_jsxDEV(\"a\", {\n          href: episode.link,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"episode-link\",\n          onClick: e => e.stopPropagation(),\n          children: \"\\u67E5\\u770B\\u539F\\u6587\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_c = EpisodeCard;\nexport default EpisodeCard;\nvar _c;\n$RefreshReg$(_c, \"EpisodeCard\");", "map": {"version": 3, "names": ["React", "formatDate", "formatDuration", "jsxDEV", "_jsxDEV", "EpisodeCard", "episode", "onSelect", "isActive", "handleClick", "handlePlayClick", "e", "stopPropagation", "truncateDescription", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "className", "onClick", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pubDate", "duration", "description", "disabled", "audioUrl", "width", "height", "viewBox", "fill", "d", "link", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeCard.js"], "sourcesContent": ["import React from 'react';\nimport { formatDate, formatDuration } from '../utils/rssParser';\nimport './EpisodeCard.css';\n\nconst EpisodeCard = ({ episode, onSelect, isActive }) => {\n  const handleClick = () => {\n    onSelect(episode);\n  };\n\n  const handlePlayClick = (e) => {\n    e.stopPropagation();\n    onSelect(episode);\n  };\n\n  // 截取描述文本\n  const truncateDescription = (text, maxLength = 200) => {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  return (\n    <div \n      className={`episode-card ${isActive ? 'active' : ''}`}\n      onClick={handleClick}\n    >\n      <div className=\"episode-card-content\">\n        <div className=\"episode-header\">\n          <h3 className=\"episode-title\">{episode.title}</h3>\n          <div className=\"episode-meta\">\n            <span className=\"episode-date\">\n              {formatDate(episode.pubDate)}\n            </span>\n            {episode.duration && (\n              <span className=\"episode-duration\">\n                {formatDuration(episode.duration)}\n              </span>\n            )}\n          </div>\n        </div>\n        \n        <p className=\"episode-description\">\n          {truncateDescription(episode.description)}\n        </p>\n        \n        <div className=\"episode-actions\">\n          <button \n            className=\"play-button\"\n            onClick={handlePlayClick}\n            disabled={!episode.audioUrl}\n          >\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M8 5v14l11-7z\"/>\n            </svg>\n            播放\n          </button>\n          \n          {episode.link && (\n            <a \n              href={episode.link} \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              className=\"episode-link\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              查看原文\n            </a>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EpisodeCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,cAAc,QAAQ,oBAAoB;AAC/D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EACvD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBF,QAAQ,CAACD,OAAO,CAAC;EACnB,CAAC;EAED,MAAMI,eAAe,GAAIC,CAAC,IAAK;IAC7BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBL,QAAQ,CAACD,OAAO,CAAC;EACnB,CAAC;;EAED;EACA,MAAMO,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,GAAG,KAAK;IACrD,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;IACpB,IAAIA,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EAED,oBACEX,OAAA;IACEc,SAAS,EAAE,gBAAgBV,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;IACtDW,OAAO,EAAEV,WAAY;IAAAW,QAAA,eAErBhB,OAAA;MAAKc,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACnChB,OAAA;QAAKc,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BhB,OAAA;UAAIc,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAEd,OAAO,CAACe;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClDrB,OAAA;UAAKc,SAAS,EAAC,cAAc;UAAAE,QAAA,gBAC3BhB,OAAA;YAAMc,SAAS,EAAC,cAAc;YAAAE,QAAA,EAC3BnB,UAAU,CAACK,OAAO,CAACoB,OAAO;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EACNnB,OAAO,CAACqB,QAAQ,iBACfvB,OAAA;YAAMc,SAAS,EAAC,kBAAkB;YAAAE,QAAA,EAC/BlB,cAAc,CAACI,OAAO,CAACqB,QAAQ;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrB,OAAA;QAAGc,SAAS,EAAC,qBAAqB;QAAAE,QAAA,EAC/BP,mBAAmB,CAACP,OAAO,CAACsB,WAAW;MAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAEJrB,OAAA;QAAKc,SAAS,EAAC,iBAAiB;QAAAE,QAAA,gBAC9BhB,OAAA;UACEc,SAAS,EAAC,aAAa;UACvBC,OAAO,EAAET,eAAgB;UACzBmB,QAAQ,EAAE,CAACvB,OAAO,CAACwB,QAAS;UAAAV,QAAA,gBAE5BhB,OAAA;YAAK2B,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAd,QAAA,eACjEhB,OAAA;cAAM+B,CAAC,EAAC;YAAe;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,gBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERnB,OAAO,CAAC8B,IAAI,iBACXhC,OAAA;UACEiC,IAAI,EAAE/B,OAAO,CAAC8B,IAAK;UACnBE,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBrB,SAAS,EAAC,cAAc;UACxBC,OAAO,EAAGR,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;UAAAQ,QAAA,EACrC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACe,EAAA,GApEInC,WAAW;AAsEjB,eAAeA,WAAW;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
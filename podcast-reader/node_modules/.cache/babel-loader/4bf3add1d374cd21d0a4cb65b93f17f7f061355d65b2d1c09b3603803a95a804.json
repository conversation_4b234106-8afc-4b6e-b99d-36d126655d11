{"ast": null, "code": "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict';\n\nconst base64 = require('base64-js');\nconst ieee754 = require('ieee754');\nconst customInspectSymbol = typeof Symbol === 'function' && typeof Symbol['for'] === 'function' // eslint-disable-line dot-notation\n? Symbol['for']('nodejs.util.inspect.custom') // eslint-disable-line dot-notation\n: null;\nexports.Buffer = Buffer;\nexports.SlowBuffer = SlowBuffer;\nexports.INSPECT_MAX_BYTES = 50;\nconst K_MAX_LENGTH = 0x7fffffff;\nexports.kMaxLength = K_MAX_LENGTH;\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Print warning and recommend using `buffer` v4.x which has an Object\n *               implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * We report that the browser does not support typed arrays if the are not subclassable\n * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`\n * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support\n * for __proto__ and has a buggy typed array implementation.\n */\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport();\nif (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' && typeof console.error === 'function') {\n  console.error('This browser lacks typed array (Uint8Array) support which is required by ' + '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.');\n}\nfunction typedArraySupport() {\n  // Can typed array instances can be augmented?\n  try {\n    const arr = new Uint8Array(1);\n    const proto = {\n      foo: function () {\n        return 42;\n      }\n    };\n    Object.setPrototypeOf(proto, Uint8Array.prototype);\n    Object.setPrototypeOf(arr, proto);\n    return arr.foo() === 42;\n  } catch (e) {\n    return false;\n  }\n}\nObject.defineProperty(Buffer.prototype, 'parent', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined;\n    return this.buffer;\n  }\n});\nObject.defineProperty(Buffer.prototype, 'offset', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined;\n    return this.byteOffset;\n  }\n});\nfunction createBuffer(length) {\n  if (length > K_MAX_LENGTH) {\n    throw new RangeError('The value \"' + length + '\" is invalid for option \"size\"');\n  }\n  // Return an augmented `Uint8Array` instance\n  const buf = new Uint8Array(length);\n  Object.setPrototypeOf(buf, Buffer.prototype);\n  return buf;\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer(arg, encodingOrOffset, length) {\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new TypeError('The \"string\" argument must be of type string. Received type number');\n    }\n    return allocUnsafe(arg);\n  }\n  return from(arg, encodingOrOffset, length);\n}\nBuffer.poolSize = 8192; // not used by this implementation\n\nfunction from(value, encodingOrOffset, length) {\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset);\n  }\n  if (ArrayBuffer.isView(value)) {\n    return fromArrayView(value);\n  }\n  if (value == null) {\n    throw new TypeError('The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' + 'or Array-like Object. Received type ' + typeof value);\n  }\n  if (isInstance(value, ArrayBuffer) || value && isInstance(value.buffer, ArrayBuffer)) {\n    return fromArrayBuffer(value, encodingOrOffset, length);\n  }\n  if (typeof SharedArrayBuffer !== 'undefined' && (isInstance(value, SharedArrayBuffer) || value && isInstance(value.buffer, SharedArrayBuffer))) {\n    return fromArrayBuffer(value, encodingOrOffset, length);\n  }\n  if (typeof value === 'number') {\n    throw new TypeError('The \"value\" argument must not be of type number. Received type number');\n  }\n  const valueOf = value.valueOf && value.valueOf();\n  if (valueOf != null && valueOf !== value) {\n    return Buffer.from(valueOf, encodingOrOffset, length);\n  }\n  const b = fromObject(value);\n  if (b) return b;\n  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null && typeof value[Symbol.toPrimitive] === 'function') {\n    return Buffer.from(value[Symbol.toPrimitive]('string'), encodingOrOffset, length);\n  }\n  throw new TypeError('The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' + 'or Array-like Object. Received type ' + typeof value);\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(value, encodingOrOffset, length);\n};\n\n// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:\n// https://github.com/feross/buffer/pull/148\nObject.setPrototypeOf(Buffer.prototype, Uint8Array.prototype);\nObject.setPrototypeOf(Buffer, Uint8Array);\nfunction assertSize(size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be of type number');\n  } else if (size < 0) {\n    throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"');\n  }\n}\nfunction alloc(size, fill, encoding) {\n  assertSize(size);\n  if (size <= 0) {\n    return createBuffer(size);\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpreted as a start offset.\n    return typeof encoding === 'string' ? createBuffer(size).fill(fill, encoding) : createBuffer(size).fill(fill);\n  }\n  return createBuffer(size);\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(size, fill, encoding);\n};\nfunction allocUnsafe(size) {\n  assertSize(size);\n  return createBuffer(size < 0 ? 0 : checked(size) | 0);\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(size);\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(size);\n};\nfunction fromString(string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('Unknown encoding: ' + encoding);\n  }\n  const length = byteLength(string, encoding) | 0;\n  let buf = createBuffer(length);\n  const actual = buf.write(string, encoding);\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual);\n  }\n  return buf;\n}\nfunction fromArrayLike(array) {\n  const length = array.length < 0 ? 0 : checked(array.length) | 0;\n  const buf = createBuffer(length);\n  for (let i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255;\n  }\n  return buf;\n}\nfunction fromArrayView(arrayView) {\n  if (isInstance(arrayView, Uint8Array)) {\n    const copy = new Uint8Array(arrayView);\n    return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength);\n  }\n  return fromArrayLike(arrayView);\n}\nfunction fromArrayBuffer(array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\"offset\" is outside of buffer bounds');\n  }\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\"length\" is outside of buffer bounds');\n  }\n  let buf;\n  if (byteOffset === undefined && length === undefined) {\n    buf = new Uint8Array(array);\n  } else if (length === undefined) {\n    buf = new Uint8Array(array, byteOffset);\n  } else {\n    buf = new Uint8Array(array, byteOffset, length);\n  }\n\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(buf, Buffer.prototype);\n  return buf;\n}\nfunction fromObject(obj) {\n  if (Buffer.isBuffer(obj)) {\n    const len = checked(obj.length) | 0;\n    const buf = createBuffer(len);\n    if (buf.length === 0) {\n      return buf;\n    }\n    obj.copy(buf, 0, 0, len);\n    return buf;\n  }\n  if (obj.length !== undefined) {\n    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {\n      return createBuffer(0);\n    }\n    return fromArrayLike(obj);\n  }\n  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n    return fromArrayLike(obj.data);\n  }\n}\nfunction checked(length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes');\n  }\n  return length | 0;\n}\nfunction SlowBuffer(length) {\n  if (+length != length) {\n    // eslint-disable-line eqeqeq\n    length = 0;\n  }\n  return Buffer.alloc(+length);\n}\nBuffer.isBuffer = function isBuffer(b) {\n  return b != null && b._isBuffer === true && b !== Buffer.prototype; // so Buffer.isBuffer(Buffer.prototype) will be false\n};\nBuffer.compare = function compare(a, b) {\n  if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength);\n  if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength);\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array');\n  }\n  if (a === b) return 0;\n  let x = a.length;\n  let y = b.length;\n  for (let i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\nBuffer.isEncoding = function isEncoding(encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true;\n    default:\n      return false;\n  }\n};\nBuffer.concat = function concat(list, length) {\n  if (!Array.isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n  if (list.length === 0) {\n    return Buffer.alloc(0);\n  }\n  let i;\n  if (length === undefined) {\n    length = 0;\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n  const buffer = Buffer.allocUnsafe(length);\n  let pos = 0;\n  for (i = 0; i < list.length; ++i) {\n    let buf = list[i];\n    if (isInstance(buf, Uint8Array)) {\n      if (pos + buf.length > buffer.length) {\n        if (!Buffer.isBuffer(buf)) buf = Buffer.from(buf);\n        buf.copy(buffer, pos);\n      } else {\n        Uint8Array.prototype.set.call(buffer, buf, pos);\n      }\n    } else if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    } else {\n      buf.copy(buffer, pos);\n    }\n    pos += buf.length;\n  }\n  return buffer;\n};\nfunction byteLength(string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n  if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {\n    return string.byteLength;\n  }\n  if (typeof string !== 'string') {\n    throw new TypeError('The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. ' + 'Received type ' + typeof string);\n  }\n  const len = string.length;\n  const mustMatch = arguments.length > 2 && arguments[2] === true;\n  if (!mustMatch && len === 0) return 0;\n\n  // Use a for loop to avoid recursion\n  let loweredCase = false;\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len;\n      case 'utf8':\n      case 'utf-8':\n        return utf8ToBytes(string).length;\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2;\n      case 'hex':\n        return len >>> 1;\n      case 'base64':\n        return base64ToBytes(string).length;\n      default:\n        if (loweredCase) {\n          return mustMatch ? -1 : utf8ToBytes(string).length; // assume utf8\n        }\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\nBuffer.byteLength = byteLength;\nfunction slowToString(encoding, start, end) {\n  let loweredCase = false;\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0;\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return '';\n  }\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n  if (end <= 0) {\n    return '';\n  }\n\n  // Force coercion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0;\n  start >>>= 0;\n  if (end <= start) {\n    return '';\n  }\n  if (!encoding) encoding = 'utf8';\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end);\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end);\n      case 'ascii':\n        return asciiSlice(this, start, end);\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end);\n      case 'base64':\n        return base64Slice(this, start, end);\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end);\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\n// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)\n// to detect a Buffer instance. It's not possible to use `instanceof Buffer`\n// reliably in a browserify context because there could be multiple different\n// copies of the 'buffer' package in use. This method works even for Buffer\n// instances that were created from another copy of the `buffer` package.\n// See: https://github.com/feross/buffer/issues/154\nBuffer.prototype._isBuffer = true;\nfunction swap(b, n, m) {\n  const i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\nBuffer.prototype.swap16 = function swap16() {\n  const len = this.length;\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits');\n  }\n  for (let i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n  return this;\n};\nBuffer.prototype.swap32 = function swap32() {\n  const len = this.length;\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits');\n  }\n  for (let i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n  return this;\n};\nBuffer.prototype.swap64 = function swap64() {\n  const len = this.length;\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits');\n  }\n  for (let i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n  return this;\n};\nBuffer.prototype.toString = function toString() {\n  const length = this.length;\n  if (length === 0) return '';\n  if (arguments.length === 0) return utf8Slice(this, 0, length);\n  return slowToString.apply(this, arguments);\n};\nBuffer.prototype.toLocaleString = Buffer.prototype.toString;\nBuffer.prototype.equals = function equals(b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer');\n  if (this === b) return true;\n  return Buffer.compare(this, b) === 0;\n};\nBuffer.prototype.inspect = function inspect() {\n  let str = '';\n  const max = exports.INSPECT_MAX_BYTES;\n  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim();\n  if (this.length > max) str += ' ... ';\n  return '<Buffer ' + str + '>';\n};\nif (customInspectSymbol) {\n  Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect;\n}\nBuffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {\n  if (isInstance(target, Uint8Array)) {\n    target = Buffer.from(target, target.offset, target.byteLength);\n  }\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('The \"target\" argument must be one of type Buffer or Uint8Array. ' + 'Received type ' + typeof target);\n  }\n  if (start === undefined) {\n    start = 0;\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index');\n  }\n  if (thisStart >= thisEnd && start >= end) {\n    return 0;\n  }\n  if (thisStart >= thisEnd) {\n    return -1;\n  }\n  if (start >= end) {\n    return 1;\n  }\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n  if (this === target) return 0;\n  let x = thisEnd - thisStart;\n  let y = end - start;\n  const len = Math.min(x, y);\n  const thisCopy = this.slice(thisStart, thisEnd);\n  const targetCopy = target.slice(start, end);\n  for (let i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break;\n    }\n  }\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1;\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n  byteOffset = +byteOffset; // Coerce to Number.\n  if (numberIsNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : buffer.length - 1;\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1;else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;else return -1;\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1;\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir);\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n    if (typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);\n      }\n    }\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);\n  }\n  throw new TypeError('val must be string, number or Buffer');\n}\nfunction arrayIndexOf(arr, val, byteOffset, encoding, dir) {\n  let indexSize = 1;\n  let arrLength = arr.length;\n  let valLength = val.length;\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n    if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1;\n      }\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n  function read(buf, i) {\n    if (indexSize === 1) {\n      return buf[i];\n    } else {\n      return buf.readUInt16BE(i * indexSize);\n    }\n  }\n  let i;\n  if (dir) {\n    let foundIndex = -1;\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n    for (i = byteOffset; i >= 0; i--) {\n      let found = true;\n      for (let j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break;\n        }\n      }\n      if (found) return i;\n    }\n  }\n  return -1;\n}\nBuffer.prototype.includes = function includes(val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1;\n};\nBuffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true);\n};\nBuffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false);\n};\nfunction hexWrite(buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  const remaining = buf.length - offset;\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n    if (length > remaining) {\n      length = remaining;\n    }\n  }\n  const strLen = string.length;\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n  let i;\n  for (i = 0; i < length; ++i) {\n    const parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (numberIsNaN(parsed)) return i;\n    buf[offset + i] = parsed;\n  }\n  return i;\n}\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\nfunction asciiWrite(buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length);\n}\nfunction base64Write(buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length);\n}\nfunction ucs2Write(buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);\n}\nBuffer.prototype.write = function write(string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0;\n    // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0;\n    // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset >>> 0;\n    if (isFinite(length)) {\n      length = length >>> 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    }\n  } else {\n    throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');\n  }\n  const remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n  if (!encoding) encoding = 'utf8';\n  let loweredCase = false;\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length);\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length);\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return asciiWrite(this, string, offset, length);\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length);\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length);\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\nBuffer.prototype.toJSON = function toJSON() {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  };\n};\nfunction base64Slice(buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf);\n  } else {\n    return base64.fromByteArray(buf.slice(start, end));\n  }\n}\nfunction utf8Slice(buf, start, end) {\n  end = Math.min(buf.length, end);\n  const res = [];\n  let i = start;\n  while (i < end) {\n    const firstByte = buf[i];\n    let codePoint = null;\n    let bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;\n    if (i + bytesPerSequence <= end) {\n      let secondByte, thirdByte, fourthByte, tempCodePoint;\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n          break;\n        case 2:\n          secondByte = buf[i + 1];\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n          break;\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n          break;\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n      }\n    }\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n  return decodeCodePointsArray(res);\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nconst MAX_ARGUMENTS_LENGTH = 0x1000;\nfunction decodeCodePointsArray(codePoints) {\n  const len = codePoints.length;\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints); // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  let res = '';\n  let i = 0;\n  while (i < len) {\n    res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));\n  }\n  return res;\n}\nfunction asciiSlice(buf, start, end) {\n  let ret = '';\n  end = Math.min(buf.length, end);\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n  return ret;\n}\nfunction latin1Slice(buf, start, end) {\n  let ret = '';\n  end = Math.min(buf.length, end);\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n  return ret;\n}\nfunction hexSlice(buf, start, end) {\n  const len = buf.length;\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n  let out = '';\n  for (let i = start; i < end; ++i) {\n    out += hexSliceLookupTable[buf[i]];\n  }\n  return out;\n}\nfunction utf16leSlice(buf, start, end) {\n  const bytes = buf.slice(start, end);\n  let res = '';\n  // If bytes.length is odd, the last 8 bits must be ignored (same as node.js)\n  for (let i = 0; i < bytes.length - 1; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n  return res;\n}\nBuffer.prototype.slice = function slice(start, end) {\n  const len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n  if (end < start) end = start;\n  const newBuf = this.subarray(start, end);\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(newBuf, Buffer.prototype);\n  return newBuf;\n};\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset(offset, ext, length) {\n  if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');\n}\nBuffer.prototype.readUintLE = Buffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {\n  offset = offset >>> 0;\n  byteLength = byteLength >>> 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  let val = this[offset];\n  let mul = 1;\n  let i = 0;\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n  return val;\n};\nBuffer.prototype.readUintBE = Buffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {\n  offset = offset >>> 0;\n  byteLength = byteLength >>> 0;\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n  let val = this[offset + --byteLength];\n  let mul = 1;\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n  return val;\n};\nBuffer.prototype.readUint8 = Buffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset];\n};\nBuffer.prototype.readUint16LE = Buffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | this[offset + 1] << 8;\n};\nBuffer.prototype.readUint16BE = Buffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] << 8 | this[offset + 1];\n};\nBuffer.prototype.readUint32LE = Buffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;\n};\nBuffer.prototype.readUint32BE = Buffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);\n};\nBuffer.prototype.readBigUInt64LE = defineBigIntMethod(function readBigUInt64LE(offset) {\n  offset = offset >>> 0;\n  validateNumber(offset, 'offset');\n  const first = this[offset];\n  const last = this[offset + 7];\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8);\n  }\n  const lo = first + this[++offset] * 2 ** 8 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 24;\n  const hi = this[++offset] + this[++offset] * 2 ** 8 + this[++offset] * 2 ** 16 + last * 2 ** 24;\n  return BigInt(lo) + (BigInt(hi) << BigInt(32));\n});\nBuffer.prototype.readBigUInt64BE = defineBigIntMethod(function readBigUInt64BE(offset) {\n  offset = offset >>> 0;\n  validateNumber(offset, 'offset');\n  const first = this[offset];\n  const last = this[offset + 7];\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8);\n  }\n  const hi = first * 2 ** 24 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 8 + this[++offset];\n  const lo = this[++offset] * 2 ** 24 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 8 + last;\n  return (BigInt(hi) << BigInt(32)) + BigInt(lo);\n});\nBuffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {\n  offset = offset >>> 0;\n  byteLength = byteLength >>> 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  let val = this[offset];\n  let mul = 1;\n  let i = 0;\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\nBuffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {\n  offset = offset >>> 0;\n  byteLength = byteLength >>> 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  let i = byteLength;\n  let mul = 1;\n  let val = this[offset + --i];\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\nBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return this[offset];\n  return (0xff - this[offset] + 1) * -1;\n};\nBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  const val = this[offset] | this[offset + 1] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\nBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  const val = this[offset + 1] | this[offset] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\nBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;\n};\nBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];\n};\nBuffer.prototype.readBigInt64LE = defineBigIntMethod(function readBigInt64LE(offset) {\n  offset = offset >>> 0;\n  validateNumber(offset, 'offset');\n  const first = this[offset];\n  const last = this[offset + 7];\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8);\n  }\n  const val = this[offset + 4] + this[offset + 5] * 2 ** 8 + this[offset + 6] * 2 ** 16 + (last << 24); // Overflow\n\n  return (BigInt(val) << BigInt(32)) + BigInt(first + this[++offset] * 2 ** 8 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 24);\n});\nBuffer.prototype.readBigInt64BE = defineBigIntMethod(function readBigInt64BE(offset) {\n  offset = offset >>> 0;\n  validateNumber(offset, 'offset');\n  const first = this[offset];\n  const last = this[offset + 7];\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8);\n  }\n  const val = (first << 24) +\n  // Overflow\n  this[++offset] * 2 ** 16 + this[++offset] * 2 ** 8 + this[++offset];\n  return (BigInt(val) << BigInt(32)) + BigInt(this[++offset] * 2 ** 24 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 8 + last);\n});\nBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, true, 23, 4);\n};\nBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, false, 23, 4);\n};\nBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, true, 52, 8);\n};\nBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n  offset = offset >>> 0;\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, false, 52, 8);\n};\nfunction checkInt(buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance');\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds');\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n}\nBuffer.prototype.writeUintLE = Buffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  byteLength = byteLength >>> 0;\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n  let mul = 1;\n  let i = 0;\n  this[offset] = value & 0xFF;\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n  return offset + byteLength;\n};\nBuffer.prototype.writeUintBE = Buffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  byteLength = byteLength >>> 0;\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n  let i = byteLength - 1;\n  let mul = 1;\n  this[offset + i] = value & 0xFF;\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n  return offset + byteLength;\n};\nBuffer.prototype.writeUint8 = Buffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\nBuffer.prototype.writeUint16LE = Buffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n  this[offset] = value & 0xff;\n  this[offset + 1] = value >>> 8;\n  return offset + 2;\n};\nBuffer.prototype.writeUint16BE = Buffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n  this[offset] = value >>> 8;\n  this[offset + 1] = value & 0xff;\n  return offset + 2;\n};\nBuffer.prototype.writeUint32LE = Buffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n  this[offset + 3] = value >>> 24;\n  this[offset + 2] = value >>> 16;\n  this[offset + 1] = value >>> 8;\n  this[offset] = value & 0xff;\n  return offset + 4;\n};\nBuffer.prototype.writeUint32BE = Buffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n  this[offset] = value >>> 24;\n  this[offset + 1] = value >>> 16;\n  this[offset + 2] = value >>> 8;\n  this[offset + 3] = value & 0xff;\n  return offset + 4;\n};\nfunction wrtBigUInt64LE(buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7);\n  let lo = Number(value & BigInt(0xffffffff));\n  buf[offset++] = lo;\n  lo = lo >> 8;\n  buf[offset++] = lo;\n  lo = lo >> 8;\n  buf[offset++] = lo;\n  lo = lo >> 8;\n  buf[offset++] = lo;\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff));\n  buf[offset++] = hi;\n  hi = hi >> 8;\n  buf[offset++] = hi;\n  hi = hi >> 8;\n  buf[offset++] = hi;\n  hi = hi >> 8;\n  buf[offset++] = hi;\n  return offset;\n}\nfunction wrtBigUInt64BE(buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7);\n  let lo = Number(value & BigInt(0xffffffff));\n  buf[offset + 7] = lo;\n  lo = lo >> 8;\n  buf[offset + 6] = lo;\n  lo = lo >> 8;\n  buf[offset + 5] = lo;\n  lo = lo >> 8;\n  buf[offset + 4] = lo;\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff));\n  buf[offset + 3] = hi;\n  hi = hi >> 8;\n  buf[offset + 2] = hi;\n  hi = hi >> 8;\n  buf[offset + 1] = hi;\n  hi = hi >> 8;\n  buf[offset] = hi;\n  return offset + 8;\n}\nBuffer.prototype.writeBigUInt64LE = defineBigIntMethod(function writeBigUInt64LE(value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'));\n});\nBuffer.prototype.writeBigUInt64BE = defineBigIntMethod(function writeBigUInt64BE(value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'));\n});\nBuffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) {\n    const limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n  let i = 0;\n  let mul = 1;\n  let sub = 0;\n  this[offset] = value & 0xFF;\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n  return offset + byteLength;\n};\nBuffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) {\n    const limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n  let i = byteLength - 1;\n  let mul = 1;\n  let sub = 0;\n  this[offset + i] = value & 0xFF;\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n  return offset + byteLength;\n};\nBuffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\nBuffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n  this[offset] = value & 0xff;\n  this[offset + 1] = value >>> 8;\n  return offset + 2;\n};\nBuffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n  this[offset] = value >>> 8;\n  this[offset + 1] = value & 0xff;\n  return offset + 2;\n};\nBuffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  this[offset] = value & 0xff;\n  this[offset + 1] = value >>> 8;\n  this[offset + 2] = value >>> 16;\n  this[offset + 3] = value >>> 24;\n  return offset + 4;\n};\nBuffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n  this[offset] = value >>> 24;\n  this[offset + 1] = value >>> 16;\n  this[offset + 2] = value >>> 8;\n  this[offset + 3] = value & 0xff;\n  return offset + 4;\n};\nBuffer.prototype.writeBigInt64LE = defineBigIntMethod(function writeBigInt64LE(value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'));\n});\nBuffer.prototype.writeBigInt64BE = defineBigIntMethod(function writeBigInt64BE(value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'));\n});\nfunction checkIEEE754(buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n  if (offset < 0) throw new RangeError('Index out of range');\n}\nfunction writeFloat(buf, value, offset, littleEndian, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38);\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4;\n}\nBuffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert);\n};\nBuffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert);\n};\nfunction writeDouble(buf, value, offset, littleEndian, noAssert) {\n  value = +value;\n  offset = offset >>> 0;\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308);\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8;\n}\nBuffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert);\n};\nBuffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert);\n};\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer');\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start;\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0;\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('Index out of range');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds');\n\n  // Are we oob?\n  if (end > this.length) end = this.length;\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n  const len = end - start;\n  if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {\n    // Use built-in when available, missing from IE11\n    this.copyWithin(targetStart, start, end);\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, end), targetStart);\n  }\n  return len;\n};\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill(val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string');\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding);\n    }\n    if (val.length === 1) {\n      const code = val.charCodeAt(0);\n      if (encoding === 'utf8' && code < 128 || encoding === 'latin1') {\n        // Fast path: If `val` fits into a single byte, use that numeric value.\n        val = code;\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  } else if (typeof val === 'boolean') {\n    val = Number(val);\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n  if (end <= start) {\n    return this;\n  }\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  let i;\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    const bytes = Buffer.isBuffer(val) ? val : Buffer.from(val, encoding);\n    const len = bytes.length;\n    if (len === 0) {\n      throw new TypeError('The value \"' + val + '\" is invalid for argument \"value\"');\n    }\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n  return this;\n};\n\n// CUSTOM ERRORS\n// =============\n\n// Simplified versions from Node, changed for Buffer-only usage\nconst errors = {};\nfunction E(sym, getMessage, Base) {\n  errors[sym] = class NodeError extends Base {\n    constructor() {\n      super();\n      Object.defineProperty(this, 'message', {\n        value: getMessage.apply(this, arguments),\n        writable: true,\n        configurable: true\n      });\n\n      // Add the error code to the name to include it in the stack trace.\n      this.name = `${this.name} [${sym}]`;\n      // Access the stack to generate the error message including the error code\n      // from the name.\n      this.stack; // eslint-disable-line no-unused-expressions\n      // Reset the name to the actual name.\n      delete this.name;\n    }\n    get code() {\n      return sym;\n    }\n    set code(value) {\n      Object.defineProperty(this, 'code', {\n        configurable: true,\n        enumerable: true,\n        value,\n        writable: true\n      });\n    }\n    toString() {\n      return `${this.name} [${sym}]: ${this.message}`;\n    }\n  };\n}\nE('ERR_BUFFER_OUT_OF_BOUNDS', function (name) {\n  if (name) {\n    return `${name} is outside of buffer bounds`;\n  }\n  return 'Attempt to access memory outside buffer bounds';\n}, RangeError);\nE('ERR_INVALID_ARG_TYPE', function (name, actual) {\n  return `The \"${name}\" argument must be of type number. Received type ${typeof actual}`;\n}, TypeError);\nE('ERR_OUT_OF_RANGE', function (str, range, input) {\n  let msg = `The value of \"${str}\" is out of range.`;\n  let received = input;\n  if (Number.isInteger(input) && Math.abs(input) > 2 ** 32) {\n    received = addNumericalSeparator(String(input));\n  } else if (typeof input === 'bigint') {\n    received = String(input);\n    if (input > BigInt(2) ** BigInt(32) || input < -(BigInt(2) ** BigInt(32))) {\n      received = addNumericalSeparator(received);\n    }\n    received += 'n';\n  }\n  msg += ` It must be ${range}. Received ${received}`;\n  return msg;\n}, RangeError);\nfunction addNumericalSeparator(val) {\n  let res = '';\n  let i = val.length;\n  const start = val[0] === '-' ? 1 : 0;\n  for (; i >= start + 4; i -= 3) {\n    res = `_${val.slice(i - 3, i)}${res}`;\n  }\n  return `${val.slice(0, i)}${res}`;\n}\n\n// CHECK FUNCTIONS\n// ===============\n\nfunction checkBounds(buf, offset, byteLength) {\n  validateNumber(offset, 'offset');\n  if (buf[offset] === undefined || buf[offset + byteLength] === undefined) {\n    boundsError(offset, buf.length - (byteLength + 1));\n  }\n}\nfunction checkIntBI(value, min, max, buf, offset, byteLength) {\n  if (value > max || value < min) {\n    const n = typeof min === 'bigint' ? 'n' : '';\n    let range;\n    if (byteLength > 3) {\n      if (min === 0 || min === BigInt(0)) {\n        range = `>= 0${n} and < 2${n} ** ${(byteLength + 1) * 8}${n}`;\n      } else {\n        range = `>= -(2${n} ** ${(byteLength + 1) * 8 - 1}${n}) and < 2 ** ` + `${(byteLength + 1) * 8 - 1}${n}`;\n      }\n    } else {\n      range = `>= ${min}${n} and <= ${max}${n}`;\n    }\n    throw new errors.ERR_OUT_OF_RANGE('value', range, value);\n  }\n  checkBounds(buf, offset, byteLength);\n}\nfunction validateNumber(value, name) {\n  if (typeof value !== 'number') {\n    throw new errors.ERR_INVALID_ARG_TYPE(name, 'number', value);\n  }\n}\nfunction boundsError(value, length, type) {\n  if (Math.floor(value) !== value) {\n    validateNumber(value, type);\n    throw new errors.ERR_OUT_OF_RANGE(type || 'offset', 'an integer', value);\n  }\n  if (length < 0) {\n    throw new errors.ERR_BUFFER_OUT_OF_BOUNDS();\n  }\n  throw new errors.ERR_OUT_OF_RANGE(type || 'offset', `>= ${type ? 1 : 0} and <= ${length}`, value);\n}\n\n// HELPER FUNCTIONS\n// ================\n\nconst INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g;\nfunction base64clean(str) {\n  // Node takes equal signs as end of the Base64 encoding\n  str = str.split('=')[0];\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = str.trim().replace(INVALID_BASE64_RE, '');\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return '';\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n  return str;\n}\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  let codePoint;\n  const length = string.length;\n  let leadSurrogate = null;\n  const bytes = [];\n  for (let i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i);\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        }\n\n        // valid lead\n        leadSurrogate = codePoint;\n        continue;\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n    leadSurrogate = null;\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n  return bytes;\n}\nfunction asciiToBytes(str) {\n  const byteArray = [];\n  for (let i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n  return byteArray;\n}\nfunction utf16leToBytes(str, units) {\n  let c, hi, lo;\n  const byteArray = [];\n  for (let i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break;\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n  return byteArray;\n}\nfunction base64ToBytes(str) {\n  return base64.toByteArray(base64clean(str));\n}\nfunction blitBuffer(src, dst, offset, length) {\n  let i;\n  for (i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n  return i;\n}\n\n// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass\n// the `instanceof` check but they should be treated as of that type.\n// See: https://github.com/feross/buffer/issues/166\nfunction isInstance(obj, type) {\n  return obj instanceof type || obj != null && obj.constructor != null && obj.constructor.name != null && obj.constructor.name === type.name;\n}\nfunction numberIsNaN(obj) {\n  // For IE11 support\n  return obj !== obj; // eslint-disable-line no-self-compare\n}\n\n// Create lookup table for `toString('hex')`\n// See: https://github.com/feross/buffer/issues/219\nconst hexSliceLookupTable = function () {\n  const alphabet = '0123456789abcdef';\n  const table = new Array(256);\n  for (let i = 0; i < 16; ++i) {\n    const i16 = i * 16;\n    for (let j = 0; j < 16; ++j) {\n      table[i16 + j] = alphabet[i] + alphabet[j];\n    }\n  }\n  return table;\n}();\n\n// Return not function with Error if BigInt not supported\nfunction defineBigIntMethod(fn) {\n  return typeof BigInt === 'undefined' ? BufferBigIntNotDefined : fn;\n}\nfunction BufferBigIntNotDefined() {\n  throw new Error('BigInt not supported');\n}", "map": {"version": 3, "names": ["base64", "require", "ieee754", "customInspectSymbol", "Symbol", "exports", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "INSPECT_MAX_BYTES", "K_MAX_LENGTH", "kMaxLength", "TYPED_ARRAY_SUPPORT", "typedArraySupport", "console", "error", "arr", "Uint8Array", "proto", "foo", "Object", "setPrototypeOf", "prototype", "e", "defineProperty", "enumerable", "get", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "buffer", "byteOffset", "createBuffer", "length", "RangeError", "buf", "arg", "encodingOrOffset", "TypeError", "allocUnsafe", "from", "poolSize", "value", "fromString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "fromArrayView", "isInstance", "fromArrayBuffer", "SharedArrayBuffer", "valueOf", "b", "fromObject", "toPrimitive", "assertSize", "size", "alloc", "fill", "encoding", "checked", "allocUnsafeSlow", "string", "isEncoding", "byteLength", "actual", "write", "slice", "fromArrayLike", "array", "i", "arrayView", "copy", "obj", "len", "numberIsNaN", "type", "Array", "isArray", "data", "toString", "_isBuffer", "compare", "a", "offset", "x", "y", "Math", "min", "String", "toLowerCase", "concat", "list", "pos", "set", "call", "mustMatch", "arguments", "loweredCase", "utf8ToBytes", "base64ToBytes", "slowToString", "start", "end", "hexSlice", "utf8Slice", "asciiSlice", "latin1Slice", "base64Slice", "utf16leSlice", "swap", "n", "m", "swap16", "swap32", "swap64", "apply", "toLocaleString", "equals", "inspect", "str", "max", "replace", "trim", "target", "thisStart", "thisEnd", "thisCopy", "targetCopy", "bidirectionalIndexOf", "val", "dir", "arrayIndexOf", "indexOf", "lastIndexOf", "indexSize", "arr<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "read", "readUInt16BE", "foundIndex", "found", "j", "includes", "hexWrite", "Number", "remaining", "strLen", "parsed", "parseInt", "substr", "utf8Write", "blit<PERSON><PERSON>er", "asciiWrite", "asciiToBytes", "base64Write", "ucs2Write", "utf16leToBytes", "isFinite", "Error", "toJSON", "_arr", "fromByteArray", "res", "firstByte", "codePoint", "bytesPerSequence", "secondByte", "thirdByte", "fourthByte", "tempCodePoint", "push", "decodeCodePointsArray", "MAX_ARGUMENTS_LENGTH", "codePoints", "fromCharCode", "ret", "out", "hexSliceLookupTable", "bytes", "newBuf", "subarray", "checkOffset", "ext", "readUintLE", "readUIntLE", "noAssert", "mul", "readUintBE", "readUIntBE", "readUint8", "readUInt8", "readUint16LE", "readUInt16LE", "readUint16BE", "readUint32LE", "readUInt32LE", "readUint32BE", "readUInt32BE", "readBigUInt64LE", "defineBigIntMethod", "validateNumber", "first", "last", "boundsError", "lo", "hi", "BigInt", "readBigUInt64BE", "readIntLE", "pow", "readIntBE", "readInt8", "readInt16LE", "readInt16BE", "readInt32LE", "readInt32BE", "readBigInt64LE", "readBigInt64BE", "readFloatLE", "readFloatBE", "readDoubleLE", "readDoubleBE", "checkInt", "writeUintLE", "writeUIntLE", "maxBytes", "writeUintBE", "writeUIntBE", "writeUint8", "writeUInt8", "writeUint16LE", "writeUInt16LE", "writeUint16BE", "writeUInt16BE", "writeUint32LE", "writeUInt32LE", "writeUint32BE", "writeUInt32BE", "wrtBigUInt64LE", "checkIntBI", "wrtBigUInt64BE", "writeBigUInt64LE", "writeBigUInt64BE", "writeIntLE", "limit", "sub", "writeIntBE", "writeInt8", "writeInt16LE", "writeInt16BE", "writeInt32LE", "writeInt32BE", "writeBigInt64LE", "writeBigInt64BE", "checkIEEE754", "writeFloat", "littleEndian", "writeFloatLE", "writeFloatBE", "writeDouble", "writeDoubleLE", "writeDoubleBE", "targetStart", "copyWithin", "code", "charCodeAt", "errors", "E", "sym", "getMessage", "Base", "NodeError", "constructor", "writable", "configurable", "name", "stack", "message", "range", "input", "msg", "received", "isInteger", "abs", "addNumericalSeparator", "checkBounds", "ERR_OUT_OF_RANGE", "ERR_INVALID_ARG_TYPE", "floor", "ERR_BUFFER_OUT_OF_BOUNDS", "INVALID_BASE64_RE", "base64clean", "split", "units", "Infinity", "leadSurrogate", "byteArray", "c", "toByteArray", "src", "dst", "alphabet", "table", "i16", "fn", "BufferBigIntNotDefined"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/buffer/index.js"], "sourcesContent": ["/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nconst base64 = require('base64-js')\nconst ieee754 = require('ieee754')\nconst customInspectSymbol =\n  (typeof Symbol === 'function' && typeof Symbol['for'] === 'function') // eslint-disable-line dot-notation\n    ? Symbol['for']('nodejs.util.inspect.custom') // eslint-disable-line dot-notation\n    : null\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\nconst K_MAX_LENGTH = 0x7fffffff\nexports.kMaxLength = K_MAX_LENGTH\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Print warning and recommend using `buffer` v4.x which has an Object\n *               implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * We report that the browser does not support typed arrays if the are not subclassable\n * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`\n * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support\n * for __proto__ and has a buggy typed array implementation.\n */\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport()\n\nif (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&\n    typeof console.error === 'function') {\n  console.error(\n    'This browser lacks typed array (Uint8Array) support which is required by ' +\n    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'\n  )\n}\n\nfunction typedArraySupport () {\n  // Can typed array instances can be augmented?\n  try {\n    const arr = new Uint8Array(1)\n    const proto = { foo: function () { return 42 } }\n    Object.setPrototypeOf(proto, Uint8Array.prototype)\n    Object.setPrototypeOf(arr, proto)\n    return arr.foo() === 42\n  } catch (e) {\n    return false\n  }\n}\n\nObject.defineProperty(Buffer.prototype, 'parent', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.buffer\n  }\n})\n\nObject.defineProperty(Buffer.prototype, 'offset', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.byteOffset\n  }\n})\n\nfunction createBuffer (length) {\n  if (length > K_MAX_LENGTH) {\n    throw new RangeError('The value \"' + length + '\" is invalid for option \"size\"')\n  }\n  // Return an augmented `Uint8Array` instance\n  const buf = new Uint8Array(length)\n  Object.setPrototypeOf(buf, Buffer.prototype)\n  return buf\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new TypeError(\n        'The \"string\" argument must be of type string. Received type number'\n      )\n    }\n    return allocUnsafe(arg)\n  }\n  return from(arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\nfunction from (value, encodingOrOffset, length) {\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  if (ArrayBuffer.isView(value)) {\n    return fromArrayView(value)\n  }\n\n  if (value == null) {\n    throw new TypeError(\n      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n      'or Array-like Object. Received type ' + (typeof value)\n    )\n  }\n\n  if (isInstance(value, ArrayBuffer) ||\n      (value && isInstance(value.buffer, ArrayBuffer))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof SharedArrayBuffer !== 'undefined' &&\n      (isInstance(value, SharedArrayBuffer) ||\n      (value && isInstance(value.buffer, SharedArrayBuffer)))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'number') {\n    throw new TypeError(\n      'The \"value\" argument must not be of type number. Received type number'\n    )\n  }\n\n  const valueOf = value.valueOf && value.valueOf()\n  if (valueOf != null && valueOf !== value) {\n    return Buffer.from(valueOf, encodingOrOffset, length)\n  }\n\n  const b = fromObject(value)\n  if (b) return b\n\n  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&\n      typeof value[Symbol.toPrimitive] === 'function') {\n    return Buffer.from(value[Symbol.toPrimitive]('string'), encodingOrOffset, length)\n  }\n\n  throw new TypeError(\n    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n    'or Array-like Object. Received type ' + (typeof value)\n  )\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(value, encodingOrOffset, length)\n}\n\n// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:\n// https://github.com/feross/buffer/pull/148\nObject.setPrototypeOf(Buffer.prototype, Uint8Array.prototype)\nObject.setPrototypeOf(Buffer, Uint8Array)\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be of type number')\n  } else if (size < 0) {\n    throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"')\n  }\n}\n\nfunction alloc (size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpreted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(size).fill(fill, encoding)\n      : createBuffer(size).fill(fill)\n  }\n  return createBuffer(size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(size, fill, encoding)\n}\n\nfunction allocUnsafe (size) {\n  assertSize(size)\n  return createBuffer(size < 0 ? 0 : checked(size) | 0)\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(size)\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('Unknown encoding: ' + encoding)\n  }\n\n  const length = byteLength(string, encoding) | 0\n  let buf = createBuffer(length)\n\n  const actual = buf.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual)\n  }\n\n  return buf\n}\n\nfunction fromArrayLike (array) {\n  const length = array.length < 0 ? 0 : checked(array.length) | 0\n  const buf = createBuffer(length)\n  for (let i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255\n  }\n  return buf\n}\n\nfunction fromArrayView (arrayView) {\n  if (isInstance(arrayView, Uint8Array)) {\n    const copy = new Uint8Array(arrayView)\n    return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength)\n  }\n  return fromArrayLike(arrayView)\n}\n\nfunction fromArrayBuffer (array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\"offset\" is outside of buffer bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\"length\" is outside of buffer bounds')\n  }\n\n  let buf\n  if (byteOffset === undefined && length === undefined) {\n    buf = new Uint8Array(array)\n  } else if (length === undefined) {\n    buf = new Uint8Array(array, byteOffset)\n  } else {\n    buf = new Uint8Array(array, byteOffset, length)\n  }\n\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(buf, Buffer.prototype)\n\n  return buf\n}\n\nfunction fromObject (obj) {\n  if (Buffer.isBuffer(obj)) {\n    const len = checked(obj.length) | 0\n    const buf = createBuffer(len)\n\n    if (buf.length === 0) {\n      return buf\n    }\n\n    obj.copy(buf, 0, 0, len)\n    return buf\n  }\n\n  if (obj.length !== undefined) {\n    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {\n      return createBuffer(0)\n    }\n    return fromArrayLike(obj)\n  }\n\n  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n    return fromArrayLike(obj.data)\n  }\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return b != null && b._isBuffer === true &&\n    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false\n}\n\nBuffer.compare = function compare (a, b) {\n  if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength)\n  if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength)\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError(\n      'The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array'\n    )\n  }\n\n  if (a === b) return 0\n\n  let x = a.length\n  let y = b.length\n\n  for (let i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!Array.isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  let i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  const buffer = Buffer.allocUnsafe(length)\n  let pos = 0\n  for (i = 0; i < list.length; ++i) {\n    let buf = list[i]\n    if (isInstance(buf, Uint8Array)) {\n      if (pos + buf.length > buffer.length) {\n        if (!Buffer.isBuffer(buf)) buf = Buffer.from(buf)\n        buf.copy(buffer, pos)\n      } else {\n        Uint8Array.prototype.set.call(\n          buffer,\n          buf,\n          pos\n        )\n      }\n    } else if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    } else {\n      buf.copy(buffer, pos)\n    }\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    throw new TypeError(\n      'The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. ' +\n      'Received type ' + typeof string\n    )\n  }\n\n  const len = string.length\n  const mustMatch = (arguments.length > 2 && arguments[2] === true)\n  if (!mustMatch && len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) {\n          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8\n        }\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  let loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coercion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)\n// to detect a Buffer instance. It's not possible to use `instanceof Buffer`\n// reliably in a browserify context because there could be multiple different\n// copies of the 'buffer' package in use. This method works even for Buffer\n// instances that were created from another copy of the `buffer` package.\n// See: https://github.com/feross/buffer/issues/154\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  const i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  const len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (let i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  const len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (let i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  const len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (let i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  const length = this.length\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.toLocaleString = Buffer.prototype.toString\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  let str = ''\n  const max = exports.INSPECT_MAX_BYTES\n  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()\n  if (this.length > max) str += ' ... '\n  return '<Buffer ' + str + '>'\n}\nif (customInspectSymbol) {\n  Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (isInstance(target, Uint8Array)) {\n    target = Buffer.from(target, target.offset, target.byteLength)\n  }\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError(\n      'The \"target\" argument must be one of type Buffer or Uint8Array. ' +\n      'Received type ' + (typeof target)\n    )\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  let x = thisEnd - thisStart\n  let y = end - start\n  const len = Math.min(x, y)\n\n  const thisCopy = this.slice(thisStart, thisEnd)\n  const targetCopy = target.slice(start, end)\n\n  for (let i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset // Coerce to Number.\n  if (numberIsNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  let indexSize = 1\n  let arrLength = arr.length\n  let valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  let i\n  if (dir) {\n    let foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      let found = true\n      for (let j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  const remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  const strLen = string.length\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  let i\n  for (i = 0; i < length; ++i) {\n    const parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (numberIsNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset >>> 0\n    if (isFinite(length)) {\n      length = length >>> 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  const remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return asciiWrite(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  const res = []\n\n  let i = start\n  while (i < end) {\n    const firstByte = buf[i]\n    let codePoint = null\n    let bytesPerSequence = (firstByte > 0xEF)\n      ? 4\n      : (firstByte > 0xDF)\n          ? 3\n          : (firstByte > 0xBF)\n              ? 2\n              : 1\n\n    if (i + bytesPerSequence <= end) {\n      let secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nconst MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  const len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  let res = ''\n  let i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  const len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  let out = ''\n  for (let i = start; i < end; ++i) {\n    out += hexSliceLookupTable[buf[i]]\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  const bytes = buf.slice(start, end)\n  let res = ''\n  // If bytes.length is odd, the last 8 bits must be ignored (same as node.js)\n  for (let i = 0; i < bytes.length - 1; i += 2) {\n    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  const len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  const newBuf = this.subarray(start, end)\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(newBuf, Buffer.prototype)\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUintLE =\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUintBE =\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  let val = this[offset + --byteLength]\n  let mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUint8 =\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUint16LE =\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUint16BE =\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUint32LE =\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUint32BE =\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readBigUInt64LE = defineBigIntMethod(function readBigUInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const lo = first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24\n\n  const hi = this[++offset] +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    last * 2 ** 24\n\n  return BigInt(lo) + (BigInt(hi) << BigInt(32))\n})\n\nBuffer.prototype.readBigUInt64BE = defineBigIntMethod(function readBigUInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const hi = first * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  const lo = this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last\n\n  return (BigInt(hi) << BigInt(32)) + BigInt(lo)\n})\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let i = byteLength\n  let mul = 1\n  let val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readBigInt64LE = defineBigIntMethod(function readBigInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = this[offset + 4] +\n    this[offset + 5] * 2 ** 8 +\n    this[offset + 6] * 2 ** 16 +\n    (last << 24) // Overflow\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24)\n})\n\nBuffer.prototype.readBigInt64BE = defineBigIntMethod(function readBigInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = (first << 24) + // Overflow\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last)\n})\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUintLE =\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let mul = 1\n  let i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUintBE =\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUint8 =\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeUint16LE =\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint16BE =\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint32LE =\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset + 3] = (value >>> 24)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 1] = (value >>> 8)\n  this[offset] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeUint32BE =\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nfunction wrtBigUInt64LE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  return offset\n}\n\nfunction wrtBigUInt64BE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset + 7] = lo\n  lo = lo >> 8\n  buf[offset + 6] = lo\n  lo = lo >> 8\n  buf[offset + 5] = lo\n  lo = lo >> 8\n  buf[offset + 4] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset + 3] = hi\n  hi = hi >> 8\n  buf[offset + 2] = hi\n  hi = hi >> 8\n  buf[offset + 1] = hi\n  hi = hi >> 8\n  buf[offset] = hi\n  return offset + 8\n}\n\nBuffer.prototype.writeBigUInt64LE = defineBigIntMethod(function writeBigUInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeBigUInt64BE = defineBigIntMethod(function writeBigUInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = 0\n  let mul = 1\n  let sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  let sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 3] = (value >>> 24)\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeBigInt64LE = defineBigIntMethod(function writeBigInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nBuffer.prototype.writeBigInt64BE = defineBigIntMethod(function writeBigInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  const len = end - start\n\n  if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {\n    // Use built-in when available, missing from IE11\n    this.copyWithin(targetStart, start, end)\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, end),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n    if (val.length === 1) {\n      const code = val.charCodeAt(0)\n      if ((encoding === 'utf8' && code < 128) ||\n          encoding === 'latin1') {\n        // Fast path: If `val` fits into a single byte, use that numeric value.\n        val = code\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  } else if (typeof val === 'boolean') {\n    val = Number(val)\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  let i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    const bytes = Buffer.isBuffer(val)\n      ? val\n      : Buffer.from(val, encoding)\n    const len = bytes.length\n    if (len === 0) {\n      throw new TypeError('The value \"' + val +\n        '\" is invalid for argument \"value\"')\n    }\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// CUSTOM ERRORS\n// =============\n\n// Simplified versions from Node, changed for Buffer-only usage\nconst errors = {}\nfunction E (sym, getMessage, Base) {\n  errors[sym] = class NodeError extends Base {\n    constructor () {\n      super()\n\n      Object.defineProperty(this, 'message', {\n        value: getMessage.apply(this, arguments),\n        writable: true,\n        configurable: true\n      })\n\n      // Add the error code to the name to include it in the stack trace.\n      this.name = `${this.name} [${sym}]`\n      // Access the stack to generate the error message including the error code\n      // from the name.\n      this.stack // eslint-disable-line no-unused-expressions\n      // Reset the name to the actual name.\n      delete this.name\n    }\n\n    get code () {\n      return sym\n    }\n\n    set code (value) {\n      Object.defineProperty(this, 'code', {\n        configurable: true,\n        enumerable: true,\n        value,\n        writable: true\n      })\n    }\n\n    toString () {\n      return `${this.name} [${sym}]: ${this.message}`\n    }\n  }\n}\n\nE('ERR_BUFFER_OUT_OF_BOUNDS',\n  function (name) {\n    if (name) {\n      return `${name} is outside of buffer bounds`\n    }\n\n    return 'Attempt to access memory outside buffer bounds'\n  }, RangeError)\nE('ERR_INVALID_ARG_TYPE',\n  function (name, actual) {\n    return `The \"${name}\" argument must be of type number. Received type ${typeof actual}`\n  }, TypeError)\nE('ERR_OUT_OF_RANGE',\n  function (str, range, input) {\n    let msg = `The value of \"${str}\" is out of range.`\n    let received = input\n    if (Number.isInteger(input) && Math.abs(input) > 2 ** 32) {\n      received = addNumericalSeparator(String(input))\n    } else if (typeof input === 'bigint') {\n      received = String(input)\n      if (input > BigInt(2) ** BigInt(32) || input < -(BigInt(2) ** BigInt(32))) {\n        received = addNumericalSeparator(received)\n      }\n      received += 'n'\n    }\n    msg += ` It must be ${range}. Received ${received}`\n    return msg\n  }, RangeError)\n\nfunction addNumericalSeparator (val) {\n  let res = ''\n  let i = val.length\n  const start = val[0] === '-' ? 1 : 0\n  for (; i >= start + 4; i -= 3) {\n    res = `_${val.slice(i - 3, i)}${res}`\n  }\n  return `${val.slice(0, i)}${res}`\n}\n\n// CHECK FUNCTIONS\n// ===============\n\nfunction checkBounds (buf, offset, byteLength) {\n  validateNumber(offset, 'offset')\n  if (buf[offset] === undefined || buf[offset + byteLength] === undefined) {\n    boundsError(offset, buf.length - (byteLength + 1))\n  }\n}\n\nfunction checkIntBI (value, min, max, buf, offset, byteLength) {\n  if (value > max || value < min) {\n    const n = typeof min === 'bigint' ? 'n' : ''\n    let range\n    if (byteLength > 3) {\n      if (min === 0 || min === BigInt(0)) {\n        range = `>= 0${n} and < 2${n} ** ${(byteLength + 1) * 8}${n}`\n      } else {\n        range = `>= -(2${n} ** ${(byteLength + 1) * 8 - 1}${n}) and < 2 ** ` +\n                `${(byteLength + 1) * 8 - 1}${n}`\n      }\n    } else {\n      range = `>= ${min}${n} and <= ${max}${n}`\n    }\n    throw new errors.ERR_OUT_OF_RANGE('value', range, value)\n  }\n  checkBounds(buf, offset, byteLength)\n}\n\nfunction validateNumber (value, name) {\n  if (typeof value !== 'number') {\n    throw new errors.ERR_INVALID_ARG_TYPE(name, 'number', value)\n  }\n}\n\nfunction boundsError (value, length, type) {\n  if (Math.floor(value) !== value) {\n    validateNumber(value, type)\n    throw new errors.ERR_OUT_OF_RANGE(type || 'offset', 'an integer', value)\n  }\n\n  if (length < 0) {\n    throw new errors.ERR_BUFFER_OUT_OF_BOUNDS()\n  }\n\n  throw new errors.ERR_OUT_OF_RANGE(type || 'offset',\n                                    `>= ${type ? 1 : 0} and <= ${length}`,\n                                    value)\n}\n\n// HELPER FUNCTIONS\n// ================\n\nconst INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node takes equal signs as end of the Base64 encoding\n  str = str.split('=')[0]\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = str.trim().replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  let codePoint\n  const length = string.length\n  let leadSurrogate = null\n  const bytes = []\n\n  for (let i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  let c, hi, lo\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  let i\n  for (i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\n// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass\n// the `instanceof` check but they should be treated as of that type.\n// See: https://github.com/feross/buffer/issues/166\nfunction isInstance (obj, type) {\n  return obj instanceof type ||\n    (obj != null && obj.constructor != null && obj.constructor.name != null &&\n      obj.constructor.name === type.name)\n}\nfunction numberIsNaN (obj) {\n  // For IE11 support\n  return obj !== obj // eslint-disable-line no-self-compare\n}\n\n// Create lookup table for `toString('hex')`\n// See: https://github.com/feross/buffer/issues/219\nconst hexSliceLookupTable = (function () {\n  const alphabet = '0123456789abcdef'\n  const table = new Array(256)\n  for (let i = 0; i < 16; ++i) {\n    const i16 = i * 16\n    for (let j = 0; j < 16; ++j) {\n      table[i16 + j] = alphabet[i] + alphabet[j]\n    }\n  }\n  return table\n})()\n\n// Return not function with Error if BigInt not supported\nfunction defineBigIntMethod (fn) {\n  return typeof BigInt === 'undefined' ? BufferBigIntNotDefined : fn\n}\n\nfunction BufferBigIntNotDefined () {\n  throw new Error('BigInt not supported')\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,MAAMA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;AACnC,MAAMC,OAAO,GAAGD,OAAO,CAAC,SAAS,CAAC;AAClC,MAAME,mBAAmB,GACtB,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,KAAK,UAAU,CAAE;AAAA,EAClEA,MAAM,CAAC,KAAK,CAAC,CAAC,4BAA4B,CAAC,CAAC;AAAA,EAC5C,IAAI;AAEVC,OAAO,CAACC,MAAM,GAAGA,MAAM;AACvBD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/BF,OAAO,CAACG,iBAAiB,GAAG,EAAE;AAE9B,MAAMC,YAAY,GAAG,UAAU;AAC/BJ,OAAO,CAACK,UAAU,GAAGD,YAAY;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAH,MAAM,CAACK,mBAAmB,GAAGC,iBAAiB,CAAC,CAAC;AAEhD,IAAI,CAACN,MAAM,CAACK,mBAAmB,IAAI,OAAOE,OAAO,KAAK,WAAW,IAC7D,OAAOA,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;EACvCD,OAAO,CAACC,KAAK,CACX,2EAA2E,GAC3E,sEACF,CAAC;AACH;AAEA,SAASF,iBAAiBA,CAAA,EAAI;EAC5B;EACA,IAAI;IACF,MAAMG,GAAG,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;IAC7B,MAAMC,KAAK,GAAG;MAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;QAAE,OAAO,EAAE;MAAC;IAAE,CAAC;IAChDC,MAAM,CAACC,cAAc,CAACH,KAAK,EAAED,UAAU,CAACK,SAAS,CAAC;IAClDF,MAAM,CAACC,cAAc,CAACL,GAAG,EAAEE,KAAK,CAAC;IACjC,OAAOF,GAAG,CAACG,GAAG,CAAC,CAAC,KAAK,EAAE;EACzB,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEAH,MAAM,CAACI,cAAc,CAACjB,MAAM,CAACe,SAAS,EAAE,QAAQ,EAAE;EAChDG,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAOC,SAAS;IAC5C,OAAO,IAAI,CAACC,MAAM;EACpB;AACF,CAAC,CAAC;AAEFT,MAAM,CAACI,cAAc,CAACjB,MAAM,CAACe,SAAS,EAAE,QAAQ,EAAE;EAChDG,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAOC,SAAS;IAC5C,OAAO,IAAI,CAACE,UAAU;EACxB;AACF,CAAC,CAAC;AAEF,SAASC,YAAYA,CAAEC,MAAM,EAAE;EAC7B,IAAIA,MAAM,GAAGtB,YAAY,EAAE;IACzB,MAAM,IAAIuB,UAAU,CAAC,aAAa,GAAGD,MAAM,GAAG,gCAAgC,CAAC;EACjF;EACA;EACA,MAAME,GAAG,GAAG,IAAIjB,UAAU,CAACe,MAAM,CAAC;EAClCZ,MAAM,CAACC,cAAc,CAACa,GAAG,EAAE3B,MAAM,CAACe,SAAS,CAAC;EAC5C,OAAOY,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS3B,MAAMA,CAAE4B,GAAG,EAAEC,gBAAgB,EAAEJ,MAAM,EAAE;EAC9C;EACA,IAAI,OAAOG,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI,OAAOC,gBAAgB,KAAK,QAAQ,EAAE;MACxC,MAAM,IAAIC,SAAS,CACjB,oEACF,CAAC;IACH;IACA,OAAOC,WAAW,CAACH,GAAG,CAAC;EACzB;EACA,OAAOI,IAAI,CAACJ,GAAG,EAAEC,gBAAgB,EAAEJ,MAAM,CAAC;AAC5C;AAEAzB,MAAM,CAACiC,QAAQ,GAAG,IAAI,EAAC;;AAEvB,SAASD,IAAIA,CAAEE,KAAK,EAAEL,gBAAgB,EAAEJ,MAAM,EAAE;EAC9C,IAAI,OAAOS,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOC,UAAU,CAACD,KAAK,EAAEL,gBAAgB,CAAC;EAC5C;EAEA,IAAIO,WAAW,CAACC,MAAM,CAACH,KAAK,CAAC,EAAE;IAC7B,OAAOI,aAAa,CAACJ,KAAK,CAAC;EAC7B;EAEA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIJ,SAAS,CACjB,6EAA6E,GAC7E,sCAAsC,GAAI,OAAOI,KACnD,CAAC;EACH;EAEA,IAAIK,UAAU,CAACL,KAAK,EAAEE,WAAW,CAAC,IAC7BF,KAAK,IAAIK,UAAU,CAACL,KAAK,CAACZ,MAAM,EAAEc,WAAW,CAAE,EAAE;IACpD,OAAOI,eAAe,CAACN,KAAK,EAAEL,gBAAgB,EAAEJ,MAAM,CAAC;EACzD;EAEA,IAAI,OAAOgB,iBAAiB,KAAK,WAAW,KACvCF,UAAU,CAACL,KAAK,EAAEO,iBAAiB,CAAC,IACpCP,KAAK,IAAIK,UAAU,CAACL,KAAK,CAACZ,MAAM,EAAEmB,iBAAiB,CAAE,CAAC,EAAE;IAC3D,OAAOD,eAAe,CAACN,KAAK,EAAEL,gBAAgB,EAAEJ,MAAM,CAAC;EACzD;EAEA,IAAI,OAAOS,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIJ,SAAS,CACjB,uEACF,CAAC;EACH;EAEA,MAAMY,OAAO,GAAGR,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACQ,OAAO,CAAC,CAAC;EAChD,IAAIA,OAAO,IAAI,IAAI,IAAIA,OAAO,KAAKR,KAAK,EAAE;IACxC,OAAOlC,MAAM,CAACgC,IAAI,CAACU,OAAO,EAAEb,gBAAgB,EAAEJ,MAAM,CAAC;EACvD;EAEA,MAAMkB,CAAC,GAAGC,UAAU,CAACV,KAAK,CAAC;EAC3B,IAAIS,CAAC,EAAE,OAAOA,CAAC;EAEf,IAAI,OAAO7C,MAAM,KAAK,WAAW,IAAIA,MAAM,CAAC+C,WAAW,IAAI,IAAI,IAC3D,OAAOX,KAAK,CAACpC,MAAM,CAAC+C,WAAW,CAAC,KAAK,UAAU,EAAE;IACnD,OAAO7C,MAAM,CAACgC,IAAI,CAACE,KAAK,CAACpC,MAAM,CAAC+C,WAAW,CAAC,CAAC,QAAQ,CAAC,EAAEhB,gBAAgB,EAAEJ,MAAM,CAAC;EACnF;EAEA,MAAM,IAAIK,SAAS,CACjB,6EAA6E,GAC7E,sCAAsC,GAAI,OAAOI,KACnD,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,MAAM,CAACgC,IAAI,GAAG,UAAUE,KAAK,EAAEL,gBAAgB,EAAEJ,MAAM,EAAE;EACvD,OAAOO,IAAI,CAACE,KAAK,EAAEL,gBAAgB,EAAEJ,MAAM,CAAC;AAC9C,CAAC;;AAED;AACA;AACAZ,MAAM,CAACC,cAAc,CAACd,MAAM,CAACe,SAAS,EAAEL,UAAU,CAACK,SAAS,CAAC;AAC7DF,MAAM,CAACC,cAAc,CAACd,MAAM,EAAEU,UAAU,CAAC;AAEzC,SAASoC,UAAUA,CAAEC,IAAI,EAAE;EACzB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIjB,SAAS,CAAC,wCAAwC,CAAC;EAC/D,CAAC,MAAM,IAAIiB,IAAI,GAAG,CAAC,EAAE;IACnB,MAAM,IAAIrB,UAAU,CAAC,aAAa,GAAGqB,IAAI,GAAG,gCAAgC,CAAC;EAC/E;AACF;AAEA,SAASC,KAAKA,CAAED,IAAI,EAAEE,IAAI,EAAEC,QAAQ,EAAE;EACpCJ,UAAU,CAACC,IAAI,CAAC;EAChB,IAAIA,IAAI,IAAI,CAAC,EAAE;IACb,OAAOvB,YAAY,CAACuB,IAAI,CAAC;EAC3B;EACA,IAAIE,IAAI,KAAK5B,SAAS,EAAE;IACtB;IACA;IACA;IACA,OAAO,OAAO6B,QAAQ,KAAK,QAAQ,GAC/B1B,YAAY,CAACuB,IAAI,CAAC,CAACE,IAAI,CAACA,IAAI,EAAEC,QAAQ,CAAC,GACvC1B,YAAY,CAACuB,IAAI,CAAC,CAACE,IAAI,CAACA,IAAI,CAAC;EACnC;EACA,OAAOzB,YAAY,CAACuB,IAAI,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA/C,MAAM,CAACgD,KAAK,GAAG,UAAUD,IAAI,EAAEE,IAAI,EAAEC,QAAQ,EAAE;EAC7C,OAAOF,KAAK,CAACD,IAAI,EAAEE,IAAI,EAAEC,QAAQ,CAAC;AACpC,CAAC;AAED,SAASnB,WAAWA,CAAEgB,IAAI,EAAE;EAC1BD,UAAU,CAACC,IAAI,CAAC;EAChB,OAAOvB,YAAY,CAACuB,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC;AACvD;;AAEA;AACA;AACA;AACA/C,MAAM,CAAC+B,WAAW,GAAG,UAAUgB,IAAI,EAAE;EACnC,OAAOhB,WAAW,CAACgB,IAAI,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA/C,MAAM,CAACoD,eAAe,GAAG,UAAUL,IAAI,EAAE;EACvC,OAAOhB,WAAW,CAACgB,IAAI,CAAC;AAC1B,CAAC;AAED,SAASZ,UAAUA,CAAEkB,MAAM,EAAEH,QAAQ,EAAE;EACrC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;IACnDA,QAAQ,GAAG,MAAM;EACnB;EAEA,IAAI,CAAClD,MAAM,CAACsD,UAAU,CAACJ,QAAQ,CAAC,EAAE;IAChC,MAAM,IAAIpB,SAAS,CAAC,oBAAoB,GAAGoB,QAAQ,CAAC;EACtD;EAEA,MAAMzB,MAAM,GAAG8B,UAAU,CAACF,MAAM,EAAEH,QAAQ,CAAC,GAAG,CAAC;EAC/C,IAAIvB,GAAG,GAAGH,YAAY,CAACC,MAAM,CAAC;EAE9B,MAAM+B,MAAM,GAAG7B,GAAG,CAAC8B,KAAK,CAACJ,MAAM,EAAEH,QAAQ,CAAC;EAE1C,IAAIM,MAAM,KAAK/B,MAAM,EAAE;IACrB;IACA;IACA;IACAE,GAAG,GAAGA,GAAG,CAAC+B,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC;EAC5B;EAEA,OAAO7B,GAAG;AACZ;AAEA,SAASgC,aAAaA,CAAEC,KAAK,EAAE;EAC7B,MAAMnC,MAAM,GAAGmC,KAAK,CAACnC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG0B,OAAO,CAACS,KAAK,CAACnC,MAAM,CAAC,GAAG,CAAC;EAC/D,MAAME,GAAG,GAAGH,YAAY,CAACC,MAAM,CAAC;EAChC,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,MAAM,EAAEoC,CAAC,IAAI,CAAC,EAAE;IAClClC,GAAG,CAACkC,CAAC,CAAC,GAAGD,KAAK,CAACC,CAAC,CAAC,GAAG,GAAG;EACzB;EACA,OAAOlC,GAAG;AACZ;AAEA,SAASW,aAAaA,CAAEwB,SAAS,EAAE;EACjC,IAAIvB,UAAU,CAACuB,SAAS,EAAEpD,UAAU,CAAC,EAAE;IACrC,MAAMqD,IAAI,GAAG,IAAIrD,UAAU,CAACoD,SAAS,CAAC;IACtC,OAAOtB,eAAe,CAACuB,IAAI,CAACzC,MAAM,EAAEyC,IAAI,CAACxC,UAAU,EAAEwC,IAAI,CAACR,UAAU,CAAC;EACvE;EACA,OAAOI,aAAa,CAACG,SAAS,CAAC;AACjC;AAEA,SAAStB,eAAeA,CAAEoB,KAAK,EAAErC,UAAU,EAAEE,MAAM,EAAE;EACnD,IAAIF,UAAU,GAAG,CAAC,IAAIqC,KAAK,CAACL,UAAU,GAAGhC,UAAU,EAAE;IACnD,MAAM,IAAIG,UAAU,CAAC,sCAAsC,CAAC;EAC9D;EAEA,IAAIkC,KAAK,CAACL,UAAU,GAAGhC,UAAU,IAAIE,MAAM,IAAI,CAAC,CAAC,EAAE;IACjD,MAAM,IAAIC,UAAU,CAAC,sCAAsC,CAAC;EAC9D;EAEA,IAAIC,GAAG;EACP,IAAIJ,UAAU,KAAKF,SAAS,IAAII,MAAM,KAAKJ,SAAS,EAAE;IACpDM,GAAG,GAAG,IAAIjB,UAAU,CAACkD,KAAK,CAAC;EAC7B,CAAC,MAAM,IAAInC,MAAM,KAAKJ,SAAS,EAAE;IAC/BM,GAAG,GAAG,IAAIjB,UAAU,CAACkD,KAAK,EAAErC,UAAU,CAAC;EACzC,CAAC,MAAM;IACLI,GAAG,GAAG,IAAIjB,UAAU,CAACkD,KAAK,EAAErC,UAAU,EAAEE,MAAM,CAAC;EACjD;;EAEA;EACAZ,MAAM,CAACC,cAAc,CAACa,GAAG,EAAE3B,MAAM,CAACe,SAAS,CAAC;EAE5C,OAAOY,GAAG;AACZ;AAEA,SAASiB,UAAUA,CAAEoB,GAAG,EAAE;EACxB,IAAIhE,MAAM,CAACoB,QAAQ,CAAC4C,GAAG,CAAC,EAAE;IACxB,MAAMC,GAAG,GAAGd,OAAO,CAACa,GAAG,CAACvC,MAAM,CAAC,GAAG,CAAC;IACnC,MAAME,GAAG,GAAGH,YAAY,CAACyC,GAAG,CAAC;IAE7B,IAAItC,GAAG,CAACF,MAAM,KAAK,CAAC,EAAE;MACpB,OAAOE,GAAG;IACZ;IAEAqC,GAAG,CAACD,IAAI,CAACpC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEsC,GAAG,CAAC;IACxB,OAAOtC,GAAG;EACZ;EAEA,IAAIqC,GAAG,CAACvC,MAAM,KAAKJ,SAAS,EAAE;IAC5B,IAAI,OAAO2C,GAAG,CAACvC,MAAM,KAAK,QAAQ,IAAIyC,WAAW,CAACF,GAAG,CAACvC,MAAM,CAAC,EAAE;MAC7D,OAAOD,YAAY,CAAC,CAAC,CAAC;IACxB;IACA,OAAOmC,aAAa,CAACK,GAAG,CAAC;EAC3B;EAEA,IAAIA,GAAG,CAACG,IAAI,KAAK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACL,GAAG,CAACM,IAAI,CAAC,EAAE;IACpD,OAAOX,aAAa,CAACK,GAAG,CAACM,IAAI,CAAC;EAChC;AACF;AAEA,SAASnB,OAAOA,CAAE1B,MAAM,EAAE;EACxB;EACA;EACA,IAAIA,MAAM,IAAItB,YAAY,EAAE;IAC1B,MAAM,IAAIuB,UAAU,CAAC,iDAAiD,GACjD,UAAU,GAAGvB,YAAY,CAACoE,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC;EACzE;EACA,OAAO9C,MAAM,GAAG,CAAC;AACnB;AAEA,SAASxB,UAAUA,CAAEwB,MAAM,EAAE;EAC3B,IAAI,CAACA,MAAM,IAAIA,MAAM,EAAE;IAAE;IACvBA,MAAM,GAAG,CAAC;EACZ;EACA,OAAOzB,MAAM,CAACgD,KAAK,CAAC,CAACvB,MAAM,CAAC;AAC9B;AAEAzB,MAAM,CAACoB,QAAQ,GAAG,SAASA,QAAQA,CAAEuB,CAAC,EAAE;EACtC,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,CAAC6B,SAAS,KAAK,IAAI,IACtC7B,CAAC,KAAK3C,MAAM,CAACe,SAAS,EAAC;AAC3B,CAAC;AAEDf,MAAM,CAACyE,OAAO,GAAG,SAASA,OAAOA,CAAEC,CAAC,EAAE/B,CAAC,EAAE;EACvC,IAAIJ,UAAU,CAACmC,CAAC,EAAEhE,UAAU,CAAC,EAAEgE,CAAC,GAAG1E,MAAM,CAACgC,IAAI,CAAC0C,CAAC,EAAEA,CAAC,CAACC,MAAM,EAAED,CAAC,CAACnB,UAAU,CAAC;EACzE,IAAIhB,UAAU,CAACI,CAAC,EAAEjC,UAAU,CAAC,EAAEiC,CAAC,GAAG3C,MAAM,CAACgC,IAAI,CAACW,CAAC,EAAEA,CAAC,CAACgC,MAAM,EAAEhC,CAAC,CAACY,UAAU,CAAC;EACzE,IAAI,CAACvD,MAAM,CAACoB,QAAQ,CAACsD,CAAC,CAAC,IAAI,CAAC1E,MAAM,CAACoB,QAAQ,CAACuB,CAAC,CAAC,EAAE;IAC9C,MAAM,IAAIb,SAAS,CACjB,uEACF,CAAC;EACH;EAEA,IAAI4C,CAAC,KAAK/B,CAAC,EAAE,OAAO,CAAC;EAErB,IAAIiC,CAAC,GAAGF,CAAC,CAACjD,MAAM;EAChB,IAAIoD,CAAC,GAAGlC,CAAC,CAAClB,MAAM;EAEhB,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEI,GAAG,GAAGa,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC,EAAEhB,CAAC,GAAGI,GAAG,EAAE,EAAEJ,CAAC,EAAE;IAClD,IAAIa,CAAC,CAACb,CAAC,CAAC,KAAKlB,CAAC,CAACkB,CAAC,CAAC,EAAE;MACjBe,CAAC,GAAGF,CAAC,CAACb,CAAC,CAAC;MACRgB,CAAC,GAAGlC,CAAC,CAACkB,CAAC,CAAC;MACR;IACF;EACF;EAEA,IAAIe,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC,CAAC;EACpB,IAAIA,CAAC,GAAGD,CAAC,EAAE,OAAO,CAAC;EACnB,OAAO,CAAC;AACV,CAAC;AAED5E,MAAM,CAACsD,UAAU,GAAG,SAASA,UAAUA,CAAEJ,QAAQ,EAAE;EACjD,QAAQ8B,MAAM,CAAC9B,QAAQ,CAAC,CAAC+B,WAAW,CAAC,CAAC;IACpC,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,SAAS;IACd,KAAK,UAAU;MACb,OAAO,IAAI;IACb;MACE,OAAO,KAAK;EAChB;AACF,CAAC;AAEDjF,MAAM,CAACkF,MAAM,GAAG,SAASA,MAAMA,CAAEC,IAAI,EAAE1D,MAAM,EAAE;EAC7C,IAAI,CAAC2C,KAAK,CAACC,OAAO,CAACc,IAAI,CAAC,EAAE;IACxB,MAAM,IAAIrD,SAAS,CAAC,6CAA6C,CAAC;EACpE;EAEA,IAAIqD,IAAI,CAAC1D,MAAM,KAAK,CAAC,EAAE;IACrB,OAAOzB,MAAM,CAACgD,KAAK,CAAC,CAAC,CAAC;EACxB;EAEA,IAAIa,CAAC;EACL,IAAIpC,MAAM,KAAKJ,SAAS,EAAE;IACxBI,MAAM,GAAG,CAAC;IACV,KAAKoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,IAAI,CAAC1D,MAAM,EAAE,EAAEoC,CAAC,EAAE;MAChCpC,MAAM,IAAI0D,IAAI,CAACtB,CAAC,CAAC,CAACpC,MAAM;IAC1B;EACF;EAEA,MAAMH,MAAM,GAAGtB,MAAM,CAAC+B,WAAW,CAACN,MAAM,CAAC;EACzC,IAAI2D,GAAG,GAAG,CAAC;EACX,KAAKvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,IAAI,CAAC1D,MAAM,EAAE,EAAEoC,CAAC,EAAE;IAChC,IAAIlC,GAAG,GAAGwD,IAAI,CAACtB,CAAC,CAAC;IACjB,IAAItB,UAAU,CAACZ,GAAG,EAAEjB,UAAU,CAAC,EAAE;MAC/B,IAAI0E,GAAG,GAAGzD,GAAG,CAACF,MAAM,GAAGH,MAAM,CAACG,MAAM,EAAE;QACpC,IAAI,CAACzB,MAAM,CAACoB,QAAQ,CAACO,GAAG,CAAC,EAAEA,GAAG,GAAG3B,MAAM,CAACgC,IAAI,CAACL,GAAG,CAAC;QACjDA,GAAG,CAACoC,IAAI,CAACzC,MAAM,EAAE8D,GAAG,CAAC;MACvB,CAAC,MAAM;QACL1E,UAAU,CAACK,SAAS,CAACsE,GAAG,CAACC,IAAI,CAC3BhE,MAAM,EACNK,GAAG,EACHyD,GACF,CAAC;MACH;IACF,CAAC,MAAM,IAAI,CAACpF,MAAM,CAACoB,QAAQ,CAACO,GAAG,CAAC,EAAE;MAChC,MAAM,IAAIG,SAAS,CAAC,6CAA6C,CAAC;IACpE,CAAC,MAAM;MACLH,GAAG,CAACoC,IAAI,CAACzC,MAAM,EAAE8D,GAAG,CAAC;IACvB;IACAA,GAAG,IAAIzD,GAAG,CAACF,MAAM;EACnB;EACA,OAAOH,MAAM;AACf,CAAC;AAED,SAASiC,UAAUA,CAAEF,MAAM,EAAEH,QAAQ,EAAE;EACrC,IAAIlD,MAAM,CAACoB,QAAQ,CAACiC,MAAM,CAAC,EAAE;IAC3B,OAAOA,MAAM,CAAC5B,MAAM;EACtB;EACA,IAAIW,WAAW,CAACC,MAAM,CAACgB,MAAM,CAAC,IAAId,UAAU,CAACc,MAAM,EAAEjB,WAAW,CAAC,EAAE;IACjE,OAAOiB,MAAM,CAACE,UAAU;EAC1B;EACA,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIvB,SAAS,CACjB,4EAA4E,GAC5E,gBAAgB,GAAG,OAAOuB,MAC5B,CAAC;EACH;EAEA,MAAMY,GAAG,GAAGZ,MAAM,CAAC5B,MAAM;EACzB,MAAM8D,SAAS,GAAIC,SAAS,CAAC/D,MAAM,GAAG,CAAC,IAAI+D,SAAS,CAAC,CAAC,CAAC,KAAK,IAAK;EACjE,IAAI,CAACD,SAAS,IAAItB,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;;EAErC;EACA,IAAIwB,WAAW,GAAG,KAAK;EACvB,SAAS;IACP,QAAQvC,QAAQ;MACd,KAAK,OAAO;MACZ,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAOe,GAAG;MACZ,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAOyB,WAAW,CAACrC,MAAM,CAAC,CAAC5B,MAAM;MACnC,KAAK,MAAM;MACX,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,UAAU;QACb,OAAOwC,GAAG,GAAG,CAAC;MAChB,KAAK,KAAK;QACR,OAAOA,GAAG,KAAK,CAAC;MAClB,KAAK,QAAQ;QACX,OAAO0B,aAAa,CAACtC,MAAM,CAAC,CAAC5B,MAAM;MACrC;QACE,IAAIgE,WAAW,EAAE;UACf,OAAOF,SAAS,GAAG,CAAC,CAAC,GAAGG,WAAW,CAACrC,MAAM,CAAC,CAAC5B,MAAM,EAAC;QACrD;QACAyB,QAAQ,GAAG,CAAC,EAAE,GAAGA,QAAQ,EAAE+B,WAAW,CAAC,CAAC;QACxCQ,WAAW,GAAG,IAAI;IACtB;EACF;AACF;AACAzF,MAAM,CAACuD,UAAU,GAAGA,UAAU;AAE9B,SAASqC,YAAYA,CAAE1C,QAAQ,EAAE2C,KAAK,EAAEC,GAAG,EAAE;EAC3C,IAAIL,WAAW,GAAG,KAAK;;EAEvB;EACA;;EAEA;EACA;EACA;EACA;EACA,IAAII,KAAK,KAAKxE,SAAS,IAAIwE,KAAK,GAAG,CAAC,EAAE;IACpCA,KAAK,GAAG,CAAC;EACX;EACA;EACA;EACA,IAAIA,KAAK,GAAG,IAAI,CAACpE,MAAM,EAAE;IACvB,OAAO,EAAE;EACX;EAEA,IAAIqE,GAAG,KAAKzE,SAAS,IAAIyE,GAAG,GAAG,IAAI,CAACrE,MAAM,EAAE;IAC1CqE,GAAG,GAAG,IAAI,CAACrE,MAAM;EACnB;EAEA,IAAIqE,GAAG,IAAI,CAAC,EAAE;IACZ,OAAO,EAAE;EACX;;EAEA;EACAA,GAAG,MAAM,CAAC;EACVD,KAAK,MAAM,CAAC;EAEZ,IAAIC,GAAG,IAAID,KAAK,EAAE;IAChB,OAAO,EAAE;EACX;EAEA,IAAI,CAAC3C,QAAQ,EAAEA,QAAQ,GAAG,MAAM;EAEhC,OAAO,IAAI,EAAE;IACX,QAAQA,QAAQ;MACd,KAAK,KAAK;QACR,OAAO6C,QAAQ,CAAC,IAAI,EAAEF,KAAK,EAAEC,GAAG,CAAC;MAEnC,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAOE,SAAS,CAAC,IAAI,EAAEH,KAAK,EAAEC,GAAG,CAAC;MAEpC,KAAK,OAAO;QACV,OAAOG,UAAU,CAAC,IAAI,EAAEJ,KAAK,EAAEC,GAAG,CAAC;MAErC,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAOI,WAAW,CAAC,IAAI,EAAEL,KAAK,EAAEC,GAAG,CAAC;MAEtC,KAAK,QAAQ;QACX,OAAOK,WAAW,CAAC,IAAI,EAAEN,KAAK,EAAEC,GAAG,CAAC;MAEtC,KAAK,MAAM;MACX,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,UAAU;QACb,OAAOM,YAAY,CAAC,IAAI,EAAEP,KAAK,EAAEC,GAAG,CAAC;MAEvC;QACE,IAAIL,WAAW,EAAE,MAAM,IAAI3D,SAAS,CAAC,oBAAoB,GAAGoB,QAAQ,CAAC;QACrEA,QAAQ,GAAG,CAACA,QAAQ,GAAG,EAAE,EAAE+B,WAAW,CAAC,CAAC;QACxCQ,WAAW,GAAG,IAAI;IACtB;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAzF,MAAM,CAACe,SAAS,CAACyD,SAAS,GAAG,IAAI;AAEjC,SAAS6B,IAAIA,CAAE1D,CAAC,EAAE2D,CAAC,EAAEC,CAAC,EAAE;EACtB,MAAM1C,CAAC,GAAGlB,CAAC,CAAC2D,CAAC,CAAC;EACd3D,CAAC,CAAC2D,CAAC,CAAC,GAAG3D,CAAC,CAAC4D,CAAC,CAAC;EACX5D,CAAC,CAAC4D,CAAC,CAAC,GAAG1C,CAAC;AACV;AAEA7D,MAAM,CAACe,SAAS,CAACyF,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EAC3C,MAAMvC,GAAG,GAAG,IAAI,CAACxC,MAAM;EACvB,IAAIwC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIvC,UAAU,CAAC,2CAA2C,CAAC;EACnE;EACA,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,GAAG,EAAEJ,CAAC,IAAI,CAAC,EAAE;IAC/BwC,IAAI,CAAC,IAAI,EAAExC,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;EACtB;EACA,OAAO,IAAI;AACb,CAAC;AAED7D,MAAM,CAACe,SAAS,CAAC0F,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EAC3C,MAAMxC,GAAG,GAAG,IAAI,CAACxC,MAAM;EACvB,IAAIwC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIvC,UAAU,CAAC,2CAA2C,CAAC;EACnE;EACA,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,GAAG,EAAEJ,CAAC,IAAI,CAAC,EAAE;IAC/BwC,IAAI,CAAC,IAAI,EAAExC,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACpBwC,IAAI,CAAC,IAAI,EAAExC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;EAC1B;EACA,OAAO,IAAI;AACb,CAAC;AAED7D,MAAM,CAACe,SAAS,CAAC2F,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EAC3C,MAAMzC,GAAG,GAAG,IAAI,CAACxC,MAAM;EACvB,IAAIwC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACjB,MAAM,IAAIvC,UAAU,CAAC,2CAA2C,CAAC;EACnE;EACA,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,GAAG,EAAEJ,CAAC,IAAI,CAAC,EAAE;IAC/BwC,IAAI,CAAC,IAAI,EAAExC,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACpBwC,IAAI,CAAC,IAAI,EAAExC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACxBwC,IAAI,CAAC,IAAI,EAAExC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACxBwC,IAAI,CAAC,IAAI,EAAExC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;EAC1B;EACA,OAAO,IAAI;AACb,CAAC;AAED7D,MAAM,CAACe,SAAS,CAACwD,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;EAC/C,MAAM9C,MAAM,GAAG,IAAI,CAACA,MAAM;EAC1B,IAAIA,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EAC3B,IAAI+D,SAAS,CAAC/D,MAAM,KAAK,CAAC,EAAE,OAAOuE,SAAS,CAAC,IAAI,EAAE,CAAC,EAAEvE,MAAM,CAAC;EAC7D,OAAOmE,YAAY,CAACe,KAAK,CAAC,IAAI,EAAEnB,SAAS,CAAC;AAC5C,CAAC;AAEDxF,MAAM,CAACe,SAAS,CAAC6F,cAAc,GAAG5G,MAAM,CAACe,SAAS,CAACwD,QAAQ;AAE3DvE,MAAM,CAACe,SAAS,CAAC8F,MAAM,GAAG,SAASA,MAAMA,CAAElE,CAAC,EAAE;EAC5C,IAAI,CAAC3C,MAAM,CAACoB,QAAQ,CAACuB,CAAC,CAAC,EAAE,MAAM,IAAIb,SAAS,CAAC,2BAA2B,CAAC;EACzE,IAAI,IAAI,KAAKa,CAAC,EAAE,OAAO,IAAI;EAC3B,OAAO3C,MAAM,CAACyE,OAAO,CAAC,IAAI,EAAE9B,CAAC,CAAC,KAAK,CAAC;AACtC,CAAC;AAED3C,MAAM,CAACe,SAAS,CAAC+F,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;EAC7C,IAAIC,GAAG,GAAG,EAAE;EACZ,MAAMC,GAAG,GAAGjH,OAAO,CAACG,iBAAiB;EACrC6G,GAAG,GAAG,IAAI,CAACxC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAEyC,GAAG,CAAC,CAACC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC;EACnE,IAAI,IAAI,CAACzF,MAAM,GAAGuF,GAAG,EAAED,GAAG,IAAI,OAAO;EACrC,OAAO,UAAU,GAAGA,GAAG,GAAG,GAAG;AAC/B,CAAC;AACD,IAAIlH,mBAAmB,EAAE;EACvBG,MAAM,CAACe,SAAS,CAAClB,mBAAmB,CAAC,GAAGG,MAAM,CAACe,SAAS,CAAC+F,OAAO;AAClE;AAEA9G,MAAM,CAACe,SAAS,CAAC0D,OAAO,GAAG,SAASA,OAAOA,CAAE0C,MAAM,EAAEtB,KAAK,EAAEC,GAAG,EAAEsB,SAAS,EAAEC,OAAO,EAAE;EACnF,IAAI9E,UAAU,CAAC4E,MAAM,EAAEzG,UAAU,CAAC,EAAE;IAClCyG,MAAM,GAAGnH,MAAM,CAACgC,IAAI,CAACmF,MAAM,EAAEA,MAAM,CAACxC,MAAM,EAAEwC,MAAM,CAAC5D,UAAU,CAAC;EAChE;EACA,IAAI,CAACvD,MAAM,CAACoB,QAAQ,CAAC+F,MAAM,CAAC,EAAE;IAC5B,MAAM,IAAIrF,SAAS,CACjB,kEAAkE,GAClE,gBAAgB,GAAI,OAAOqF,MAC7B,CAAC;EACH;EAEA,IAAItB,KAAK,KAAKxE,SAAS,EAAE;IACvBwE,KAAK,GAAG,CAAC;EACX;EACA,IAAIC,GAAG,KAAKzE,SAAS,EAAE;IACrByE,GAAG,GAAGqB,MAAM,GAAGA,MAAM,CAAC1F,MAAM,GAAG,CAAC;EAClC;EACA,IAAI2F,SAAS,KAAK/F,SAAS,EAAE;IAC3B+F,SAAS,GAAG,CAAC;EACf;EACA,IAAIC,OAAO,KAAKhG,SAAS,EAAE;IACzBgG,OAAO,GAAG,IAAI,CAAC5F,MAAM;EACvB;EAEA,IAAIoE,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAGqB,MAAM,CAAC1F,MAAM,IAAI2F,SAAS,GAAG,CAAC,IAAIC,OAAO,GAAG,IAAI,CAAC5F,MAAM,EAAE;IAC9E,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,IAAI0F,SAAS,IAAIC,OAAO,IAAIxB,KAAK,IAAIC,GAAG,EAAE;IACxC,OAAO,CAAC;EACV;EACA,IAAIsB,SAAS,IAAIC,OAAO,EAAE;IACxB,OAAO,CAAC,CAAC;EACX;EACA,IAAIxB,KAAK,IAAIC,GAAG,EAAE;IAChB,OAAO,CAAC;EACV;EAEAD,KAAK,MAAM,CAAC;EACZC,GAAG,MAAM,CAAC;EACVsB,SAAS,MAAM,CAAC;EAChBC,OAAO,MAAM,CAAC;EAEd,IAAI,IAAI,KAAKF,MAAM,EAAE,OAAO,CAAC;EAE7B,IAAIvC,CAAC,GAAGyC,OAAO,GAAGD,SAAS;EAC3B,IAAIvC,CAAC,GAAGiB,GAAG,GAAGD,KAAK;EACnB,MAAM5B,GAAG,GAAGa,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC;EAE1B,MAAMyC,QAAQ,GAAG,IAAI,CAAC5D,KAAK,CAAC0D,SAAS,EAAEC,OAAO,CAAC;EAC/C,MAAME,UAAU,GAAGJ,MAAM,CAACzD,KAAK,CAACmC,KAAK,EAAEC,GAAG,CAAC;EAE3C,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,GAAG,EAAE,EAAEJ,CAAC,EAAE;IAC5B,IAAIyD,QAAQ,CAACzD,CAAC,CAAC,KAAK0D,UAAU,CAAC1D,CAAC,CAAC,EAAE;MACjCe,CAAC,GAAG0C,QAAQ,CAACzD,CAAC,CAAC;MACfgB,CAAC,GAAG0C,UAAU,CAAC1D,CAAC,CAAC;MACjB;IACF;EACF;EAEA,IAAIe,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC,CAAC;EACpB,IAAIA,CAAC,GAAGD,CAAC,EAAE,OAAO,CAAC;EACnB,OAAO,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4C,oBAAoBA,CAAElG,MAAM,EAAEmG,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,EAAEwE,GAAG,EAAE;EACrE;EACA,IAAIpG,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;;EAElC;EACA,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;IAClC2B,QAAQ,GAAG3B,UAAU;IACrBA,UAAU,GAAG,CAAC;EAChB,CAAC,MAAM,IAAIA,UAAU,GAAG,UAAU,EAAE;IAClCA,UAAU,GAAG,UAAU;EACzB,CAAC,MAAM,IAAIA,UAAU,GAAG,CAAC,UAAU,EAAE;IACnCA,UAAU,GAAG,CAAC,UAAU;EAC1B;EACAA,UAAU,GAAG,CAACA,UAAU,EAAC;EACzB,IAAI2C,WAAW,CAAC3C,UAAU,CAAC,EAAE;IAC3B;IACAA,UAAU,GAAGmG,GAAG,GAAG,CAAC,GAAIpG,MAAM,CAACG,MAAM,GAAG,CAAE;EAC5C;;EAEA;EACA,IAAIF,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGD,MAAM,CAACG,MAAM,GAAGF,UAAU;EAC3D,IAAIA,UAAU,IAAID,MAAM,CAACG,MAAM,EAAE;IAC/B,IAAIiG,GAAG,EAAE,OAAO,CAAC,CAAC,MACbnG,UAAU,GAAGD,MAAM,CAACG,MAAM,GAAG,CAAC;EACrC,CAAC,MAAM,IAAIF,UAAU,GAAG,CAAC,EAAE;IACzB,IAAImG,GAAG,EAAEnG,UAAU,GAAG,CAAC,MAClB,OAAO,CAAC,CAAC;EAChB;;EAEA;EACA,IAAI,OAAOkG,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAGzH,MAAM,CAACgC,IAAI,CAACyF,GAAG,EAAEvE,QAAQ,CAAC;EAClC;;EAEA;EACA,IAAIlD,MAAM,CAACoB,QAAQ,CAACqG,GAAG,CAAC,EAAE;IACxB;IACA,IAAIA,GAAG,CAAChG,MAAM,KAAK,CAAC,EAAE;MACpB,OAAO,CAAC,CAAC;IACX;IACA,OAAOkG,YAAY,CAACrG,MAAM,EAAEmG,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,EAAEwE,GAAG,CAAC;EAC7D,CAAC,MAAM,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAClCA,GAAG,GAAGA,GAAG,GAAG,IAAI,EAAC;IACjB,IAAI,OAAO/G,UAAU,CAACK,SAAS,CAAC6G,OAAO,KAAK,UAAU,EAAE;MACtD,IAAIF,GAAG,EAAE;QACP,OAAOhH,UAAU,CAACK,SAAS,CAAC6G,OAAO,CAACtC,IAAI,CAAChE,MAAM,EAAEmG,GAAG,EAAElG,UAAU,CAAC;MACnE,CAAC,MAAM;QACL,OAAOb,UAAU,CAACK,SAAS,CAAC8G,WAAW,CAACvC,IAAI,CAAChE,MAAM,EAAEmG,GAAG,EAAElG,UAAU,CAAC;MACvE;IACF;IACA,OAAOoG,YAAY,CAACrG,MAAM,EAAE,CAACmG,GAAG,CAAC,EAAElG,UAAU,EAAE2B,QAAQ,EAAEwE,GAAG,CAAC;EAC/D;EAEA,MAAM,IAAI5F,SAAS,CAAC,sCAAsC,CAAC;AAC7D;AAEA,SAAS6F,YAAYA,CAAElH,GAAG,EAAEgH,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,EAAEwE,GAAG,EAAE;EAC1D,IAAII,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAGtH,GAAG,CAACgB,MAAM;EAC1B,IAAIuG,SAAS,GAAGP,GAAG,CAAChG,MAAM;EAE1B,IAAIyB,QAAQ,KAAK7B,SAAS,EAAE;IAC1B6B,QAAQ,GAAG8B,MAAM,CAAC9B,QAAQ,CAAC,CAAC+B,WAAW,CAAC,CAAC;IACzC,IAAI/B,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,IAC3CA,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,UAAU,EAAE;MACrD,IAAIzC,GAAG,CAACgB,MAAM,GAAG,CAAC,IAAIgG,GAAG,CAAChG,MAAM,GAAG,CAAC,EAAE;QACpC,OAAO,CAAC,CAAC;MACX;MACAqG,SAAS,GAAG,CAAC;MACbC,SAAS,IAAI,CAAC;MACdC,SAAS,IAAI,CAAC;MACdzG,UAAU,IAAI,CAAC;IACjB;EACF;EAEA,SAAS0G,IAAIA,CAAEtG,GAAG,EAAEkC,CAAC,EAAE;IACrB,IAAIiE,SAAS,KAAK,CAAC,EAAE;MACnB,OAAOnG,GAAG,CAACkC,CAAC,CAAC;IACf,CAAC,MAAM;MACL,OAAOlC,GAAG,CAACuG,YAAY,CAACrE,CAAC,GAAGiE,SAAS,CAAC;IACxC;EACF;EAEA,IAAIjE,CAAC;EACL,IAAI6D,GAAG,EAAE;IACP,IAAIS,UAAU,GAAG,CAAC,CAAC;IACnB,KAAKtE,CAAC,GAAGtC,UAAU,EAAEsC,CAAC,GAAGkE,SAAS,EAAElE,CAAC,EAAE,EAAE;MACvC,IAAIoE,IAAI,CAACxH,GAAG,EAAEoD,CAAC,CAAC,KAAKoE,IAAI,CAACR,GAAG,EAAEU,UAAU,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGtE,CAAC,GAAGsE,UAAU,CAAC,EAAE;QACtE,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAEA,UAAU,GAAGtE,CAAC;QACrC,IAAIA,CAAC,GAAGsE,UAAU,GAAG,CAAC,KAAKH,SAAS,EAAE,OAAOG,UAAU,GAAGL,SAAS;MACrE,CAAC,MAAM;QACL,IAAIK,UAAU,KAAK,CAAC,CAAC,EAAEtE,CAAC,IAAIA,CAAC,GAAGsE,UAAU;QAC1CA,UAAU,GAAG,CAAC,CAAC;MACjB;IACF;EACF,CAAC,MAAM;IACL,IAAI5G,UAAU,GAAGyG,SAAS,GAAGD,SAAS,EAAExG,UAAU,GAAGwG,SAAS,GAAGC,SAAS;IAC1E,KAAKnE,CAAC,GAAGtC,UAAU,EAAEsC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAChC,IAAIuE,KAAK,GAAG,IAAI;MAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;QAClC,IAAIJ,IAAI,CAACxH,GAAG,EAAEoD,CAAC,GAAGwE,CAAC,CAAC,KAAKJ,IAAI,CAACR,GAAG,EAAEY,CAAC,CAAC,EAAE;UACrCD,KAAK,GAAG,KAAK;UACb;QACF;MACF;MACA,IAAIA,KAAK,EAAE,OAAOvE,CAAC;IACrB;EACF;EAEA,OAAO,CAAC,CAAC;AACX;AAEA7D,MAAM,CAACe,SAAS,CAACuH,QAAQ,GAAG,SAASA,QAAQA,CAAEb,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,EAAE;EACxE,OAAO,IAAI,CAAC0E,OAAO,CAACH,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvD,CAAC;AAEDlD,MAAM,CAACe,SAAS,CAAC6G,OAAO,GAAG,SAASA,OAAOA,CAAEH,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,EAAE;EACtE,OAAOsE,oBAAoB,CAAC,IAAI,EAAEC,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,EAAE,IAAI,CAAC;AACpE,CAAC;AAEDlD,MAAM,CAACe,SAAS,CAAC8G,WAAW,GAAG,SAASA,WAAWA,CAAEJ,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,EAAE;EAC9E,OAAOsE,oBAAoB,CAAC,IAAI,EAAEC,GAAG,EAAElG,UAAU,EAAE2B,QAAQ,EAAE,KAAK,CAAC;AACrE,CAAC;AAED,SAASqF,QAAQA,CAAE5G,GAAG,EAAE0B,MAAM,EAAEsB,MAAM,EAAElD,MAAM,EAAE;EAC9CkD,MAAM,GAAG6D,MAAM,CAAC7D,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM8D,SAAS,GAAG9G,GAAG,CAACF,MAAM,GAAGkD,MAAM;EACrC,IAAI,CAAClD,MAAM,EAAE;IACXA,MAAM,GAAGgH,SAAS;EACpB,CAAC,MAAM;IACLhH,MAAM,GAAG+G,MAAM,CAAC/G,MAAM,CAAC;IACvB,IAAIA,MAAM,GAAGgH,SAAS,EAAE;MACtBhH,MAAM,GAAGgH,SAAS;IACpB;EACF;EAEA,MAAMC,MAAM,GAAGrF,MAAM,CAAC5B,MAAM;EAE5B,IAAIA,MAAM,GAAGiH,MAAM,GAAG,CAAC,EAAE;IACvBjH,MAAM,GAAGiH,MAAM,GAAG,CAAC;EACrB;EACA,IAAI7E,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,MAAM,EAAE,EAAEoC,CAAC,EAAE;IAC3B,MAAM8E,MAAM,GAAGC,QAAQ,CAACvF,MAAM,CAACwF,MAAM,CAAChF,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD,IAAIK,WAAW,CAACyE,MAAM,CAAC,EAAE,OAAO9E,CAAC;IACjClC,GAAG,CAACgD,MAAM,GAAGd,CAAC,CAAC,GAAG8E,MAAM;EAC1B;EACA,OAAO9E,CAAC;AACV;AAEA,SAASiF,SAASA,CAAEnH,GAAG,EAAE0B,MAAM,EAAEsB,MAAM,EAAElD,MAAM,EAAE;EAC/C,OAAOsH,UAAU,CAACrD,WAAW,CAACrC,MAAM,EAAE1B,GAAG,CAACF,MAAM,GAAGkD,MAAM,CAAC,EAAEhD,GAAG,EAAEgD,MAAM,EAAElD,MAAM,CAAC;AAClF;AAEA,SAASuH,UAAUA,CAAErH,GAAG,EAAE0B,MAAM,EAAEsB,MAAM,EAAElD,MAAM,EAAE;EAChD,OAAOsH,UAAU,CAACE,YAAY,CAAC5F,MAAM,CAAC,EAAE1B,GAAG,EAAEgD,MAAM,EAAElD,MAAM,CAAC;AAC9D;AAEA,SAASyH,WAAWA,CAAEvH,GAAG,EAAE0B,MAAM,EAAEsB,MAAM,EAAElD,MAAM,EAAE;EACjD,OAAOsH,UAAU,CAACpD,aAAa,CAACtC,MAAM,CAAC,EAAE1B,GAAG,EAAEgD,MAAM,EAAElD,MAAM,CAAC;AAC/D;AAEA,SAAS0H,SAASA,CAAExH,GAAG,EAAE0B,MAAM,EAAEsB,MAAM,EAAElD,MAAM,EAAE;EAC/C,OAAOsH,UAAU,CAACK,cAAc,CAAC/F,MAAM,EAAE1B,GAAG,CAACF,MAAM,GAAGkD,MAAM,CAAC,EAAEhD,GAAG,EAAEgD,MAAM,EAAElD,MAAM,CAAC;AACrF;AAEAzB,MAAM,CAACe,SAAS,CAAC0C,KAAK,GAAG,SAASA,KAAKA,CAAEJ,MAAM,EAAEsB,MAAM,EAAElD,MAAM,EAAEyB,QAAQ,EAAE;EACzE;EACA,IAAIyB,MAAM,KAAKtD,SAAS,EAAE;IACxB6B,QAAQ,GAAG,MAAM;IACjBzB,MAAM,GAAG,IAAI,CAACA,MAAM;IACpBkD,MAAM,GAAG,CAAC;IACZ;EACA,CAAC,MAAM,IAAIlD,MAAM,KAAKJ,SAAS,IAAI,OAAOsD,MAAM,KAAK,QAAQ,EAAE;IAC7DzB,QAAQ,GAAGyB,MAAM;IACjBlD,MAAM,GAAG,IAAI,CAACA,MAAM;IACpBkD,MAAM,GAAG,CAAC;IACZ;EACA,CAAC,MAAM,IAAI0E,QAAQ,CAAC1E,MAAM,CAAC,EAAE;IAC3BA,MAAM,GAAGA,MAAM,KAAK,CAAC;IACrB,IAAI0E,QAAQ,CAAC5H,MAAM,CAAC,EAAE;MACpBA,MAAM,GAAGA,MAAM,KAAK,CAAC;MACrB,IAAIyB,QAAQ,KAAK7B,SAAS,EAAE6B,QAAQ,GAAG,MAAM;IAC/C,CAAC,MAAM;MACLA,QAAQ,GAAGzB,MAAM;MACjBA,MAAM,GAAGJ,SAAS;IACpB;EACF,CAAC,MAAM;IACL,MAAM,IAAIiI,KAAK,CACb,yEACF,CAAC;EACH;EAEA,MAAMb,SAAS,GAAG,IAAI,CAAChH,MAAM,GAAGkD,MAAM;EACtC,IAAIlD,MAAM,KAAKJ,SAAS,IAAII,MAAM,GAAGgH,SAAS,EAAEhH,MAAM,GAAGgH,SAAS;EAElE,IAAKpF,MAAM,CAAC5B,MAAM,GAAG,CAAC,KAAKA,MAAM,GAAG,CAAC,IAAIkD,MAAM,GAAG,CAAC,CAAC,IAAKA,MAAM,GAAG,IAAI,CAAClD,MAAM,EAAE;IAC7E,MAAM,IAAIC,UAAU,CAAC,wCAAwC,CAAC;EAChE;EAEA,IAAI,CAACwB,QAAQ,EAAEA,QAAQ,GAAG,MAAM;EAEhC,IAAIuC,WAAW,GAAG,KAAK;EACvB,SAAS;IACP,QAAQvC,QAAQ;MACd,KAAK,KAAK;QACR,OAAOqF,QAAQ,CAAC,IAAI,EAAElF,MAAM,EAAEsB,MAAM,EAAElD,MAAM,CAAC;MAE/C,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAOqH,SAAS,CAAC,IAAI,EAAEzF,MAAM,EAAEsB,MAAM,EAAElD,MAAM,CAAC;MAEhD,KAAK,OAAO;MACZ,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAOuH,UAAU,CAAC,IAAI,EAAE3F,MAAM,EAAEsB,MAAM,EAAElD,MAAM,CAAC;MAEjD,KAAK,QAAQ;QACX;QACA,OAAOyH,WAAW,CAAC,IAAI,EAAE7F,MAAM,EAAEsB,MAAM,EAAElD,MAAM,CAAC;MAElD,KAAK,MAAM;MACX,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,UAAU;QACb,OAAO0H,SAAS,CAAC,IAAI,EAAE9F,MAAM,EAAEsB,MAAM,EAAElD,MAAM,CAAC;MAEhD;QACE,IAAIgE,WAAW,EAAE,MAAM,IAAI3D,SAAS,CAAC,oBAAoB,GAAGoB,QAAQ,CAAC;QACrEA,QAAQ,GAAG,CAAC,EAAE,GAAGA,QAAQ,EAAE+B,WAAW,CAAC,CAAC;QACxCQ,WAAW,GAAG,IAAI;IACtB;EACF;AACF,CAAC;AAEDzF,MAAM,CAACe,SAAS,CAACwI,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EAC3C,OAAO;IACLpF,IAAI,EAAE,QAAQ;IACdG,IAAI,EAAEF,KAAK,CAACrD,SAAS,CAAC2C,KAAK,CAAC4B,IAAI,CAAC,IAAI,CAACkE,IAAI,IAAI,IAAI,EAAE,CAAC;EACvD,CAAC;AACH,CAAC;AAED,SAASrD,WAAWA,CAAExE,GAAG,EAAEkE,KAAK,EAAEC,GAAG,EAAE;EACrC,IAAID,KAAK,KAAK,CAAC,IAAIC,GAAG,KAAKnE,GAAG,CAACF,MAAM,EAAE;IACrC,OAAO/B,MAAM,CAAC+J,aAAa,CAAC9H,GAAG,CAAC;EAClC,CAAC,MAAM;IACL,OAAOjC,MAAM,CAAC+J,aAAa,CAAC9H,GAAG,CAAC+B,KAAK,CAACmC,KAAK,EAAEC,GAAG,CAAC,CAAC;EACpD;AACF;AAEA,SAASE,SAASA,CAAErE,GAAG,EAAEkE,KAAK,EAAEC,GAAG,EAAE;EACnCA,GAAG,GAAGhB,IAAI,CAACC,GAAG,CAACpD,GAAG,CAACF,MAAM,EAAEqE,GAAG,CAAC;EAC/B,MAAM4D,GAAG,GAAG,EAAE;EAEd,IAAI7F,CAAC,GAAGgC,KAAK;EACb,OAAOhC,CAAC,GAAGiC,GAAG,EAAE;IACd,MAAM6D,SAAS,GAAGhI,GAAG,CAACkC,CAAC,CAAC;IACxB,IAAI+F,SAAS,GAAG,IAAI;IACpB,IAAIC,gBAAgB,GAAIF,SAAS,GAAG,IAAI,GACpC,CAAC,GACAA,SAAS,GAAG,IAAI,GACb,CAAC,GACAA,SAAS,GAAG,IAAI,GACb,CAAC,GACD,CAAC;IAEb,IAAI9F,CAAC,GAAGgG,gBAAgB,IAAI/D,GAAG,EAAE;MAC/B,IAAIgE,UAAU,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa;MAEpD,QAAQJ,gBAAgB;QACtB,KAAK,CAAC;UACJ,IAAIF,SAAS,GAAG,IAAI,EAAE;YACpBC,SAAS,GAAGD,SAAS;UACvB;UACA;QACF,KAAK,CAAC;UACJG,UAAU,GAAGnI,GAAG,CAACkC,CAAC,GAAG,CAAC,CAAC;UACvB,IAAI,CAACiG,UAAU,GAAG,IAAI,MAAM,IAAI,EAAE;YAChCG,aAAa,GAAG,CAACN,SAAS,GAAG,IAAI,KAAK,GAAG,GAAIG,UAAU,GAAG,IAAK;YAC/D,IAAIG,aAAa,GAAG,IAAI,EAAE;cACxBL,SAAS,GAAGK,aAAa;YAC3B;UACF;UACA;QACF,KAAK,CAAC;UACJH,UAAU,GAAGnI,GAAG,CAACkC,CAAC,GAAG,CAAC,CAAC;UACvBkG,SAAS,GAAGpI,GAAG,CAACkC,CAAC,GAAG,CAAC,CAAC;UACtB,IAAI,CAACiG,UAAU,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,SAAS,GAAG,IAAI,MAAM,IAAI,EAAE;YAC/DE,aAAa,GAAG,CAACN,SAAS,GAAG,GAAG,KAAK,GAAG,GAAG,CAACG,UAAU,GAAG,IAAI,KAAK,GAAG,GAAIC,SAAS,GAAG,IAAK;YAC1F,IAAIE,aAAa,GAAG,KAAK,KAAKA,aAAa,GAAG,MAAM,IAAIA,aAAa,GAAG,MAAM,CAAC,EAAE;cAC/EL,SAAS,GAAGK,aAAa;YAC3B;UACF;UACA;QACF,KAAK,CAAC;UACJH,UAAU,GAAGnI,GAAG,CAACkC,CAAC,GAAG,CAAC,CAAC;UACvBkG,SAAS,GAAGpI,GAAG,CAACkC,CAAC,GAAG,CAAC,CAAC;UACtBmG,UAAU,GAAGrI,GAAG,CAACkC,CAAC,GAAG,CAAC,CAAC;UACvB,IAAI,CAACiG,UAAU,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,SAAS,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,UAAU,GAAG,IAAI,MAAM,IAAI,EAAE;YAC/FC,aAAa,GAAG,CAACN,SAAS,GAAG,GAAG,KAAK,IAAI,GAAG,CAACG,UAAU,GAAG,IAAI,KAAK,GAAG,GAAG,CAACC,SAAS,GAAG,IAAI,KAAK,GAAG,GAAIC,UAAU,GAAG,IAAK;YACxH,IAAIC,aAAa,GAAG,MAAM,IAAIA,aAAa,GAAG,QAAQ,EAAE;cACtDL,SAAS,GAAGK,aAAa;YAC3B;UACF;MACJ;IACF;IAEA,IAAIL,SAAS,KAAK,IAAI,EAAE;MACtB;MACA;MACAA,SAAS,GAAG,MAAM;MAClBC,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM,IAAID,SAAS,GAAG,MAAM,EAAE;MAC7B;MACAA,SAAS,IAAI,OAAO;MACpBF,GAAG,CAACQ,IAAI,CAACN,SAAS,KAAK,EAAE,GAAG,KAAK,GAAG,MAAM,CAAC;MAC3CA,SAAS,GAAG,MAAM,GAAGA,SAAS,GAAG,KAAK;IACxC;IAEAF,GAAG,CAACQ,IAAI,CAACN,SAAS,CAAC;IACnB/F,CAAC,IAAIgG,gBAAgB;EACvB;EAEA,OAAOM,qBAAqB,CAACT,GAAG,CAAC;AACnC;;AAEA;AACA;AACA;AACA,MAAMU,oBAAoB,GAAG,MAAM;AAEnC,SAASD,qBAAqBA,CAAEE,UAAU,EAAE;EAC1C,MAAMpG,GAAG,GAAGoG,UAAU,CAAC5I,MAAM;EAC7B,IAAIwC,GAAG,IAAImG,oBAAoB,EAAE;IAC/B,OAAOpF,MAAM,CAACsF,YAAY,CAAC3D,KAAK,CAAC3B,MAAM,EAAEqF,UAAU,CAAC,EAAC;EACvD;;EAEA;EACA,IAAIX,GAAG,GAAG,EAAE;EACZ,IAAI7F,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGI,GAAG,EAAE;IACdyF,GAAG,IAAI1E,MAAM,CAACsF,YAAY,CAAC3D,KAAK,CAC9B3B,MAAM,EACNqF,UAAU,CAAC3G,KAAK,CAACG,CAAC,EAAEA,CAAC,IAAIuG,oBAAoB,CAC/C,CAAC;EACH;EACA,OAAOV,GAAG;AACZ;AAEA,SAASzD,UAAUA,CAAEtE,GAAG,EAAEkE,KAAK,EAAEC,GAAG,EAAE;EACpC,IAAIyE,GAAG,GAAG,EAAE;EACZzE,GAAG,GAAGhB,IAAI,CAACC,GAAG,CAACpD,GAAG,CAACF,MAAM,EAAEqE,GAAG,CAAC;EAE/B,KAAK,IAAIjC,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,GAAGiC,GAAG,EAAE,EAAEjC,CAAC,EAAE;IAChC0G,GAAG,IAAIvF,MAAM,CAACsF,YAAY,CAAC3I,GAAG,CAACkC,CAAC,CAAC,GAAG,IAAI,CAAC;EAC3C;EACA,OAAO0G,GAAG;AACZ;AAEA,SAASrE,WAAWA,CAAEvE,GAAG,EAAEkE,KAAK,EAAEC,GAAG,EAAE;EACrC,IAAIyE,GAAG,GAAG,EAAE;EACZzE,GAAG,GAAGhB,IAAI,CAACC,GAAG,CAACpD,GAAG,CAACF,MAAM,EAAEqE,GAAG,CAAC;EAE/B,KAAK,IAAIjC,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,GAAGiC,GAAG,EAAE,EAAEjC,CAAC,EAAE;IAChC0G,GAAG,IAAIvF,MAAM,CAACsF,YAAY,CAAC3I,GAAG,CAACkC,CAAC,CAAC,CAAC;EACpC;EACA,OAAO0G,GAAG;AACZ;AAEA,SAASxE,QAAQA,CAAEpE,GAAG,EAAEkE,KAAK,EAAEC,GAAG,EAAE;EAClC,MAAM7B,GAAG,GAAGtC,GAAG,CAACF,MAAM;EAEtB,IAAI,CAACoE,KAAK,IAAIA,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC;EAClC,IAAI,CAACC,GAAG,IAAIA,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG7B,GAAG,EAAE6B,GAAG,GAAG7B,GAAG;EAE3C,IAAIuG,GAAG,GAAG,EAAE;EACZ,KAAK,IAAI3G,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,GAAGiC,GAAG,EAAE,EAAEjC,CAAC,EAAE;IAChC2G,GAAG,IAAIC,mBAAmB,CAAC9I,GAAG,CAACkC,CAAC,CAAC,CAAC;EACpC;EACA,OAAO2G,GAAG;AACZ;AAEA,SAASpE,YAAYA,CAAEzE,GAAG,EAAEkE,KAAK,EAAEC,GAAG,EAAE;EACtC,MAAM4E,KAAK,GAAG/I,GAAG,CAAC+B,KAAK,CAACmC,KAAK,EAAEC,GAAG,CAAC;EACnC,IAAI4D,GAAG,GAAG,EAAE;EACZ;EACA,KAAK,IAAI7F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6G,KAAK,CAACjJ,MAAM,GAAG,CAAC,EAAEoC,CAAC,IAAI,CAAC,EAAE;IAC5C6F,GAAG,IAAI1E,MAAM,CAACsF,YAAY,CAACI,KAAK,CAAC7G,CAAC,CAAC,GAAI6G,KAAK,CAAC7G,CAAC,GAAG,CAAC,CAAC,GAAG,GAAI,CAAC;EAC7D;EACA,OAAO6F,GAAG;AACZ;AAEA1J,MAAM,CAACe,SAAS,CAAC2C,KAAK,GAAG,SAASA,KAAKA,CAAEmC,KAAK,EAAEC,GAAG,EAAE;EACnD,MAAM7B,GAAG,GAAG,IAAI,CAACxC,MAAM;EACvBoE,KAAK,GAAG,CAAC,CAACA,KAAK;EACfC,GAAG,GAAGA,GAAG,KAAKzE,SAAS,GAAG4C,GAAG,GAAG,CAAC,CAAC6B,GAAG;EAErC,IAAID,KAAK,GAAG,CAAC,EAAE;IACbA,KAAK,IAAI5B,GAAG;IACZ,IAAI4B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC;EAC1B,CAAC,MAAM,IAAIA,KAAK,GAAG5B,GAAG,EAAE;IACtB4B,KAAK,GAAG5B,GAAG;EACb;EAEA,IAAI6B,GAAG,GAAG,CAAC,EAAE;IACXA,GAAG,IAAI7B,GAAG;IACV,IAAI6B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC;EACtB,CAAC,MAAM,IAAIA,GAAG,GAAG7B,GAAG,EAAE;IACpB6B,GAAG,GAAG7B,GAAG;EACX;EAEA,IAAI6B,GAAG,GAAGD,KAAK,EAAEC,GAAG,GAAGD,KAAK;EAE5B,MAAM8E,MAAM,GAAG,IAAI,CAACC,QAAQ,CAAC/E,KAAK,EAAEC,GAAG,CAAC;EACxC;EACAjF,MAAM,CAACC,cAAc,CAAC6J,MAAM,EAAE3K,MAAM,CAACe,SAAS,CAAC;EAE/C,OAAO4J,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA,SAASE,WAAWA,CAAElG,MAAM,EAAEmG,GAAG,EAAErJ,MAAM,EAAE;EACzC,IAAKkD,MAAM,GAAG,CAAC,KAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIjD,UAAU,CAAC,oBAAoB,CAAC;EAChF,IAAIiD,MAAM,GAAGmG,GAAG,GAAGrJ,MAAM,EAAE,MAAM,IAAIC,UAAU,CAAC,uCAAuC,CAAC;AAC1F;AAEA1B,MAAM,CAACe,SAAS,CAACgK,UAAU,GAC3B/K,MAAM,CAACe,SAAS,CAACiK,UAAU,GAAG,SAASA,UAAUA,CAAErG,MAAM,EAAEpB,UAAU,EAAE0H,QAAQ,EAAE;EAC/EtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBpB,UAAU,GAAGA,UAAU,KAAK,CAAC;EAC7B,IAAI,CAAC0H,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAEpB,UAAU,EAAE,IAAI,CAAC9B,MAAM,CAAC;EAE3D,IAAIgG,GAAG,GAAG,IAAI,CAAC9C,MAAM,CAAC;EACtB,IAAIuG,GAAG,GAAG,CAAC;EACX,IAAIrH,CAAC,GAAG,CAAC;EACT,OAAO,EAAEA,CAAC,GAAGN,UAAU,KAAK2H,GAAG,IAAI,KAAK,CAAC,EAAE;IACzCzD,GAAG,IAAI,IAAI,CAAC9C,MAAM,GAAGd,CAAC,CAAC,GAAGqH,GAAG;EAC/B;EAEA,OAAOzD,GAAG;AACZ,CAAC;AAEDzH,MAAM,CAACe,SAAS,CAACoK,UAAU,GAC3BnL,MAAM,CAACe,SAAS,CAACqK,UAAU,GAAG,SAASA,UAAUA,CAAEzG,MAAM,EAAEpB,UAAU,EAAE0H,QAAQ,EAAE;EAC/EtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBpB,UAAU,GAAGA,UAAU,KAAK,CAAC;EAC7B,IAAI,CAAC0H,QAAQ,EAAE;IACbJ,WAAW,CAAClG,MAAM,EAAEpB,UAAU,EAAE,IAAI,CAAC9B,MAAM,CAAC;EAC9C;EAEA,IAAIgG,GAAG,GAAG,IAAI,CAAC9C,MAAM,GAAG,EAAEpB,UAAU,CAAC;EACrC,IAAI2H,GAAG,GAAG,CAAC;EACX,OAAO3H,UAAU,GAAG,CAAC,KAAK2H,GAAG,IAAI,KAAK,CAAC,EAAE;IACvCzD,GAAG,IAAI,IAAI,CAAC9C,MAAM,GAAG,EAAEpB,UAAU,CAAC,GAAG2H,GAAG;EAC1C;EAEA,OAAOzD,GAAG;AACZ,CAAC;AAEDzH,MAAM,CAACe,SAAS,CAACsK,SAAS,GAC1BrL,MAAM,CAACe,SAAS,CAACuK,SAAS,GAAG,SAASA,SAASA,CAAE3G,MAAM,EAAEsG,QAAQ,EAAE;EACjEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,OAAO,IAAI,CAACkD,MAAM,CAAC;AACrB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACwK,YAAY,GAC7BvL,MAAM,CAACe,SAAS,CAACyK,YAAY,GAAG,SAASA,YAAYA,CAAE7G,MAAM,EAAEsG,QAAQ,EAAE;EACvEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,OAAO,IAAI,CAACkD,MAAM,CAAC,GAAI,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE;AAC/C,CAAC;AAED3E,MAAM,CAACe,SAAS,CAAC0K,YAAY,GAC7BzL,MAAM,CAACe,SAAS,CAACmH,YAAY,GAAG,SAASA,YAAYA,CAAEvD,MAAM,EAAEsG,QAAQ,EAAE;EACvEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,OAAQ,IAAI,CAACkD,MAAM,CAAC,IAAI,CAAC,GAAI,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC;AAC/C,CAAC;AAED3E,MAAM,CAACe,SAAS,CAAC2K,YAAY,GAC7B1L,MAAM,CAACe,SAAS,CAAC4K,YAAY,GAAG,SAASA,YAAYA,CAAEhH,MAAM,EAAEsG,QAAQ,EAAE;EACvEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAElD,OAAO,CAAE,IAAI,CAACkD,MAAM,CAAC,GAChB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,IACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,GAAG,SAAU;AACpC,CAAC;AAED3E,MAAM,CAACe,SAAS,CAAC6K,YAAY,GAC7B5L,MAAM,CAACe,SAAS,CAAC8K,YAAY,GAAG,SAASA,YAAYA,CAAElH,MAAM,EAAEsG,QAAQ,EAAE;EACvEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAElD,OAAQ,IAAI,CAACkD,MAAM,CAAC,GAAG,SAAS,IAC5B,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,CAAC;AACrB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAAC+K,eAAe,GAAGC,kBAAkB,CAAC,SAASD,eAAeA,CAAEnH,MAAM,EAAE;EACtFA,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBqH,cAAc,CAACrH,MAAM,EAAE,QAAQ,CAAC;EAChC,MAAMsH,KAAK,GAAG,IAAI,CAACtH,MAAM,CAAC;EAC1B,MAAMuH,IAAI,GAAG,IAAI,CAACvH,MAAM,GAAG,CAAC,CAAC;EAC7B,IAAIsH,KAAK,KAAK5K,SAAS,IAAI6K,IAAI,KAAK7K,SAAS,EAAE;IAC7C8K,WAAW,CAACxH,MAAM,EAAE,IAAI,CAAClD,MAAM,GAAG,CAAC,CAAC;EACtC;EAEA,MAAM2K,EAAE,GAAGH,KAAK,GACd,IAAI,CAAC,EAAEtH,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GACvB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GACxB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;EAE1B,MAAM0H,EAAE,GAAG,IAAI,CAAC,EAAE1H,MAAM,CAAC,GACvB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GACvB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GACxBuH,IAAI,GAAG,CAAC,IAAI,EAAE;EAEhB,OAAOI,MAAM,CAACF,EAAE,CAAC,IAAIE,MAAM,CAACD,EAAE,CAAC,IAAIC,MAAM,CAAC,EAAE,CAAC,CAAC;AAChD,CAAC,CAAC;AAEFtM,MAAM,CAACe,SAAS,CAACwL,eAAe,GAAGR,kBAAkB,CAAC,SAASQ,eAAeA,CAAE5H,MAAM,EAAE;EACtFA,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBqH,cAAc,CAACrH,MAAM,EAAE,QAAQ,CAAC;EAChC,MAAMsH,KAAK,GAAG,IAAI,CAACtH,MAAM,CAAC;EAC1B,MAAMuH,IAAI,GAAG,IAAI,CAACvH,MAAM,GAAG,CAAC,CAAC;EAC7B,IAAIsH,KAAK,KAAK5K,SAAS,IAAI6K,IAAI,KAAK7K,SAAS,EAAE;IAC7C8K,WAAW,CAACxH,MAAM,EAAE,IAAI,CAAClD,MAAM,GAAG,CAAC,CAAC;EACtC;EAEA,MAAM4K,EAAE,GAAGJ,KAAK,GAAG,CAAC,IAAI,EAAE,GACxB,IAAI,CAAC,EAAEtH,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GACxB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GACvB,IAAI,CAAC,EAAEA,MAAM,CAAC;EAEhB,MAAMyH,EAAE,GAAG,IAAI,CAAC,EAAEzH,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GACjC,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GACxB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GACvBuH,IAAI;EAEN,OAAO,CAACI,MAAM,CAACD,EAAE,CAAC,IAAIC,MAAM,CAAC,EAAE,CAAC,IAAIA,MAAM,CAACF,EAAE,CAAC;AAChD,CAAC,CAAC;AAEFpM,MAAM,CAACe,SAAS,CAACyL,SAAS,GAAG,SAASA,SAASA,CAAE7H,MAAM,EAAEpB,UAAU,EAAE0H,QAAQ,EAAE;EAC7EtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBpB,UAAU,GAAGA,UAAU,KAAK,CAAC;EAC7B,IAAI,CAAC0H,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAEpB,UAAU,EAAE,IAAI,CAAC9B,MAAM,CAAC;EAE3D,IAAIgG,GAAG,GAAG,IAAI,CAAC9C,MAAM,CAAC;EACtB,IAAIuG,GAAG,GAAG,CAAC;EACX,IAAIrH,CAAC,GAAG,CAAC;EACT,OAAO,EAAEA,CAAC,GAAGN,UAAU,KAAK2H,GAAG,IAAI,KAAK,CAAC,EAAE;IACzCzD,GAAG,IAAI,IAAI,CAAC9C,MAAM,GAAGd,CAAC,CAAC,GAAGqH,GAAG;EAC/B;EACAA,GAAG,IAAI,IAAI;EAEX,IAAIzD,GAAG,IAAIyD,GAAG,EAAEzD,GAAG,IAAI3C,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGlJ,UAAU,CAAC;EAElD,OAAOkE,GAAG;AACZ,CAAC;AAEDzH,MAAM,CAACe,SAAS,CAAC2L,SAAS,GAAG,SAASA,SAASA,CAAE/H,MAAM,EAAEpB,UAAU,EAAE0H,QAAQ,EAAE;EAC7EtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBpB,UAAU,GAAGA,UAAU,KAAK,CAAC;EAC7B,IAAI,CAAC0H,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAEpB,UAAU,EAAE,IAAI,CAAC9B,MAAM,CAAC;EAE3D,IAAIoC,CAAC,GAAGN,UAAU;EAClB,IAAI2H,GAAG,GAAG,CAAC;EACX,IAAIzD,GAAG,GAAG,IAAI,CAAC9C,MAAM,GAAG,EAAEd,CAAC,CAAC;EAC5B,OAAOA,CAAC,GAAG,CAAC,KAAKqH,GAAG,IAAI,KAAK,CAAC,EAAE;IAC9BzD,GAAG,IAAI,IAAI,CAAC9C,MAAM,GAAG,EAAEd,CAAC,CAAC,GAAGqH,GAAG;EACjC;EACAA,GAAG,IAAI,IAAI;EAEX,IAAIzD,GAAG,IAAIyD,GAAG,EAAEzD,GAAG,IAAI3C,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGlJ,UAAU,CAAC;EAElD,OAAOkE,GAAG;AACZ,CAAC;AAEDzH,MAAM,CAACe,SAAS,CAAC4L,QAAQ,GAAG,SAASA,QAAQA,CAAEhI,MAAM,EAAEsG,QAAQ,EAAE;EAC/DtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,IAAI,EAAE,IAAI,CAACkD,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,OAAQ,IAAI,CAACA,MAAM,CAAC;EAChD,OAAQ,CAAC,IAAI,GAAG,IAAI,CAACA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC;AAED3E,MAAM,CAACe,SAAS,CAAC6L,WAAW,GAAG,SAASA,WAAWA,CAAEjI,MAAM,EAAEsG,QAAQ,EAAE;EACrEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,MAAMgG,GAAG,GAAG,IAAI,CAAC9C,MAAM,CAAC,GAAI,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE;EAClD,OAAQ8C,GAAG,GAAG,MAAM,GAAIA,GAAG,GAAG,UAAU,GAAGA,GAAG;AAChD,CAAC;AAEDzH,MAAM,CAACe,SAAS,CAAC8L,WAAW,GAAG,SAASA,WAAWA,CAAElI,MAAM,EAAEsG,QAAQ,EAAE;EACrEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,MAAMgG,GAAG,GAAG,IAAI,CAAC9C,MAAM,GAAG,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,IAAI,CAAE;EAClD,OAAQ8C,GAAG,GAAG,MAAM,GAAIA,GAAG,GAAG,UAAU,GAAGA,GAAG;AAChD,CAAC;AAEDzH,MAAM,CAACe,SAAS,CAAC+L,WAAW,GAAG,SAASA,WAAWA,CAAEnI,MAAM,EAAEsG,QAAQ,EAAE;EACrEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAElD,OAAQ,IAAI,CAACkD,MAAM,CAAC,GACjB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG;AAC5B,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACgM,WAAW,GAAG,SAASA,WAAWA,CAAEpI,MAAM,EAAEsG,QAAQ,EAAE;EACrEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAElD,OAAQ,IAAI,CAACkD,MAAM,CAAC,IAAI,EAAE,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACvB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAE;AACtB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACiM,cAAc,GAAGjB,kBAAkB,CAAC,SAASiB,cAAcA,CAAErI,MAAM,EAAE;EACpFA,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBqH,cAAc,CAACrH,MAAM,EAAE,QAAQ,CAAC;EAChC,MAAMsH,KAAK,GAAG,IAAI,CAACtH,MAAM,CAAC;EAC1B,MAAMuH,IAAI,GAAG,IAAI,CAACvH,MAAM,GAAG,CAAC,CAAC;EAC7B,IAAIsH,KAAK,KAAK5K,SAAS,IAAI6K,IAAI,KAAK7K,SAAS,EAAE;IAC7C8K,WAAW,CAACxH,MAAM,EAAE,IAAI,CAAClD,MAAM,GAAG,CAAC,CAAC;EACtC;EAEA,MAAMgG,GAAG,GAAG,IAAI,CAAC9C,MAAM,GAAG,CAAC,CAAC,GAC1B,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GACzB,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IACzBuH,IAAI,IAAI,EAAE,CAAC,EAAC;;EAEf,OAAO,CAACI,MAAM,CAAC7E,GAAG,CAAC,IAAI6E,MAAM,CAAC,EAAE,CAAC,IAC/BA,MAAM,CAACL,KAAK,GACZ,IAAI,CAAC,EAAEtH,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GACvB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GACxB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AAC7B,CAAC,CAAC;AAEF3E,MAAM,CAACe,SAAS,CAACkM,cAAc,GAAGlB,kBAAkB,CAAC,SAASkB,cAAcA,CAAEtI,MAAM,EAAE;EACpFA,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBqH,cAAc,CAACrH,MAAM,EAAE,QAAQ,CAAC;EAChC,MAAMsH,KAAK,GAAG,IAAI,CAACtH,MAAM,CAAC;EAC1B,MAAMuH,IAAI,GAAG,IAAI,CAACvH,MAAM,GAAG,CAAC,CAAC;EAC7B,IAAIsH,KAAK,KAAK5K,SAAS,IAAI6K,IAAI,KAAK7K,SAAS,EAAE;IAC7C8K,WAAW,CAACxH,MAAM,EAAE,IAAI,CAAClD,MAAM,GAAG,CAAC,CAAC;EACtC;EAEA,MAAMgG,GAAG,GAAG,CAACwE,KAAK,IAAI,EAAE;EAAI;EAC1B,IAAI,CAAC,EAAEtH,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GACxB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GACvB,IAAI,CAAC,EAAEA,MAAM,CAAC;EAEhB,OAAO,CAAC2H,MAAM,CAAC7E,GAAG,CAAC,IAAI6E,MAAM,CAAC,EAAE,CAAC,IAC/BA,MAAM,CAAC,IAAI,CAAC,EAAE3H,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GAC/B,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GACxB,IAAI,CAAC,EAAEA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GACvBuH,IAAI,CAAC;AACT,CAAC,CAAC;AAEFlM,MAAM,CAACe,SAAS,CAACmM,WAAW,GAAG,SAASA,WAAWA,CAAEvI,MAAM,EAAEsG,QAAQ,EAAE;EACrEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,OAAO7B,OAAO,CAACqI,IAAI,CAAC,IAAI,EAAEtD,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;AAChD,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACoM,WAAW,GAAG,SAASA,WAAWA,CAAExI,MAAM,EAAEsG,QAAQ,EAAE;EACrEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,OAAO7B,OAAO,CAACqI,IAAI,CAAC,IAAI,EAAEtD,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACqM,YAAY,GAAG,SAASA,YAAYA,CAAEzI,MAAM,EAAEsG,QAAQ,EAAE;EACvEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,OAAO7B,OAAO,CAACqI,IAAI,CAAC,IAAI,EAAEtD,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;AAChD,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACsM,YAAY,GAAG,SAASA,YAAYA,CAAE1I,MAAM,EAAEsG,QAAQ,EAAE;EACvEtG,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEJ,WAAW,CAAClG,MAAM,EAAE,CAAC,EAAE,IAAI,CAAClD,MAAM,CAAC;EAClD,OAAO7B,OAAO,CAACqI,IAAI,CAAC,IAAI,EAAEtD,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,CAAC;AAED,SAAS2I,QAAQA,CAAE3L,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAEmG,GAAG,EAAE9D,GAAG,EAAEjC,GAAG,EAAE;EACpD,IAAI,CAAC/E,MAAM,CAACoB,QAAQ,CAACO,GAAG,CAAC,EAAE,MAAM,IAAIG,SAAS,CAAC,6CAA6C,CAAC;EAC7F,IAAII,KAAK,GAAG8E,GAAG,IAAI9E,KAAK,GAAG6C,GAAG,EAAE,MAAM,IAAIrD,UAAU,CAAC,mCAAmC,CAAC;EACzF,IAAIiD,MAAM,GAAGmG,GAAG,GAAGnJ,GAAG,CAACF,MAAM,EAAE,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;AAC3E;AAEA1B,MAAM,CAACe,SAAS,CAACwM,WAAW,GAC5BvN,MAAM,CAACe,SAAS,CAACyM,WAAW,GAAG,SAASA,WAAWA,CAAEtL,KAAK,EAAEyC,MAAM,EAAEpB,UAAU,EAAE0H,QAAQ,EAAE;EACxF/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBpB,UAAU,GAAGA,UAAU,KAAK,CAAC;EAC7B,IAAI,CAAC0H,QAAQ,EAAE;IACb,MAAMwC,QAAQ,GAAG3I,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGlJ,UAAU,CAAC,GAAG,CAAC;IAChD+J,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAEpB,UAAU,EAAEkK,QAAQ,EAAE,CAAC,CAAC;EACxD;EAEA,IAAIvC,GAAG,GAAG,CAAC;EACX,IAAIrH,CAAC,GAAG,CAAC;EACT,IAAI,CAACc,MAAM,CAAC,GAAGzC,KAAK,GAAG,IAAI;EAC3B,OAAO,EAAE2B,CAAC,GAAGN,UAAU,KAAK2H,GAAG,IAAI,KAAK,CAAC,EAAE;IACzC,IAAI,CAACvG,MAAM,GAAGd,CAAC,CAAC,GAAI3B,KAAK,GAAGgJ,GAAG,GAAI,IAAI;EACzC;EAEA,OAAOvG,MAAM,GAAGpB,UAAU;AAC5B,CAAC;AAEDvD,MAAM,CAACe,SAAS,CAAC2M,WAAW,GAC5B1N,MAAM,CAACe,SAAS,CAAC4M,WAAW,GAAG,SAASA,WAAWA,CAAEzL,KAAK,EAAEyC,MAAM,EAAEpB,UAAU,EAAE0H,QAAQ,EAAE;EACxF/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrBpB,UAAU,GAAGA,UAAU,KAAK,CAAC;EAC7B,IAAI,CAAC0H,QAAQ,EAAE;IACb,MAAMwC,QAAQ,GAAG3I,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGlJ,UAAU,CAAC,GAAG,CAAC;IAChD+J,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAEpB,UAAU,EAAEkK,QAAQ,EAAE,CAAC,CAAC;EACxD;EAEA,IAAI5J,CAAC,GAAGN,UAAU,GAAG,CAAC;EACtB,IAAI2H,GAAG,GAAG,CAAC;EACX,IAAI,CAACvG,MAAM,GAAGd,CAAC,CAAC,GAAG3B,KAAK,GAAG,IAAI;EAC/B,OAAO,EAAE2B,CAAC,IAAI,CAAC,KAAKqH,GAAG,IAAI,KAAK,CAAC,EAAE;IACjC,IAAI,CAACvG,MAAM,GAAGd,CAAC,CAAC,GAAI3B,KAAK,GAAGgJ,GAAG,GAAI,IAAI;EACzC;EAEA,OAAOvG,MAAM,GAAGpB,UAAU;AAC5B,CAAC;AAEDvD,MAAM,CAACe,SAAS,CAAC6M,UAAU,GAC3B5N,MAAM,CAACe,SAAS,CAAC8M,UAAU,GAAG,SAASA,UAAUA,CAAE3L,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAC1E/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;EACxD,IAAI,CAACA,MAAM,CAAC,GAAIzC,KAAK,GAAG,IAAK;EAC7B,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAAC+M,aAAa,GAC9B9N,MAAM,CAACe,SAAS,CAACgN,aAAa,GAAG,SAASA,aAAaA,CAAE7L,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAChF/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;EAC1D,IAAI,CAACA,MAAM,CAAC,GAAIzC,KAAK,GAAG,IAAK;EAC7B,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,CAAE;EAChC,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACiN,aAAa,GAC9BhO,MAAM,CAACe,SAAS,CAACkN,aAAa,GAAG,SAASA,aAAaA,CAAE/L,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAChF/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;EAC1D,IAAI,CAACA,MAAM,CAAC,GAAIzC,KAAK,KAAK,CAAE;EAC5B,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,GAAG,IAAK;EACjC,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACmN,aAAa,GAC9BlO,MAAM,CAACe,SAAS,CAACoN,aAAa,GAAG,SAASA,aAAaA,CAAEjM,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAChF/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;EAC9D,IAAI,CAACA,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,EAAG;EACjC,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,EAAG;EACjC,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,CAAE;EAChC,IAAI,CAACyC,MAAM,CAAC,GAAIzC,KAAK,GAAG,IAAK;EAC7B,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACqN,aAAa,GAC9BpO,MAAM,CAACe,SAAS,CAACsN,aAAa,GAAG,SAASA,aAAaA,CAAEnM,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAChF/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;EAC9D,IAAI,CAACA,MAAM,CAAC,GAAIzC,KAAK,KAAK,EAAG;EAC7B,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,EAAG;EACjC,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,CAAE;EAChC,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,GAAG,IAAK;EACjC,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED,SAAS2J,cAAcA,CAAE3M,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAEI,GAAG,EAAEiC,GAAG,EAAE;EACrDuH,UAAU,CAACrM,KAAK,EAAE6C,GAAG,EAAEiC,GAAG,EAAErF,GAAG,EAAEgD,MAAM,EAAE,CAAC,CAAC;EAE3C,IAAIyH,EAAE,GAAG5D,MAAM,CAACtG,KAAK,GAAGoK,MAAM,CAAC,UAAU,CAAC,CAAC;EAC3C3K,GAAG,CAACgD,MAAM,EAAE,CAAC,GAAGyH,EAAE;EAClBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZzK,GAAG,CAACgD,MAAM,EAAE,CAAC,GAAGyH,EAAE;EAClBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZzK,GAAG,CAACgD,MAAM,EAAE,CAAC,GAAGyH,EAAE;EAClBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZzK,GAAG,CAACgD,MAAM,EAAE,CAAC,GAAGyH,EAAE;EAClB,IAAIC,EAAE,GAAG7D,MAAM,CAACtG,KAAK,IAAIoK,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,UAAU,CAAC,CAAC;EACzD3K,GAAG,CAACgD,MAAM,EAAE,CAAC,GAAG0H,EAAE;EAClBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZ1K,GAAG,CAACgD,MAAM,EAAE,CAAC,GAAG0H,EAAE;EAClBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZ1K,GAAG,CAACgD,MAAM,EAAE,CAAC,GAAG0H,EAAE;EAClBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZ1K,GAAG,CAACgD,MAAM,EAAE,CAAC,GAAG0H,EAAE;EAClB,OAAO1H,MAAM;AACf;AAEA,SAAS6J,cAAcA,CAAE7M,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAEI,GAAG,EAAEiC,GAAG,EAAE;EACrDuH,UAAU,CAACrM,KAAK,EAAE6C,GAAG,EAAEiC,GAAG,EAAErF,GAAG,EAAEgD,MAAM,EAAE,CAAC,CAAC;EAE3C,IAAIyH,EAAE,GAAG5D,MAAM,CAACtG,KAAK,GAAGoK,MAAM,CAAC,UAAU,CAAC,CAAC;EAC3C3K,GAAG,CAACgD,MAAM,GAAG,CAAC,CAAC,GAAGyH,EAAE;EACpBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZzK,GAAG,CAACgD,MAAM,GAAG,CAAC,CAAC,GAAGyH,EAAE;EACpBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZzK,GAAG,CAACgD,MAAM,GAAG,CAAC,CAAC,GAAGyH,EAAE;EACpBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZzK,GAAG,CAACgD,MAAM,GAAG,CAAC,CAAC,GAAGyH,EAAE;EACpB,IAAIC,EAAE,GAAG7D,MAAM,CAACtG,KAAK,IAAIoK,MAAM,CAAC,EAAE,CAAC,GAAGA,MAAM,CAAC,UAAU,CAAC,CAAC;EACzD3K,GAAG,CAACgD,MAAM,GAAG,CAAC,CAAC,GAAG0H,EAAE;EACpBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZ1K,GAAG,CAACgD,MAAM,GAAG,CAAC,CAAC,GAAG0H,EAAE;EACpBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZ1K,GAAG,CAACgD,MAAM,GAAG,CAAC,CAAC,GAAG0H,EAAE;EACpBA,EAAE,GAAGA,EAAE,IAAI,CAAC;EACZ1K,GAAG,CAACgD,MAAM,CAAC,GAAG0H,EAAE;EAChB,OAAO1H,MAAM,GAAG,CAAC;AACnB;AAEA3E,MAAM,CAACe,SAAS,CAAC0N,gBAAgB,GAAG1C,kBAAkB,CAAC,SAAS0C,gBAAgBA,CAAEvM,KAAK,EAAEyC,MAAM,GAAG,CAAC,EAAE;EACnG,OAAO2J,cAAc,CAAC,IAAI,EAAEpM,KAAK,EAAEyC,MAAM,EAAE2H,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACrF,CAAC,CAAC;AAEFtM,MAAM,CAACe,SAAS,CAAC2N,gBAAgB,GAAG3C,kBAAkB,CAAC,SAAS2C,gBAAgBA,CAAExM,KAAK,EAAEyC,MAAM,GAAG,CAAC,EAAE;EACnG,OAAO6J,cAAc,CAAC,IAAI,EAAEtM,KAAK,EAAEyC,MAAM,EAAE2H,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACrF,CAAC,CAAC;AAEFtM,MAAM,CAACe,SAAS,CAAC4N,UAAU,GAAG,SAASA,UAAUA,CAAEzM,KAAK,EAAEyC,MAAM,EAAEpB,UAAU,EAAE0H,QAAQ,EAAE;EACtF/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAE;IACb,MAAM2D,KAAK,GAAG9J,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAG,CAAC,GAAGlJ,UAAU,GAAI,CAAC,CAAC;IAE/C+J,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAEpB,UAAU,EAAEqL,KAAK,GAAG,CAAC,EAAE,CAACA,KAAK,CAAC;EAC9D;EAEA,IAAI/K,CAAC,GAAG,CAAC;EACT,IAAIqH,GAAG,GAAG,CAAC;EACX,IAAI2D,GAAG,GAAG,CAAC;EACX,IAAI,CAAClK,MAAM,CAAC,GAAGzC,KAAK,GAAG,IAAI;EAC3B,OAAO,EAAE2B,CAAC,GAAGN,UAAU,KAAK2H,GAAG,IAAI,KAAK,CAAC,EAAE;IACzC,IAAIhJ,KAAK,GAAG,CAAC,IAAI2M,GAAG,KAAK,CAAC,IAAI,IAAI,CAAClK,MAAM,GAAGd,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MACxDgL,GAAG,GAAG,CAAC;IACT;IACA,IAAI,CAAClK,MAAM,GAAGd,CAAC,CAAC,GAAG,CAAE3B,KAAK,GAAGgJ,GAAG,IAAK,CAAC,IAAI2D,GAAG,GAAG,IAAI;EACtD;EAEA,OAAOlK,MAAM,GAAGpB,UAAU;AAC5B,CAAC;AAEDvD,MAAM,CAACe,SAAS,CAAC+N,UAAU,GAAG,SAASA,UAAUA,CAAE5M,KAAK,EAAEyC,MAAM,EAAEpB,UAAU,EAAE0H,QAAQ,EAAE;EACtF/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAE;IACb,MAAM2D,KAAK,GAAG9J,IAAI,CAAC2H,GAAG,CAAC,CAAC,EAAG,CAAC,GAAGlJ,UAAU,GAAI,CAAC,CAAC;IAE/C+J,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAEpB,UAAU,EAAEqL,KAAK,GAAG,CAAC,EAAE,CAACA,KAAK,CAAC;EAC9D;EAEA,IAAI/K,CAAC,GAAGN,UAAU,GAAG,CAAC;EACtB,IAAI2H,GAAG,GAAG,CAAC;EACX,IAAI2D,GAAG,GAAG,CAAC;EACX,IAAI,CAAClK,MAAM,GAAGd,CAAC,CAAC,GAAG3B,KAAK,GAAG,IAAI;EAC/B,OAAO,EAAE2B,CAAC,IAAI,CAAC,KAAKqH,GAAG,IAAI,KAAK,CAAC,EAAE;IACjC,IAAIhJ,KAAK,GAAG,CAAC,IAAI2M,GAAG,KAAK,CAAC,IAAI,IAAI,CAAClK,MAAM,GAAGd,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MACxDgL,GAAG,GAAG,CAAC;IACT;IACA,IAAI,CAAClK,MAAM,GAAGd,CAAC,CAAC,GAAG,CAAE3B,KAAK,GAAGgJ,GAAG,IAAK,CAAC,IAAI2D,GAAG,GAAG,IAAI;EACtD;EAEA,OAAOlK,MAAM,GAAGpB,UAAU;AAC5B,CAAC;AAEDvD,MAAM,CAACe,SAAS,CAACgO,SAAS,GAAG,SAASA,SAASA,CAAE7M,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EACxE/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC;EAC5D,IAAIzC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,GAAGA,KAAK,GAAG,CAAC;EACvC,IAAI,CAACyC,MAAM,CAAC,GAAIzC,KAAK,GAAG,IAAK;EAC7B,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACiO,YAAY,GAAG,SAASA,YAAYA,CAAE9M,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAC9E/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC;EAChE,IAAI,CAACA,MAAM,CAAC,GAAIzC,KAAK,GAAG,IAAK;EAC7B,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,CAAE;EAChC,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACkO,YAAY,GAAG,SAASA,YAAYA,CAAE/M,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAC9E/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC;EAChE,IAAI,CAACA,MAAM,CAAC,GAAIzC,KAAK,KAAK,CAAE;EAC5B,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,GAAG,IAAK;EACjC,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACmO,YAAY,GAAG,SAASA,YAAYA,CAAEhN,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAC9E/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,UAAU,CAAC;EACxE,IAAI,CAACA,MAAM,CAAC,GAAIzC,KAAK,GAAG,IAAK;EAC7B,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,CAAE;EAChC,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,EAAG;EACjC,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,EAAG;EACjC,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACoO,YAAY,GAAG,SAASA,YAAYA,CAAEjN,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAC9E/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAEqC,QAAQ,CAAC,IAAI,EAAEpL,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,UAAU,CAAC;EACxE,IAAIzC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,UAAU,GAAGA,KAAK,GAAG,CAAC;EAC7C,IAAI,CAACyC,MAAM,CAAC,GAAIzC,KAAK,KAAK,EAAG;EAC7B,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,EAAG;EACjC,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,KAAK,CAAE;EAChC,IAAI,CAACyC,MAAM,GAAG,CAAC,CAAC,GAAIzC,KAAK,GAAG,IAAK;EACjC,OAAOyC,MAAM,GAAG,CAAC;AACnB,CAAC;AAED3E,MAAM,CAACe,SAAS,CAACqO,eAAe,GAAGrD,kBAAkB,CAAC,SAASqD,eAAeA,CAAElN,KAAK,EAAEyC,MAAM,GAAG,CAAC,EAAE;EACjG,OAAO2J,cAAc,CAAC,IAAI,EAAEpM,KAAK,EAAEyC,MAAM,EAAE,CAAC2H,MAAM,CAAC,oBAAoB,CAAC,EAAEA,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzG,CAAC,CAAC;AAEFtM,MAAM,CAACe,SAAS,CAACsO,eAAe,GAAGtD,kBAAkB,CAAC,SAASsD,eAAeA,CAAEnN,KAAK,EAAEyC,MAAM,GAAG,CAAC,EAAE;EACjG,OAAO6J,cAAc,CAAC,IAAI,EAAEtM,KAAK,EAAEyC,MAAM,EAAE,CAAC2H,MAAM,CAAC,oBAAoB,CAAC,EAAEA,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzG,CAAC,CAAC;AAEF,SAASgD,YAAYA,CAAE3N,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAEmG,GAAG,EAAE9D,GAAG,EAAEjC,GAAG,EAAE;EACxD,IAAIJ,MAAM,GAAGmG,GAAG,GAAGnJ,GAAG,CAACF,MAAM,EAAE,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EACzE,IAAIiD,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIjD,UAAU,CAAC,oBAAoB,CAAC;AAC5D;AAEA,SAAS6N,UAAUA,CAAE5N,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAE6K,YAAY,EAAEvE,QAAQ,EAAE;EAC/D/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAE;IACbqE,YAAY,CAAC3N,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,CAAC,sBAAsB,CAAC;EACtF;EACA/E,OAAO,CAAC6D,KAAK,CAAC9B,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAE6K,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC;EACtD,OAAO7K,MAAM,GAAG,CAAC;AACnB;AAEA3E,MAAM,CAACe,SAAS,CAAC0O,YAAY,GAAG,SAASA,YAAYA,CAAEvN,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAC9E,OAAOsE,UAAU,CAAC,IAAI,EAAErN,KAAK,EAAEyC,MAAM,EAAE,IAAI,EAAEsG,QAAQ,CAAC;AACxD,CAAC;AAEDjL,MAAM,CAACe,SAAS,CAAC2O,YAAY,GAAG,SAASA,YAAYA,CAAExN,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAC9E,OAAOsE,UAAU,CAAC,IAAI,EAAErN,KAAK,EAAEyC,MAAM,EAAE,KAAK,EAAEsG,QAAQ,CAAC;AACzD,CAAC;AAED,SAAS0E,WAAWA,CAAEhO,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAE6K,YAAY,EAAEvE,QAAQ,EAAE;EAChE/I,KAAK,GAAG,CAACA,KAAK;EACdyC,MAAM,GAAGA,MAAM,KAAK,CAAC;EACrB,IAAI,CAACsG,QAAQ,EAAE;IACbqE,YAAY,CAAC3N,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAE,CAAC,EAAE,uBAAuB,EAAE,CAAC,uBAAuB,CAAC;EACxF;EACA/E,OAAO,CAAC6D,KAAK,CAAC9B,GAAG,EAAEO,KAAK,EAAEyC,MAAM,EAAE6K,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC;EACtD,OAAO7K,MAAM,GAAG,CAAC;AACnB;AAEA3E,MAAM,CAACe,SAAS,CAAC6O,aAAa,GAAG,SAASA,aAAaA,CAAE1N,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAChF,OAAO0E,WAAW,CAAC,IAAI,EAAEzN,KAAK,EAAEyC,MAAM,EAAE,IAAI,EAAEsG,QAAQ,CAAC;AACzD,CAAC;AAEDjL,MAAM,CAACe,SAAS,CAAC8O,aAAa,GAAG,SAASA,aAAaA,CAAE3N,KAAK,EAAEyC,MAAM,EAAEsG,QAAQ,EAAE;EAChF,OAAO0E,WAAW,CAAC,IAAI,EAAEzN,KAAK,EAAEyC,MAAM,EAAE,KAAK,EAAEsG,QAAQ,CAAC;AAC1D,CAAC;;AAED;AACAjL,MAAM,CAACe,SAAS,CAACgD,IAAI,GAAG,SAASA,IAAIA,CAAEoD,MAAM,EAAE2I,WAAW,EAAEjK,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAI,CAAC9F,MAAM,CAACoB,QAAQ,CAAC+F,MAAM,CAAC,EAAE,MAAM,IAAIrF,SAAS,CAAC,6BAA6B,CAAC;EAChF,IAAI,CAAC+D,KAAK,EAAEA,KAAK,GAAG,CAAC;EACrB,IAAI,CAACC,GAAG,IAAIA,GAAG,KAAK,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACrE,MAAM;EACxC,IAAIqO,WAAW,IAAI3I,MAAM,CAAC1F,MAAM,EAAEqO,WAAW,GAAG3I,MAAM,CAAC1F,MAAM;EAC7D,IAAI,CAACqO,WAAW,EAAEA,WAAW,GAAG,CAAC;EACjC,IAAIhK,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAGD,KAAK,EAAEC,GAAG,GAAGD,KAAK;;EAEvC;EACA,IAAIC,GAAG,KAAKD,KAAK,EAAE,OAAO,CAAC;EAC3B,IAAIsB,MAAM,CAAC1F,MAAM,KAAK,CAAC,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;EAEtD;EACA,IAAIqO,WAAW,GAAG,CAAC,EAAE;IACnB,MAAM,IAAIpO,UAAU,CAAC,2BAA2B,CAAC;EACnD;EACA,IAAImE,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACpE,MAAM,EAAE,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EACjF,IAAIoE,GAAG,GAAG,CAAC,EAAE,MAAM,IAAIpE,UAAU,CAAC,yBAAyB,CAAC;;EAE5D;EACA,IAAIoE,GAAG,GAAG,IAAI,CAACrE,MAAM,EAAEqE,GAAG,GAAG,IAAI,CAACrE,MAAM;EACxC,IAAI0F,MAAM,CAAC1F,MAAM,GAAGqO,WAAW,GAAGhK,GAAG,GAAGD,KAAK,EAAE;IAC7CC,GAAG,GAAGqB,MAAM,CAAC1F,MAAM,GAAGqO,WAAW,GAAGjK,KAAK;EAC3C;EAEA,MAAM5B,GAAG,GAAG6B,GAAG,GAAGD,KAAK;EAEvB,IAAI,IAAI,KAAKsB,MAAM,IAAI,OAAOzG,UAAU,CAACK,SAAS,CAACgP,UAAU,KAAK,UAAU,EAAE;IAC5E;IACA,IAAI,CAACA,UAAU,CAACD,WAAW,EAAEjK,KAAK,EAAEC,GAAG,CAAC;EAC1C,CAAC,MAAM;IACLpF,UAAU,CAACK,SAAS,CAACsE,GAAG,CAACC,IAAI,CAC3B6B,MAAM,EACN,IAAI,CAACyD,QAAQ,CAAC/E,KAAK,EAAEC,GAAG,CAAC,EACzBgK,WACF,CAAC;EACH;EAEA,OAAO7L,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACAjE,MAAM,CAACe,SAAS,CAACkC,IAAI,GAAG,SAASA,IAAIA,CAAEwE,GAAG,EAAE5B,KAAK,EAAEC,GAAG,EAAE5C,QAAQ,EAAE;EAChE;EACA,IAAI,OAAOuE,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI,OAAO5B,KAAK,KAAK,QAAQ,EAAE;MAC7B3C,QAAQ,GAAG2C,KAAK;MAChBA,KAAK,GAAG,CAAC;MACTC,GAAG,GAAG,IAAI,CAACrE,MAAM;IACnB,CAAC,MAAM,IAAI,OAAOqE,GAAG,KAAK,QAAQ,EAAE;MAClC5C,QAAQ,GAAG4C,GAAG;MACdA,GAAG,GAAG,IAAI,CAACrE,MAAM;IACnB;IACA,IAAIyB,QAAQ,KAAK7B,SAAS,IAAI,OAAO6B,QAAQ,KAAK,QAAQ,EAAE;MAC1D,MAAM,IAAIpB,SAAS,CAAC,2BAA2B,CAAC;IAClD;IACA,IAAI,OAAOoB,QAAQ,KAAK,QAAQ,IAAI,CAAClD,MAAM,CAACsD,UAAU,CAACJ,QAAQ,CAAC,EAAE;MAChE,MAAM,IAAIpB,SAAS,CAAC,oBAAoB,GAAGoB,QAAQ,CAAC;IACtD;IACA,IAAIuE,GAAG,CAAChG,MAAM,KAAK,CAAC,EAAE;MACpB,MAAMuO,IAAI,GAAGvI,GAAG,CAACwI,UAAU,CAAC,CAAC,CAAC;MAC9B,IAAK/M,QAAQ,KAAK,MAAM,IAAI8M,IAAI,GAAG,GAAG,IAClC9M,QAAQ,KAAK,QAAQ,EAAE;QACzB;QACAuE,GAAG,GAAGuI,IAAI;MACZ;IACF;EACF,CAAC,MAAM,IAAI,OAAOvI,GAAG,KAAK,QAAQ,EAAE;IAClCA,GAAG,GAAGA,GAAG,GAAG,GAAG;EACjB,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;IACnCA,GAAG,GAAGe,MAAM,CAACf,GAAG,CAAC;EACnB;;EAEA;EACA,IAAI5B,KAAK,GAAG,CAAC,IAAI,IAAI,CAACpE,MAAM,GAAGoE,KAAK,IAAI,IAAI,CAACpE,MAAM,GAAGqE,GAAG,EAAE;IACzD,MAAM,IAAIpE,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,IAAIoE,GAAG,IAAID,KAAK,EAAE;IAChB,OAAO,IAAI;EACb;EAEAA,KAAK,GAAGA,KAAK,KAAK,CAAC;EACnBC,GAAG,GAAGA,GAAG,KAAKzE,SAAS,GAAG,IAAI,CAACI,MAAM,GAAGqE,GAAG,KAAK,CAAC;EAEjD,IAAI,CAAC2B,GAAG,EAAEA,GAAG,GAAG,CAAC;EAEjB,IAAI5D,CAAC;EACL,IAAI,OAAO4D,GAAG,KAAK,QAAQ,EAAE;IAC3B,KAAK5D,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,GAAGiC,GAAG,EAAE,EAAEjC,CAAC,EAAE;MAC5B,IAAI,CAACA,CAAC,CAAC,GAAG4D,GAAG;IACf;EACF,CAAC,MAAM;IACL,MAAMiD,KAAK,GAAG1K,MAAM,CAACoB,QAAQ,CAACqG,GAAG,CAAC,GAC9BA,GAAG,GACHzH,MAAM,CAACgC,IAAI,CAACyF,GAAG,EAAEvE,QAAQ,CAAC;IAC9B,MAAMe,GAAG,GAAGyG,KAAK,CAACjJ,MAAM;IACxB,IAAIwC,GAAG,KAAK,CAAC,EAAE;MACb,MAAM,IAAInC,SAAS,CAAC,aAAa,GAAG2F,GAAG,GACrC,mCAAmC,CAAC;IACxC;IACA,KAAK5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,GAAGD,KAAK,EAAE,EAAEhC,CAAC,EAAE;MAChC,IAAI,CAACA,CAAC,GAAGgC,KAAK,CAAC,GAAG6E,KAAK,CAAC7G,CAAC,GAAGI,GAAG,CAAC;IAClC;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;;AAEA;AACA,MAAMiM,MAAM,GAAG,CAAC,CAAC;AACjB,SAASC,CAACA,CAAEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAE;EACjCJ,MAAM,CAACE,GAAG,CAAC,GAAG,MAAMG,SAAS,SAASD,IAAI,CAAC;IACzCE,WAAWA,CAAA,EAAI;MACb,KAAK,CAAC,CAAC;MAEP3P,MAAM,CAACI,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;QACrCiB,KAAK,EAAEmO,UAAU,CAAC1J,KAAK,CAAC,IAAI,EAAEnB,SAAS,CAAC;QACxCiL,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC,CAAC;;MAEF;MACA,IAAI,CAACC,IAAI,GAAG,GAAG,IAAI,CAACA,IAAI,KAAKP,GAAG,GAAG;MACnC;MACA;MACA,IAAI,CAACQ,KAAK,EAAC;MACX;MACA,OAAO,IAAI,CAACD,IAAI;IAClB;IAEA,IAAIX,IAAIA,CAAA,EAAI;MACV,OAAOI,GAAG;IACZ;IAEA,IAAIJ,IAAIA,CAAE9N,KAAK,EAAE;MACfrB,MAAM,CAACI,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;QAClCyP,YAAY,EAAE,IAAI;QAClBxP,UAAU,EAAE,IAAI;QAChBgB,KAAK;QACLuO,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAlM,QAAQA,CAAA,EAAI;MACV,OAAO,GAAG,IAAI,CAACoM,IAAI,KAAKP,GAAG,MAAM,IAAI,CAACS,OAAO,EAAE;IACjD;EACF,CAAC;AACH;AAEAV,CAAC,CAAC,0BAA0B,EAC1B,UAAUQ,IAAI,EAAE;EACd,IAAIA,IAAI,EAAE;IACR,OAAO,GAAGA,IAAI,8BAA8B;EAC9C;EAEA,OAAO,gDAAgD;AACzD,CAAC,EAAEjP,UAAU,CAAC;AAChByO,CAAC,CAAC,sBAAsB,EACtB,UAAUQ,IAAI,EAAEnN,MAAM,EAAE;EACtB,OAAO,QAAQmN,IAAI,oDAAoD,OAAOnN,MAAM,EAAE;AACxF,CAAC,EAAE1B,SAAS,CAAC;AACfqO,CAAC,CAAC,kBAAkB,EAClB,UAAUpJ,GAAG,EAAE+J,KAAK,EAAEC,KAAK,EAAE;EAC3B,IAAIC,GAAG,GAAG,iBAAiBjK,GAAG,oBAAoB;EAClD,IAAIkK,QAAQ,GAAGF,KAAK;EACpB,IAAIvI,MAAM,CAAC0I,SAAS,CAACH,KAAK,CAAC,IAAIjM,IAAI,CAACqM,GAAG,CAACJ,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE;IACxDE,QAAQ,GAAGG,qBAAqB,CAACpM,MAAM,CAAC+L,KAAK,CAAC,CAAC;EACjD,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACpCE,QAAQ,GAAGjM,MAAM,CAAC+L,KAAK,CAAC;IACxB,IAAIA,KAAK,GAAGzE,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,EAAE,CAAC,IAAIyE,KAAK,GAAG,EAAEzE,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACzE2E,QAAQ,GAAGG,qBAAqB,CAACH,QAAQ,CAAC;IAC5C;IACAA,QAAQ,IAAI,GAAG;EACjB;EACAD,GAAG,IAAI,eAAeF,KAAK,cAAcG,QAAQ,EAAE;EACnD,OAAOD,GAAG;AACZ,CAAC,EAAEtP,UAAU,CAAC;AAEhB,SAAS0P,qBAAqBA,CAAE3J,GAAG,EAAE;EACnC,IAAIiC,GAAG,GAAG,EAAE;EACZ,IAAI7F,CAAC,GAAG4D,GAAG,CAAChG,MAAM;EAClB,MAAMoE,KAAK,GAAG4B,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;EACpC,OAAO5D,CAAC,IAAIgC,KAAK,GAAG,CAAC,EAAEhC,CAAC,IAAI,CAAC,EAAE;IAC7B6F,GAAG,GAAG,IAAIjC,GAAG,CAAC/D,KAAK,CAACG,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG6F,GAAG,EAAE;EACvC;EACA,OAAO,GAAGjC,GAAG,CAAC/D,KAAK,CAAC,CAAC,EAAEG,CAAC,CAAC,GAAG6F,GAAG,EAAE;AACnC;;AAEA;AACA;;AAEA,SAAS2H,WAAWA,CAAE1P,GAAG,EAAEgD,MAAM,EAAEpB,UAAU,EAAE;EAC7CyI,cAAc,CAACrH,MAAM,EAAE,QAAQ,CAAC;EAChC,IAAIhD,GAAG,CAACgD,MAAM,CAAC,KAAKtD,SAAS,IAAIM,GAAG,CAACgD,MAAM,GAAGpB,UAAU,CAAC,KAAKlC,SAAS,EAAE;IACvE8K,WAAW,CAACxH,MAAM,EAAEhD,GAAG,CAACF,MAAM,IAAI8B,UAAU,GAAG,CAAC,CAAC,CAAC;EACpD;AACF;AAEA,SAASgL,UAAUA,CAAErM,KAAK,EAAE6C,GAAG,EAAEiC,GAAG,EAAErF,GAAG,EAAEgD,MAAM,EAAEpB,UAAU,EAAE;EAC7D,IAAIrB,KAAK,GAAG8E,GAAG,IAAI9E,KAAK,GAAG6C,GAAG,EAAE;IAC9B,MAAMuB,CAAC,GAAG,OAAOvB,GAAG,KAAK,QAAQ,GAAG,GAAG,GAAG,EAAE;IAC5C,IAAI+L,KAAK;IACT,IAAIvN,UAAU,GAAG,CAAC,EAAE;MAClB,IAAIwB,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAKuH,MAAM,CAAC,CAAC,CAAC,EAAE;QAClCwE,KAAK,GAAG,OAAOxK,CAAC,WAAWA,CAAC,OAAO,CAAC/C,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG+C,CAAC,EAAE;MAC/D,CAAC,MAAM;QACLwK,KAAK,GAAG,SAASxK,CAAC,OAAO,CAAC/C,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG+C,CAAC,eAAe,GAC5D,GAAG,CAAC/C,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG+C,CAAC,EAAE;MAC3C;IACF,CAAC,MAAM;MACLwK,KAAK,GAAG,MAAM/L,GAAG,GAAGuB,CAAC,WAAWU,GAAG,GAAGV,CAAC,EAAE;IAC3C;IACA,MAAM,IAAI4J,MAAM,CAACoB,gBAAgB,CAAC,OAAO,EAAER,KAAK,EAAE5O,KAAK,CAAC;EAC1D;EACAmP,WAAW,CAAC1P,GAAG,EAAEgD,MAAM,EAAEpB,UAAU,CAAC;AACtC;AAEA,SAASyI,cAAcA,CAAE9J,KAAK,EAAEyO,IAAI,EAAE;EACpC,IAAI,OAAOzO,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIgO,MAAM,CAACqB,oBAAoB,CAACZ,IAAI,EAAE,QAAQ,EAAEzO,KAAK,CAAC;EAC9D;AACF;AAEA,SAASiK,WAAWA,CAAEjK,KAAK,EAAET,MAAM,EAAE0C,IAAI,EAAE;EACzC,IAAIW,IAAI,CAAC0M,KAAK,CAACtP,KAAK,CAAC,KAAKA,KAAK,EAAE;IAC/B8J,cAAc,CAAC9J,KAAK,EAAEiC,IAAI,CAAC;IAC3B,MAAM,IAAI+L,MAAM,CAACoB,gBAAgB,CAACnN,IAAI,IAAI,QAAQ,EAAE,YAAY,EAAEjC,KAAK,CAAC;EAC1E;EAEA,IAAIT,MAAM,GAAG,CAAC,EAAE;IACd,MAAM,IAAIyO,MAAM,CAACuB,wBAAwB,CAAC,CAAC;EAC7C;EAEA,MAAM,IAAIvB,MAAM,CAACoB,gBAAgB,CAACnN,IAAI,IAAI,QAAQ,EAChB,MAAMA,IAAI,GAAG,CAAC,GAAG,CAAC,WAAW1C,MAAM,EAAE,EACrCS,KAAK,CAAC;AAC1C;;AAEA;AACA;;AAEA,MAAMwP,iBAAiB,GAAG,mBAAmB;AAE7C,SAASC,WAAWA,CAAE5K,GAAG,EAAE;EACzB;EACAA,GAAG,GAAGA,GAAG,CAAC6K,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACvB;EACA7K,GAAG,GAAGA,GAAG,CAACG,IAAI,CAAC,CAAC,CAACD,OAAO,CAACyK,iBAAiB,EAAE,EAAE,CAAC;EAC/C;EACA,IAAI3K,GAAG,CAACtF,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE;EAC7B;EACA,OAAOsF,GAAG,CAACtF,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IAC3BsF,GAAG,GAAGA,GAAG,GAAG,GAAG;EACjB;EACA,OAAOA,GAAG;AACZ;AAEA,SAASrB,WAAWA,CAAErC,MAAM,EAAEwO,KAAK,EAAE;EACnCA,KAAK,GAAGA,KAAK,IAAIC,QAAQ;EACzB,IAAIlI,SAAS;EACb,MAAMnI,MAAM,GAAG4B,MAAM,CAAC5B,MAAM;EAC5B,IAAIsQ,aAAa,GAAG,IAAI;EACxB,MAAMrH,KAAK,GAAG,EAAE;EAEhB,KAAK,IAAI7G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,MAAM,EAAE,EAAEoC,CAAC,EAAE;IAC/B+F,SAAS,GAAGvG,MAAM,CAAC4M,UAAU,CAACpM,CAAC,CAAC;;IAEhC;IACA,IAAI+F,SAAS,GAAG,MAAM,IAAIA,SAAS,GAAG,MAAM,EAAE;MAC5C;MACA,IAAI,CAACmI,aAAa,EAAE;QAClB;QACA,IAAInI,SAAS,GAAG,MAAM,EAAE;UACtB;UACA,IAAI,CAACiI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEnH,KAAK,CAACR,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACnD;QACF,CAAC,MAAM,IAAIrG,CAAC,GAAG,CAAC,KAAKpC,MAAM,EAAE;UAC3B;UACA,IAAI,CAACoQ,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEnH,KAAK,CAACR,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACnD;QACF;;QAEA;QACA6H,aAAa,GAAGnI,SAAS;QAEzB;MACF;;MAEA;MACA,IAAIA,SAAS,GAAG,MAAM,EAAE;QACtB,IAAI,CAACiI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEnH,KAAK,CAACR,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QACnD6H,aAAa,GAAGnI,SAAS;QACzB;MACF;;MAEA;MACAA,SAAS,GAAG,CAACmI,aAAa,GAAG,MAAM,IAAI,EAAE,GAAGnI,SAAS,GAAG,MAAM,IAAI,OAAO;IAC3E,CAAC,MAAM,IAAImI,aAAa,EAAE;MACxB;MACA,IAAI,CAACF,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEnH,KAAK,CAACR,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrD;IAEA6H,aAAa,GAAG,IAAI;;IAEpB;IACA,IAAInI,SAAS,GAAG,IAAI,EAAE;MACpB,IAAI,CAACiI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;MACtBnH,KAAK,CAACR,IAAI,CAACN,SAAS,CAAC;IACvB,CAAC,MAAM,IAAIA,SAAS,GAAG,KAAK,EAAE;MAC5B,IAAI,CAACiI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;MACtBnH,KAAK,CAACR,IAAI,CACRN,SAAS,IAAI,GAAG,GAAG,IAAI,EACvBA,SAAS,GAAG,IAAI,GAAG,IACrB,CAAC;IACH,CAAC,MAAM,IAAIA,SAAS,GAAG,OAAO,EAAE;MAC9B,IAAI,CAACiI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;MACtBnH,KAAK,CAACR,IAAI,CACRN,SAAS,IAAI,GAAG,GAAG,IAAI,EACvBA,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,EAC9BA,SAAS,GAAG,IAAI,GAAG,IACrB,CAAC;IACH,CAAC,MAAM,IAAIA,SAAS,GAAG,QAAQ,EAAE;MAC/B,IAAI,CAACiI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;MACtBnH,KAAK,CAACR,IAAI,CACRN,SAAS,IAAI,IAAI,GAAG,IAAI,EACxBA,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,EAC9BA,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,EAC9BA,SAAS,GAAG,IAAI,GAAG,IACrB,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAIN,KAAK,CAAC,oBAAoB,CAAC;IACvC;EACF;EAEA,OAAOoB,KAAK;AACd;AAEA,SAASzB,YAAYA,CAAElC,GAAG,EAAE;EAC1B,MAAMiL,SAAS,GAAG,EAAE;EACpB,KAAK,IAAInO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,GAAG,CAACtF,MAAM,EAAE,EAAEoC,CAAC,EAAE;IACnC;IACAmO,SAAS,CAAC9H,IAAI,CAACnD,GAAG,CAACkJ,UAAU,CAACpM,CAAC,CAAC,GAAG,IAAI,CAAC;EAC1C;EACA,OAAOmO,SAAS;AAClB;AAEA,SAAS5I,cAAcA,CAAErC,GAAG,EAAE8K,KAAK,EAAE;EACnC,IAAII,CAAC,EAAE5F,EAAE,EAAED,EAAE;EACb,MAAM4F,SAAS,GAAG,EAAE;EACpB,KAAK,IAAInO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,GAAG,CAACtF,MAAM,EAAE,EAAEoC,CAAC,EAAE;IACnC,IAAI,CAACgO,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;IAEtBI,CAAC,GAAGlL,GAAG,CAACkJ,UAAU,CAACpM,CAAC,CAAC;IACrBwI,EAAE,GAAG4F,CAAC,IAAI,CAAC;IACX7F,EAAE,GAAG6F,CAAC,GAAG,GAAG;IACZD,SAAS,CAAC9H,IAAI,CAACkC,EAAE,CAAC;IAClB4F,SAAS,CAAC9H,IAAI,CAACmC,EAAE,CAAC;EACpB;EAEA,OAAO2F,SAAS;AAClB;AAEA,SAASrM,aAAaA,CAAEoB,GAAG,EAAE;EAC3B,OAAOrH,MAAM,CAACwS,WAAW,CAACP,WAAW,CAAC5K,GAAG,CAAC,CAAC;AAC7C;AAEA,SAASgC,UAAUA,CAAEoJ,GAAG,EAAEC,GAAG,EAAEzN,MAAM,EAAElD,MAAM,EAAE;EAC7C,IAAIoC,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,MAAM,EAAE,EAAEoC,CAAC,EAAE;IAC3B,IAAKA,CAAC,GAAGc,MAAM,IAAIyN,GAAG,CAAC3Q,MAAM,IAAMoC,CAAC,IAAIsO,GAAG,CAAC1Q,MAAO,EAAE;IACrD2Q,GAAG,CAACvO,CAAC,GAAGc,MAAM,CAAC,GAAGwN,GAAG,CAACtO,CAAC,CAAC;EAC1B;EACA,OAAOA,CAAC;AACV;;AAEA;AACA;AACA;AACA,SAAStB,UAAUA,CAAEyB,GAAG,EAAEG,IAAI,EAAE;EAC9B,OAAOH,GAAG,YAAYG,IAAI,IACvBH,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACwM,WAAW,IAAI,IAAI,IAAIxM,GAAG,CAACwM,WAAW,CAACG,IAAI,IAAI,IAAI,IACrE3M,GAAG,CAACwM,WAAW,CAACG,IAAI,KAAKxM,IAAI,CAACwM,IAAK;AACzC;AACA,SAASzM,WAAWA,CAAEF,GAAG,EAAE;EACzB;EACA,OAAOA,GAAG,KAAKA,GAAG,EAAC;AACrB;;AAEA;AACA;AACA,MAAMyG,mBAAmB,GAAI,YAAY;EACvC,MAAM4H,QAAQ,GAAG,kBAAkB;EACnC,MAAMC,KAAK,GAAG,IAAIlO,KAAK,CAAC,GAAG,CAAC;EAC5B,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;IAC3B,MAAM0O,GAAG,GAAG1O,CAAC,GAAG,EAAE;IAClB,KAAK,IAAIwE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3BiK,KAAK,CAACC,GAAG,GAAGlK,CAAC,CAAC,GAAGgK,QAAQ,CAACxO,CAAC,CAAC,GAAGwO,QAAQ,CAAChK,CAAC,CAAC;IAC5C;EACF;EACA,OAAOiK,KAAK;AACd,CAAC,CAAE,CAAC;;AAEJ;AACA,SAASvG,kBAAkBA,CAAEyG,EAAE,EAAE;EAC/B,OAAO,OAAOlG,MAAM,KAAK,WAAW,GAAGmG,sBAAsB,GAAGD,EAAE;AACpE;AAEA,SAASC,sBAAsBA,CAAA,EAAI;EACjC,MAAM,IAAInJ,KAAK,CAAC,sBAAsB,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeList.js\";\nimport React from 'react';\nimport EpisodeCard from './EpisodeCard';\nimport './EpisodeList.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EpisodeList = ({\n  episodes,\n  onEpisodeSelect,\n  onShowDetail,\n  currentEpisode\n}) => {\n  if (!episodes || episodes.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"episode-list-empty\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u6682\\u65E0\\u8282\\u76EE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"episode-list\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"episode-list-title\",\n      children: [\"\\u8282\\u76EE\\u5217\\u8868 (\", episodes.length, \")\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"episode-list-container\",\n      children: episodes.map(episode => /*#__PURE__*/_jsxDEV(EpisodeCard, {\n        episode: episode,\n        onSelect: onEpisodeSelect,\n        isActive: currentEpisode && currentEpisode.id === episode.id\n      }, episode.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = EpisodeList;\nexport default EpisodeList;\nvar _c;\n$RefreshReg$(_c, \"EpisodeList\");", "map": {"version": 3, "names": ["React", "EpisodeCard", "jsxDEV", "_jsxDEV", "EpisodeList", "episodes", "onEpisodeSelect", "onShowDetail", "currentEpisode", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "episode", "onSelect", "isActive", "id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeList.js"], "sourcesContent": ["import React from 'react';\nimport EpisodeCard from './EpisodeCard';\nimport './EpisodeList.css';\n\nconst EpisodeList = ({ episodes, onEpisodeSelect, onShowDetail, currentEpisode }) => {\n  if (!episodes || episodes.length === 0) {\n    return (\n      <div className=\"episode-list-empty\">\n        <p>暂无节目</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"episode-list\">\n      <h2 className=\"episode-list-title\">节目列表 ({episodes.length})</h2>\n      <div className=\"episode-list-container\">\n        {episodes.map((episode) => (\n          <EpisodeCard\n            key={episode.id}\n            episode={episode}\n            onSelect={onEpisodeSelect}\n            isActive={currentEpisode && currentEpisode.id === episode.id}\n          />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default EpisodeList;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,eAAe;EAAEC,YAAY;EAAEC;AAAe,CAAC,KAAK;EACnF,IAAI,CAACH,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IACtC,oBACEN,OAAA;MAAKO,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCR,OAAA;QAAAQ,QAAA,EAAG;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEV;EAEA,oBACEZ,OAAA;IAAKO,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BR,OAAA;MAAIO,SAAS,EAAC,oBAAoB;MAAAC,QAAA,GAAC,4BAAM,EAACN,QAAQ,CAACI,MAAM,EAAC,GAAC;IAAA;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChEZ,OAAA;MAAKO,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EACpCN,QAAQ,CAACW,GAAG,CAAEC,OAAO,iBACpBd,OAAA,CAACF,WAAW;QAEVgB,OAAO,EAAEA,OAAQ;QACjBC,QAAQ,EAAEZ,eAAgB;QAC1Ba,QAAQ,EAAEX,cAAc,IAAIA,cAAc,CAACY,EAAE,KAAKH,OAAO,CAACG;MAAG,GAHxDH,OAAO,CAACG,EAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIhB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GAxBIjB,WAAW;AA0BjB,eAAeA,WAAW;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
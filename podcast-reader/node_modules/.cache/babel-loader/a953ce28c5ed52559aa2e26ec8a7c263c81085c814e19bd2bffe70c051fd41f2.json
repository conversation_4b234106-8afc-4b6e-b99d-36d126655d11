{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n\n'use strict';\n\nmodule.exports = Transform;\nvar _require$codes = require('../errors').codes,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n  ERR_TRANSFORM_ALREADY_TRANSFORMING = _require$codes.ERR_TRANSFORM_ALREADY_TRANSFORMING,\n  ERR_TRANSFORM_WITH_LENGTH_0 = _require$codes.ERR_TRANSFORM_WITH_LENGTH_0;\nvar Duplex = require('./_stream_duplex');\nrequire('inherits')(Transform, Duplex);\nfunction afterTransform(er, data) {\n  var ts = this._transformState;\n  ts.transforming = false;\n  var cb = ts.writecb;\n  if (cb === null) {\n    return this.emit('error', new ERR_MULTIPLE_CALLBACK());\n  }\n  ts.writechunk = null;\n  ts.writecb = null;\n  if (data != null)\n    // single equals check for both `null` and `undefined`\n    this.push(data);\n  cb(er);\n  var rs = this._readableState;\n  rs.reading = false;\n  if (rs.needReadable || rs.length < rs.highWaterMark) {\n    this._read(rs.highWaterMark);\n  }\n}\nfunction Transform(options) {\n  if (!(this instanceof Transform)) return new Transform(options);\n  Duplex.call(this, options);\n  this._transformState = {\n    afterTransform: afterTransform.bind(this),\n    needTransform: false,\n    transforming: false,\n    writecb: null,\n    writechunk: null,\n    writeencoding: null\n  };\n\n  // start out asking for a readable event once data is transformed.\n  this._readableState.needReadable = true;\n\n  // we have implemented the _read method, and done the other things\n  // that Readable wants before the first _read call, so unset the\n  // sync guard flag.\n  this._readableState.sync = false;\n  if (options) {\n    if (typeof options.transform === 'function') this._transform = options.transform;\n    if (typeof options.flush === 'function') this._flush = options.flush;\n  }\n\n  // When the writable side finishes, then flush out anything remaining.\n  this.on('prefinish', prefinish);\n}\nfunction prefinish() {\n  var _this = this;\n  if (typeof this._flush === 'function' && !this._readableState.destroyed) {\n    this._flush(function (er, data) {\n      done(_this, er, data);\n    });\n  } else {\n    done(this, null, null);\n  }\n}\nTransform.prototype.push = function (chunk, encoding) {\n  this._transformState.needTransform = false;\n  return Duplex.prototype.push.call(this, chunk, encoding);\n};\n\n// This is the part where you do stuff!\n// override this function in implementation classes.\n// 'chunk' is an input chunk.\n//\n// Call `push(newChunk)` to pass along transformed output\n// to the readable side.  You may call 'push' zero or more times.\n//\n// Call `cb(err)` when you are done with this chunk.  If you pass\n// an error, then that'll put the hurt on the whole operation.  If you\n// never call cb(), then you'll never get another chunk.\nTransform.prototype._transform = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_transform()'));\n};\nTransform.prototype._write = function (chunk, encoding, cb) {\n  var ts = this._transformState;\n  ts.writecb = cb;\n  ts.writechunk = chunk;\n  ts.writeencoding = encoding;\n  if (!ts.transforming) {\n    var rs = this._readableState;\n    if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);\n  }\n};\n\n// Doesn't matter what the args are here.\n// _transform does all the work.\n// That we got here means that the readable side wants more data.\nTransform.prototype._read = function (n) {\n  var ts = this._transformState;\n  if (ts.writechunk !== null && !ts.transforming) {\n    ts.transforming = true;\n    this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);\n  } else {\n    // mark that we need a transform, so that any data that comes in\n    // will get processed, now that we've asked for it.\n    ts.needTransform = true;\n  }\n};\nTransform.prototype._destroy = function (err, cb) {\n  Duplex.prototype._destroy.call(this, err, function (err2) {\n    cb(err2);\n  });\n};\nfunction done(stream, er, data) {\n  if (er) return stream.emit('error', er);\n  if (data != null)\n    // single equals check for both `null` and `undefined`\n    stream.push(data);\n\n  // TODO(BridgeAR): Write a test for these two error cases\n  // if there's nothing in the write buffer, then that means\n  // that nothing more will ever be provided\n  if (stream._writableState.length) throw new ERR_TRANSFORM_WITH_LENGTH_0();\n  if (stream._transformState.transforming) throw new ERR_TRANSFORM_ALREADY_TRANSFORMING();\n  return stream.push(null);\n}", "map": {"version": 3, "names": ["module", "exports", "Transform", "_require$codes", "require", "codes", "ERR_METHOD_NOT_IMPLEMENTED", "ERR_MULTIPLE_CALLBACK", "ERR_TRANSFORM_ALREADY_TRANSFORMING", "ERR_TRANSFORM_WITH_LENGTH_0", "Duplex", "afterTransform", "er", "data", "ts", "_transformState", "transforming", "cb", "writecb", "emit", "writechunk", "push", "rs", "_readableState", "reading", "needReadable", "length", "highWaterMark", "_read", "options", "call", "bind", "needTransform", "writeencoding", "sync", "transform", "_transform", "flush", "_flush", "on", "prefinish", "_this", "destroyed", "done", "prototype", "chunk", "encoding", "_write", "n", "_destroy", "err", "err2", "stream", "_writableState"], "sources": ["/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/node_modules/readable-stream/lib/_stream_transform.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n\n'use strict';\n\nmodule.exports = Transform;\nvar _require$codes = require('../errors').codes,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n  ERR_TRANSFORM_ALREADY_TRANSFORMING = _require$codes.ERR_TRANSFORM_ALREADY_TRANSFORMING,\n  ERR_TRANSFORM_WITH_LENGTH_0 = _require$codes.ERR_TRANSFORM_WITH_LENGTH_0;\nvar Duplex = require('./_stream_duplex');\nrequire('inherits')(Transform, Duplex);\nfunction afterTransform(er, data) {\n  var ts = this._transformState;\n  ts.transforming = false;\n  var cb = ts.writecb;\n  if (cb === null) {\n    return this.emit('error', new ERR_MULTIPLE_CALLBACK());\n  }\n  ts.writechunk = null;\n  ts.writecb = null;\n  if (data != null)\n    // single equals check for both `null` and `undefined`\n    this.push(data);\n  cb(er);\n  var rs = this._readableState;\n  rs.reading = false;\n  if (rs.needReadable || rs.length < rs.highWaterMark) {\n    this._read(rs.highWaterMark);\n  }\n}\nfunction Transform(options) {\n  if (!(this instanceof Transform)) return new Transform(options);\n  Duplex.call(this, options);\n  this._transformState = {\n    afterTransform: afterTransform.bind(this),\n    needTransform: false,\n    transforming: false,\n    writecb: null,\n    writechunk: null,\n    writeencoding: null\n  };\n\n  // start out asking for a readable event once data is transformed.\n  this._readableState.needReadable = true;\n\n  // we have implemented the _read method, and done the other things\n  // that Readable wants before the first _read call, so unset the\n  // sync guard flag.\n  this._readableState.sync = false;\n  if (options) {\n    if (typeof options.transform === 'function') this._transform = options.transform;\n    if (typeof options.flush === 'function') this._flush = options.flush;\n  }\n\n  // When the writable side finishes, then flush out anything remaining.\n  this.on('prefinish', prefinish);\n}\nfunction prefinish() {\n  var _this = this;\n  if (typeof this._flush === 'function' && !this._readableState.destroyed) {\n    this._flush(function (er, data) {\n      done(_this, er, data);\n    });\n  } else {\n    done(this, null, null);\n  }\n}\nTransform.prototype.push = function (chunk, encoding) {\n  this._transformState.needTransform = false;\n  return Duplex.prototype.push.call(this, chunk, encoding);\n};\n\n// This is the part where you do stuff!\n// override this function in implementation classes.\n// 'chunk' is an input chunk.\n//\n// Call `push(newChunk)` to pass along transformed output\n// to the readable side.  You may call 'push' zero or more times.\n//\n// Call `cb(err)` when you are done with this chunk.  If you pass\n// an error, then that'll put the hurt on the whole operation.  If you\n// never call cb(), then you'll never get another chunk.\nTransform.prototype._transform = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_transform()'));\n};\nTransform.prototype._write = function (chunk, encoding, cb) {\n  var ts = this._transformState;\n  ts.writecb = cb;\n  ts.writechunk = chunk;\n  ts.writeencoding = encoding;\n  if (!ts.transforming) {\n    var rs = this._readableState;\n    if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);\n  }\n};\n\n// Doesn't matter what the args are here.\n// _transform does all the work.\n// That we got here means that the readable side wants more data.\nTransform.prototype._read = function (n) {\n  var ts = this._transformState;\n  if (ts.writechunk !== null && !ts.transforming) {\n    ts.transforming = true;\n    this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);\n  } else {\n    // mark that we need a transform, so that any data that comes in\n    // will get processed, now that we've asked for it.\n    ts.needTransform = true;\n  }\n};\nTransform.prototype._destroy = function (err, cb) {\n  Duplex.prototype._destroy.call(this, err, function (err2) {\n    cb(err2);\n  });\n};\nfunction done(stream, er, data) {\n  if (er) return stream.emit('error', er);\n  if (data != null)\n    // single equals check for both `null` and `undefined`\n    stream.push(data);\n\n  // TODO(BridgeAR): Write a test for these two error cases\n  // if there's nothing in the write buffer, then that means\n  // that nothing more will ever be provided\n  if (stream._writableState.length) throw new ERR_TRANSFORM_WITH_LENGTH_0();\n  if (stream._transformState.transforming) throw new ERR_TRANSFORM_ALREADY_TRANSFORMING();\n  return stream.push(null);\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1B,IAAIC,cAAc,GAAGC,OAAO,CAAC,WAAW,CAAC,CAACC,KAAK;EAC7CC,0BAA0B,GAAGH,cAAc,CAACG,0BAA0B;EACtEC,qBAAqB,GAAGJ,cAAc,CAACI,qBAAqB;EAC5DC,kCAAkC,GAAGL,cAAc,CAACK,kCAAkC;EACtFC,2BAA2B,GAAGN,cAAc,CAACM,2BAA2B;AAC1E,IAAIC,MAAM,GAAGN,OAAO,CAAC,kBAAkB,CAAC;AACxCA,OAAO,CAAC,UAAU,CAAC,CAACF,SAAS,EAAEQ,MAAM,CAAC;AACtC,SAASC,cAAcA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAChC,IAAIC,EAAE,GAAG,IAAI,CAACC,eAAe;EAC7BD,EAAE,CAACE,YAAY,GAAG,KAAK;EACvB,IAAIC,EAAE,GAAGH,EAAE,CAACI,OAAO;EACnB,IAAID,EAAE,KAAK,IAAI,EAAE;IACf,OAAO,IAAI,CAACE,IAAI,CAAC,OAAO,EAAE,IAAIZ,qBAAqB,CAAC,CAAC,CAAC;EACxD;EACAO,EAAE,CAACM,UAAU,GAAG,IAAI;EACpBN,EAAE,CAACI,OAAO,GAAG,IAAI;EACjB,IAAIL,IAAI,IAAI,IAAI;IACd;IACA,IAAI,CAACQ,IAAI,CAACR,IAAI,CAAC;EACjBI,EAAE,CAACL,EAAE,CAAC;EACN,IAAIU,EAAE,GAAG,IAAI,CAACC,cAAc;EAC5BD,EAAE,CAACE,OAAO,GAAG,KAAK;EAClB,IAAIF,EAAE,CAACG,YAAY,IAAIH,EAAE,CAACI,MAAM,GAAGJ,EAAE,CAACK,aAAa,EAAE;IACnD,IAAI,CAACC,KAAK,CAACN,EAAE,CAACK,aAAa,CAAC;EAC9B;AACF;AACA,SAASzB,SAASA,CAAC2B,OAAO,EAAE;EAC1B,IAAI,EAAE,IAAI,YAAY3B,SAAS,CAAC,EAAE,OAAO,IAAIA,SAAS,CAAC2B,OAAO,CAAC;EAC/DnB,MAAM,CAACoB,IAAI,CAAC,IAAI,EAAED,OAAO,CAAC;EAC1B,IAAI,CAACd,eAAe,GAAG;IACrBJ,cAAc,EAAEA,cAAc,CAACoB,IAAI,CAAC,IAAI,CAAC;IACzCC,aAAa,EAAE,KAAK;IACpBhB,YAAY,EAAE,KAAK;IACnBE,OAAO,EAAE,IAAI;IACbE,UAAU,EAAE,IAAI;IAChBa,aAAa,EAAE;EACjB,CAAC;;EAED;EACA,IAAI,CAACV,cAAc,CAACE,YAAY,GAAG,IAAI;;EAEvC;EACA;EACA;EACA,IAAI,CAACF,cAAc,CAACW,IAAI,GAAG,KAAK;EAChC,IAAIL,OAAO,EAAE;IACX,IAAI,OAAOA,OAAO,CAACM,SAAS,KAAK,UAAU,EAAE,IAAI,CAACC,UAAU,GAAGP,OAAO,CAACM,SAAS;IAChF,IAAI,OAAON,OAAO,CAACQ,KAAK,KAAK,UAAU,EAAE,IAAI,CAACC,MAAM,GAAGT,OAAO,CAACQ,KAAK;EACtE;;EAEA;EACA,IAAI,CAACE,EAAE,CAAC,WAAW,EAAEC,SAAS,CAAC;AACjC;AACA,SAASA,SAASA,CAAA,EAAG;EACnB,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAI,OAAO,IAAI,CAACH,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,CAACf,cAAc,CAACmB,SAAS,EAAE;IACvE,IAAI,CAACJ,MAAM,CAAC,UAAU1B,EAAE,EAAEC,IAAI,EAAE;MAC9B8B,IAAI,CAACF,KAAK,EAAE7B,EAAE,EAAEC,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL8B,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACxB;AACF;AACAzC,SAAS,CAAC0C,SAAS,CAACvB,IAAI,GAAG,UAAUwB,KAAK,EAAEC,QAAQ,EAAE;EACpD,IAAI,CAAC/B,eAAe,CAACiB,aAAa,GAAG,KAAK;EAC1C,OAAOtB,MAAM,CAACkC,SAAS,CAACvB,IAAI,CAACS,IAAI,CAAC,IAAI,EAAEe,KAAK,EAAEC,QAAQ,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA5C,SAAS,CAAC0C,SAAS,CAACR,UAAU,GAAG,UAAUS,KAAK,EAAEC,QAAQ,EAAE7B,EAAE,EAAE;EAC9DA,EAAE,CAAC,IAAIX,0BAA0B,CAAC,cAAc,CAAC,CAAC;AACpD,CAAC;AACDJ,SAAS,CAAC0C,SAAS,CAACG,MAAM,GAAG,UAAUF,KAAK,EAAEC,QAAQ,EAAE7B,EAAE,EAAE;EAC1D,IAAIH,EAAE,GAAG,IAAI,CAACC,eAAe;EAC7BD,EAAE,CAACI,OAAO,GAAGD,EAAE;EACfH,EAAE,CAACM,UAAU,GAAGyB,KAAK;EACrB/B,EAAE,CAACmB,aAAa,GAAGa,QAAQ;EAC3B,IAAI,CAAChC,EAAE,CAACE,YAAY,EAAE;IACpB,IAAIM,EAAE,GAAG,IAAI,CAACC,cAAc;IAC5B,IAAIT,EAAE,CAACkB,aAAa,IAAIV,EAAE,CAACG,YAAY,IAAIH,EAAE,CAACI,MAAM,GAAGJ,EAAE,CAACK,aAAa,EAAE,IAAI,CAACC,KAAK,CAACN,EAAE,CAACK,aAAa,CAAC;EACvG;AACF,CAAC;;AAED;AACA;AACA;AACAzB,SAAS,CAAC0C,SAAS,CAAChB,KAAK,GAAG,UAAUoB,CAAC,EAAE;EACvC,IAAIlC,EAAE,GAAG,IAAI,CAACC,eAAe;EAC7B,IAAID,EAAE,CAACM,UAAU,KAAK,IAAI,IAAI,CAACN,EAAE,CAACE,YAAY,EAAE;IAC9CF,EAAE,CAACE,YAAY,GAAG,IAAI;IACtB,IAAI,CAACoB,UAAU,CAACtB,EAAE,CAACM,UAAU,EAAEN,EAAE,CAACmB,aAAa,EAAEnB,EAAE,CAACH,cAAc,CAAC;EACrE,CAAC,MAAM;IACL;IACA;IACAG,EAAE,CAACkB,aAAa,GAAG,IAAI;EACzB;AACF,CAAC;AACD9B,SAAS,CAAC0C,SAAS,CAACK,QAAQ,GAAG,UAAUC,GAAG,EAAEjC,EAAE,EAAE;EAChDP,MAAM,CAACkC,SAAS,CAACK,QAAQ,CAACnB,IAAI,CAAC,IAAI,EAAEoB,GAAG,EAAE,UAAUC,IAAI,EAAE;IACxDlC,EAAE,CAACkC,IAAI,CAAC;EACV,CAAC,CAAC;AACJ,CAAC;AACD,SAASR,IAAIA,CAACS,MAAM,EAAExC,EAAE,EAAEC,IAAI,EAAE;EAC9B,IAAID,EAAE,EAAE,OAAOwC,MAAM,CAACjC,IAAI,CAAC,OAAO,EAAEP,EAAE,CAAC;EACvC,IAAIC,IAAI,IAAI,IAAI;IACd;IACAuC,MAAM,CAAC/B,IAAI,CAACR,IAAI,CAAC;;EAEnB;EACA;EACA;EACA,IAAIuC,MAAM,CAACC,cAAc,CAAC3B,MAAM,EAAE,MAAM,IAAIjB,2BAA2B,CAAC,CAAC;EACzE,IAAI2C,MAAM,CAACrC,eAAe,CAACC,YAAY,EAAE,MAAM,IAAIR,kCAAkC,CAAC,CAAC;EACvF,OAAO4C,MAAM,CAAC/B,IAAI,CAAC,IAAI,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}
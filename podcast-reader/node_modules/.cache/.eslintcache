[{"/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/index.js": "1", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/reportWebVitals.js": "2", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/App.js": "3", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/data/mockData.js": "4", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/PodcastHeader.js": "5", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/SearchBar.js": "6", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/AudioPlayer.js": "7", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/utils/rssParser.js": "8", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeList.js": "9", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeCard.js": "10", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeDetail.js": "11", "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/PodcastStats.js": "12"}, {"size": 535, "mtime": 1750854787617, "results": "13", "hashOfConfig": "14"}, {"size": 362, "mtime": 1750854787617, "results": "15", "hashOfConfig": "14"}, {"size": 3440, "mtime": 1750859989666, "results": "16", "hashOfConfig": "14"}, {"size": 3184, "mtime": 1750859169225, "results": "17", "hashOfConfig": "14"}, {"size": 1041, "mtime": 1750855068454, "results": "18", "hashOfConfig": "14"}, {"size": 3112, "mtime": 1750855197213, "results": "19", "hashOfConfig": "14"}, {"size": 5218, "mtime": 1750860044474, "results": "20", "hashOfConfig": "14"}, {"size": 3907, "mtime": 1750859300832, "results": "21", "hashOfConfig": "14"}, {"size": 867, "mtime": 1750859856198, "results": "22", "hashOfConfig": "14"}, {"size": 2069, "mtime": 1750859829309, "results": "23", "hashOfConfig": "14"}, {"size": 2522, "mtime": 1750859328083, "results": "24", "hashOfConfig": "14"}, {"size": 2118, "mtime": 1750859952708, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "zrrh03", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/index.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/reportWebVitals.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/App.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/data/mockData.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/PodcastHeader.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/SearchBar.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/AudioPlayer.js", ["62", "63"], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/utils/rssParser.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeList.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeCard.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/EpisodeDetail.js", [], [], "/Users/<USER>/Workspaces/coding/code.byted.org/liuyong.tz/ai-test/augment-test-case/reader/podcast-reader/src/components/PodcastStats.js", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 49, "column": 7, "nodeType": "66", "messageId": "67", "endLine": 49, "endColumn": 17}, {"ruleId": "68", "severity": 1, "message": "69", "line": 51, "column": 9, "nodeType": "70", "endLine": 61, "endColumn": 4, "suggestions": "71"}, "no-use-before-define", "'togglePlay' was used before it was defined.", "Identifier", "usedBeforeDefined", "react-hooks/exhaustive-deps", "The 'togglePlay' function makes the dependencies of useEffect Hook (at line 49) change on every render. To fix this, wrap the definition of 'togglePlay' in its own useCallback() Hook.", "VariableDeclarator", ["72"], {"desc": "73", "fix": "74"}, "Wrap the definition of 'togglePlay' in its own useCallback() Hook.", {"range": "75", "text": "76"}, [1831, 2014], "useCallback(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    if (isPlaying) {\n      audio.pause();\n    } else {\n      audio.play();\n    }\n    setIsPlaying(!isPlaying);\n  })"]
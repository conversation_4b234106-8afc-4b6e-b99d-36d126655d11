.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 120px; /* 为播放器留出空间 */
}

.error-message {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  padding: 1rem;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  color: #856404;
  text-align: center;
  box-shadow: 0 2px 8px rgba(255, 234, 167, 0.3);
}

.error-message p {
  margin: 0;
  font-weight: 500;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 1rem;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .App {
    padding-bottom: 140px; /* 移动端播放器更高 */
  }
}

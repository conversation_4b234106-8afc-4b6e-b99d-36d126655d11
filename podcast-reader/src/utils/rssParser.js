import axios from 'axios';

// 由于CORS限制，我们需要使用代理服务或者在开发环境中处理
const CORS_PROXY = 'https://cors-anywhere.herokuapp.com/';

export const parseRSSFeed = async (rssUrl) => {
  try {
    // 在实际应用中，你可能需要设置自己的CORS代理
    const response = await axios.get(`${CORS_PROXY}${rssUrl}`);

    // 使用浏览器原生的DOMParser解析XML
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(response.data, 'text/xml');

    // 检查解析错误
    const parseError = xmlDoc.querySelector('parsererror');
    if (parseError) {
      throw new Error('XML parsing error');
    }

    const channel = xmlDoc.querySelector('channel');
    if (!channel) {
      throw new Error('Invalid RSS format');
    }

    // 提取播客信息
    const podcastInfo = {
      title: getTextContent(channel, 'title') || 'Unknown Podcast',
      description: getTextContent(channel, 'description') || '',
      language: getTextContent(channel, 'language') || 'zh-cn',
      image: getImageUrl(channel),
      link: getTextContent(channel, 'link') || '',
      lastBuildDate: getTextContent(channel, 'lastBuildDate') || null,
    };

    // 提取节目列表
    const items = Array.from(xmlDoc.querySelectorAll('item'));
    const episodes = items.map((item, index) => {
      // 提取音频链接
      const enclosure = item.querySelector('enclosure');
      const audioUrl = enclosure ? enclosure.getAttribute('url') : null;

      // 提取时长
      let duration = getTextContent(item, 'itunes\\:duration') ||
                    (enclosure ? enclosure.getAttribute('itunes:duration') : null);

      return {
        id: `episode-${index}`,
        title: stripHtml(getTextContent(item, 'title') || `Episode ${index + 1}`),
        description: stripHtml(getTextContent(item, 'description') || ''),
        pubDate: getTextContent(item, 'pubDate') || null,
        audioUrl: audioUrl,
        duration: duration,
        link: getTextContent(item, 'link') || null,
        guid: getTextContent(item, 'guid') || `episode-${index}`,
      };
    });

    return {
      podcastInfo,
      episodes
    };
  } catch (error) {
    console.error('Error fetching RSS feed:', error);
    throw error;
  }
};

// 辅助函数：获取元素文本内容
const getTextContent = (parent, selector) => {
  const element = parent.querySelector(selector);
  return element ? element.textContent.trim() : null;
};

// 辅助函数：获取图片URL
const getImageUrl = (channel) => {
  const image = channel.querySelector('image url');
  if (image) return image.textContent.trim();

  const itunesImage = channel.querySelector('itunes\\:image');
  if (itunesImage) return itunesImage.getAttribute('href');

  return null;
};

// 清理HTML标签的简单函数
const stripHtml = (html) => {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '').trim();
};

// 格式化时长显示
export const formatDuration = (duration) => {
  if (!duration) return '';

  // 如果是 HH:MM:SS 格式
  if (duration.includes(':')) {
    return duration;
  }

  // 如果是秒数
  const seconds = parseInt(duration);
  if (isNaN(seconds)) return duration;

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
};

// 格式化日期显示
export const formatDate = (dateString) => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
};

import React, { useState } from 'react';
import PodcastHeader from './components/PodcastHeader';
import SearchBar from './components/SearchBar';
import EpisodeList from './components/EpisodeList';
import AudioPlayer from './components/AudioPlayer';
import { parseRSSFeed } from './utils/rssParser';
import { mockPodcastData } from './data/mockData';
import './App.css';

function App() {
  const [podcastData, setPodcastData] = useState(mockPodcastData);
  const [filteredEpisodes, setFilteredEpisodes] = useState(mockPodcastData.episodes);
  const [currentEpisode, setCurrentEpisode] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 搜索功能
  const handleSearch = (searchTerm) => {
    if (!searchTerm.trim()) {
      setFilteredEpisodes(podcastData.episodes);
      return;
    }

    const filtered = podcastData.episodes.filter(episode =>
      episode.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      episode.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredEpisodes(filtered);
  };

  // 加载RSS源
  const handleRSSUrlSubmit = async (rssUrl) => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await parseRSSFeed(rssUrl);
      setPodcastData(data);
      setFilteredEpisodes(data.episodes);
      setCurrentEpisode(null);
    } catch (err) {
      console.error('Failed to load RSS feed:', err);
      setError('无法加载RSS源，请检查URL是否正确或网络连接。正在显示示例数据。');
      // 如果加载失败，保持使用模拟数据
    } finally {
      setIsLoading(false);
    }
  };

  // 选择节目
  const handleEpisodeSelect = (episode) => {
    setCurrentEpisode(episode);
  };

  // 关闭播放器
  const handleClosePlayer = () => {
    setCurrentEpisode(null);
  };

  return (
    <div className="App">
      <PodcastHeader podcastInfo={podcastData.podcastInfo} />

      <SearchBar
        onSearch={handleSearch}
        onRSSUrlSubmit={handleRSSUrlSubmit}
      />

      {error && (
        <div className="error-message">
          <p>{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>正在加载RSS源...</p>
        </div>
      ) : (
        <EpisodeList
          episodes={filteredEpisodes}
          onEpisodeSelect={handleEpisodeSelect}
          currentEpisode={currentEpisode}
        />
      )}

      {currentEpisode && (
        <AudioPlayer
          episode={currentEpisode}
          onClose={handleClosePlayer}
        />
      )}
    </div>
  );
}

export default App;

import React from 'react';
import './PodcastStats.css';

const PodcastStats = ({ episodes, filteredCount }) => {
  if (!episodes || episodes.length === 0) return null;

  const totalEpisodes = episodes.length;
  const showingCount = filteredCount !== undefined ? filteredCount : totalEpisodes;
  
  // 计算总时长（如果有时长信息）
  const totalDuration = episodes.reduce((total, episode) => {
    if (!episode.duration) return total;
    
    // 解析时长格式 (HH:MM:SS 或 MM:SS)
    const parts = episode.duration.split(':').map(Number);
    let seconds = 0;
    
    if (parts.length === 3) {
      // HH:MM:SS
      seconds = parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts.length === 2) {
      // MM:SS
      seconds = parts[0] * 60 + parts[1];
    }
    
    return total + seconds;
  }, 0);

  const formatTotalDuration = (totalSeconds) => {
    if (totalSeconds === 0) return null;
    
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  const formattedDuration = formatTotalDuration(totalDuration);

  return (
    <div className="podcast-stats">
      <div className="stats-container">
        <div className="stat-item">
          <span className="stat-number">{showingCount}</span>
          <span className="stat-label">
            {showingCount !== totalEpisodes ? '筛选结果' : '总节目数'}
          </span>
        </div>
        
        {showingCount !== totalEpisodes && (
          <div className="stat-item">
            <span className="stat-number">{totalEpisodes}</span>
            <span className="stat-label">总节目数</span>
          </div>
        )}
        
        {formattedDuration && (
          <div className="stat-item">
            <span className="stat-number">{formattedDuration}</span>
            <span className="stat-label">总时长</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default PodcastStats;

import React from 'react';
import { formatDate, formatDuration } from '../utils/rssParser';
import './EpisodeCard.css';

const EpisodeCard = ({ episode, onSelect, onShowDetail, isActive }) => {
  const handleClick = () => {
    if (onShowDetail) {
      onShowDetail(episode);
    }
  };

  const handlePlayClick = (e) => {
    e.stopPropagation();
    onSelect(episode);
  };

  // 截取描述文本
  const truncateDescription = (text, maxLength = 200) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div
      className={`episode-card ${isActive ? 'active' : ''}`}
      onClick={handleClick}
    >
      <div className="episode-card-content">
        <div className="episode-header">
          <h3 className="episode-title">{episode.title}</h3>
          <div className="episode-meta">
            <span className="episode-date">
              {formatDate(episode.pubDate)}
            </span>
            {episode.duration && (
              <span className="episode-duration">
                {formatDuration(episode.duration)}
              </span>
            )}
          </div>
        </div>

        <p className="episode-description">
          {truncateDescription(episode.description)}
        </p>

        <div className="episode-actions">
          <button
            className="play-button"
            onClick={handlePlayClick}
            disabled={!episode.audioUrl}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
            播放
          </button>

          {episode.link && (
            <a
              href={episode.link}
              target="_blank"
              rel="noopener noreferrer"
              className="episode-link"
              onClick={(e) => e.stopPropagation()}
            >
              查看原文
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

export default EpisodeCard;

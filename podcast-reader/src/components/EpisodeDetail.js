import React from 'react';
import { formatDate, formatDuration } from '../utils/rssParser';
import './EpisodeDetail.css';

const EpisodeDetail = ({ episode, onClose, onPlay }) => {
  if (!episode) return null;

  const handlePlayClick = () => {
    onPlay(episode);
  };

  return (
    <div className="episode-detail-overlay" onClick={onClose}>
      <div className="episode-detail" onClick={(e) => e.stopPropagation()}>
        <div className="episode-detail-header">
          <h2 className="episode-detail-title">{episode.title}</h2>
          <button className="close-detail-button" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div className="episode-detail-meta">
          <span className="episode-detail-date">
            发布时间: {formatDate(episode.pubDate)}
          </span>
          {episode.duration && (
            <span className="episode-detail-duration">
              时长: {formatDuration(episode.duration)}
            </span>
          )}
        </div>

        <div className="episode-detail-content">
          <div className="episode-detail-description">
            <h3>节目简介</h3>
            <p>{episode.description || '暂无简介'}</p>
          </div>

          <div className="episode-detail-actions">
            <button 
              className="detail-play-button"
              onClick={handlePlayClick}
              disabled={!episode.audioUrl}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
              </svg>
              播放节目
            </button>
            
            {episode.link && (
              <a 
                href={episode.link} 
                target="_blank" 
                rel="noopener noreferrer"
                className="detail-link-button"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
                </svg>
                查看原文
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EpisodeDetail;

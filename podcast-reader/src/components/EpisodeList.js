import React from 'react';
import EpisodeCard from './EpisodeCard';
import './EpisodeList.css';

const EpisodeList = ({ episodes, onEpisodeSelect, onShowDetail, currentEpisode }) => {
  if (!episodes || episodes.length === 0) {
    return (
      <div className="episode-list-empty">
        <p>暂无节目</p>
      </div>
    );
  }

  return (
    <div className="episode-list">
      <h2 className="episode-list-title">节目列表 ({episodes.length})</h2>
      <div className="episode-list-container">
        {episodes.map((episode) => (
          <EpisodeCard
            key={episode.id}
            episode={episode}
            onSelect={onEpisodeSelect}
            onShowDetail={onShowDetail}
            isActive={currentEpisode && currentEpisode.id === episode.id}
          />
        ))}
      </div>
    </div>
  );
};

export default EpisodeList;

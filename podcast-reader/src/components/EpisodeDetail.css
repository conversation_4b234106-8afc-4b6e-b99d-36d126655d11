.episode-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
}

.episode-detail {
  background: white;
  border-radius: 16px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.episode-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e0e0e0;
}

.episode-detail-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
  color: #333;
  line-height: 1.4;
  flex: 1;
  padding-right: 1rem;
}

.close-detail-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  color: #666;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.close-detail-button:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.episode-detail-meta {
  display: flex;
  gap: 2rem;
  padding: 1rem 2rem;
  background: #f8f9fa;
  font-size: 0.9rem;
  color: #666;
}

.episode-detail-date,
.episode-detail-duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.episode-detail-content {
  padding: 2rem;
}

.episode-detail-description h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #333;
}

.episode-detail-description p {
  line-height: 1.8;
  color: #555;
  margin: 0 0 2rem 0;
  white-space: pre-wrap;
}

.episode-detail-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.detail-play-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #667eea;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.detail-play-button:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.detail-play-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.detail-link-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  text-decoration: none;
  padding: 1rem 1.5rem;
  border: 2px solid #667eea;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.detail-link-button:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* 滚动条样式 */
.episode-detail::-webkit-scrollbar {
  width: 6px;
}

.episode-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.episode-detail::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.episode-detail::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@media (max-width: 768px) {
  .episode-detail-overlay {
    padding: 0.5rem;
  }
  
  .episode-detail {
    border-radius: 12px;
    max-height: 95vh;
  }
  
  .episode-detail-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }
  
  .episode-detail-title {
    font-size: 1.3rem;
  }
  
  .episode-detail-meta {
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
  }
  
  .episode-detail-content {
    padding: 1.5rem;
  }
  
  .episode-detail-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .detail-link-button {
    justify-content: center;
  }
}
